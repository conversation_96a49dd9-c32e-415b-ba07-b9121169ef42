<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <link rel="stylesheet" th:href="@{/lib/zTree_v3/css/zTreeStyle/zTreeStyle.css}" type="text/css">
</head>
<body>
<div class="layui-form timo-compile">
    <form th:action="@{/system/menu/save}">
        <input type="hidden" name="id" th:if="${menu}" th:value="${menu.id}"/>
        <div class="layui-form-item">
            <label class="layui-form-label required">标题</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="title"  placeholder="请输入标题" th:value="${menu?.title}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">URL地址</label>
            <div class="layui-input-inline">
                <input class="layui-input url-input" type="text" name="url"  placeholder="请输入URL地址" th:value="${menu?.url}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">权限标识</label>
            <div class="layui-input-inline">
                <input class="layui-input perms-input" type="text" name="perms"  placeholder="请输入权限标识" th:value="${menu?.perms}">
            </div>
            <button class="layui-btn layui-btn-primary layui-btn-xs perms-refresh" style="margin-top: 8px">
                <i class="layui-icon layui-icon-refresh" style="margin-right: 0"></i>
            </button>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">菜单图标</label>
            <div class="layui-input-inline">
                <input class="layui-input icon-input" type="text" name="icon"  placeholder="请输入菜单图标" th:value="${menu?.icon}">
            </div>
            <i th:class="'icon-show '+${menu?.icon}" style="line-height: 38px;"></i>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">父级菜单</label>
            <div class="layui-input-inline">
                <input class="layui-input select-tree" th:attr="data-url=@{/system/menu/list}, data-value=${pMenu?.id}" type="text" name="pid"  placeholder="请输入父级菜单" th:value="${pMenu?.title}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">菜单类型</label>
            <div class="layui-input-inline">
                <select name="type" mo:dict="MENU_TYPE" mo-selected="${menu?.type}" mo-empty="" lay-verify="type"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-inline">
                <select class="select-sort" name="sort"
                        th:attr="data-url=@{/system/menu/sortList}, data-id=${menu?.id}, data-sort=${menu?.sort}" lay-verify="sort"></select>
            </div>
            <div class="layui-input-info">（之后）</div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="remark">[[${menu?.remark}]]</textarea>
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
<script type="text/javascript" th:src="@{/js/plugins/jquery-2.2.4.min.js}"></script>
<script type="text/javascript" th:src="@{/lib/zTree_v3/js/jquery.ztree.core.min.js}"></script>
<script type="text/javascript" th:src="@{/js/timoTree.js}"></script>
<script type="text/javascript">
    layui.use(['form'], function () {
        window.form = layui.form;
        // 初始化排序下拉选项
        var pid = $(".select-tree").data('value');
        if (pid !== undefined){
            sortRender({id: pid});
        }
    });

    // 初始化下拉树
    $.fn.selectTree({
        rootTree: '顶级菜单',
        // 选中后事件
        onSelected: sortRender
    });

    // 更新渲染排序下拉选项
    function sortRender(treeNode) {
        var pid = treeNode.id;
        var sort = $(".select-sort");
        var id = sort.data('id') ? sort.data('id') : 0;
        var url = sort.data('url') + "/" + pid + "/" + id;
        $.get(url, function (result) {
            var options = '';
            var sortNum = Object.keys(result).length;
            if(pid === $(".select-tree").data('value') && sort.data('sort')){
                sortNum = sort.data('sort') - 1;
            }
            result[0] = "首位";
            for(var key in result){
                var selected = sortNum == key ? "selected=''" : "";
                options += "<option value='"+ key +"' " + selected + ">"+ result[key] +"</option>";
            }
            sort.html(options);
            form.render('select');
        });
    }

    // 监听变动图标
    $(".icon-input").on("input propertychange", function(){
        $(".icon-show").attr("class", "icon-show "+$(this).val());
    });

    // 同步操作权限输入框
    var $perms = $(".perms-input").val();
    $(".url-input").on("input propertychange", function(){
        if($perms === ''){
            $(".perms-refresh").click();
        }
    });

    // 更新权限标识
    $(".perms-refresh").on("click", function (e) {
        e.preventDefault();
        var $perms = $(".perms-input");
        var url = $(".url-input").val().substr(1);
        var perms = url.replace(new RegExp( '/' , "g" ), ':');
        $perms.val(perms);
    })

</script>
</body>
</html>