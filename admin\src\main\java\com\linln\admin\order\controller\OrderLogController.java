package com.linln.admin.order.controller;

import com.linln.admin.order.domain.OrderLog;
import com.linln.admin.order.repository.OrderLogRepository;
import com.linln.admin.order.service.OrderLogService;
import com.linln.admin.order.validator.OrderLogValid;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.SpringContextUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import com.linln.component.excel.ExcelUtil;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.Role;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/04/11
 */
@Controller
@RequestMapping("/order/orderLog")
public class OrderLogController {

    @Autowired
    private OrderLogService orderLogService;
    /**
     * 导出数据
     */
    @GetMapping("/export")
    @ResponseBody
    public void exportExcel() {
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        boolean bossFlag = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("boss", x.getRemark()));
        if(!bossFlag){
            return;
        }

        OrderLog orderLog = new OrderLog();
        // 创建匹配器，进行动态查询匹配
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("title", match -> match.contains());

        // 获取数据列表
        Example<OrderLog> example = Example.of(orderLog, matcher);
        OrderLogRepository orderLogRepository = SpringContextUtil.getBean(OrderLogRepository.class);

        List<OrderLog> list = orderLogRepository.findAll(example);
        ExcelUtil.exportExcel(OrderLog.class, list);
    }

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("order:orderLog:index")
    public String index(Model model, OrderLog orderLog) {

        // 创建匹配器，进行动态查询匹配
        ExampleMatcher matcher = ExampleMatcher.matching();

        // 获取数据列表
        Example<OrderLog> example = Example.of(orderLog, matcher);
        Page<OrderLog> list = orderLogService.getPageList(example);

        setAuthFlag(model);
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        return "/order/orderLog/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("order:orderLog:add")
    public String toAdd(Model model) {
        setAuthFlag(model);
        return "/order/orderLog/add";
    }

    private void setAuthFlag(Model model) {
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        boolean externalFlag = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("external", x.getRemark()));
        boolean internalFlag = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("internal", x.getRemark()));
        boolean bossFlag = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("boss", x.getRemark()));

        // 封装数据
        model.addAttribute("externalFlag", externalFlag);
        model.addAttribute("internalFlag", internalFlag);
        model.addAttribute("bossFlag", bossFlag);
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("order:orderLog:edit")
    public String toEdit(@PathVariable("id") OrderLog orderLog, Model model) {
        setAuthFlag(model);
        model.addAttribute("orderLog", orderLog);
        return "/order/orderLog/add";
    }

    /**
     * 保存添加/修改的数据
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"order:orderLog:add", "order:orderLog:edit"})
    @ResponseBody
    public ResultVo save(@Validated OrderLogValid valid, OrderLog orderLog) {
        // 复制保留无需修改的数据
        if (orderLog.getId() != null) {
            OrderLog beOrderLog = orderLogService.getById(orderLog.getId());
            EntityBeanUtil.copyProperties(beOrderLog, orderLog);
            orderLog.setOrderNo(beOrderLog.getOrderNo());

            if(Objects.isNull(orderLog.getMarkup())){
                orderLog.setMarkup(beOrderLog.getMarkup());
            }
            if(Objects.isNull(orderLog.getCustomer())){
                orderLog.setCustomer(beOrderLog.getCustomer());
            }
            if(Objects.isNull(orderLog.getManager())){
                orderLog.setManager(beOrderLog.getManager());
            }
            if(Objects.isNull(orderLog.getGoodsNo())){
                orderLog.setGoodsNo(beOrderLog.getGoodsNo());
            }
            if(Objects.isNull(orderLog.getInternalOrdAmt())){
                orderLog.setInternalOrdAmt(beOrderLog.getInternalOrdAmt());
            }
            if(Objects.isNull(orderLog.getBuyFees())){
                orderLog.setBuyFees(beOrderLog.getBuyFees());
            }
            if(Objects.isNull(orderLog.getInternalFees())){
                orderLog.setInternalFees(beOrderLog.getInternalFees());
            }
            if(Objects.isNull(orderLog.getInternationalFees())){
                orderLog.setInternationalFees(beOrderLog.getInternationalFees());
            }
            if(Objects.isNull(orderLog.getInternalRemark())){
                orderLog.setInternalRemark(beOrderLog.getInternalRemark());
            }
            if(Objects.isNull(orderLog.getUserRuss())){
                orderLog.setUserRuss(beOrderLog.getUserRuss());
            }
            if(Objects.isNull(orderLog.getTaxFees())){
                orderLog.setTaxFees(beOrderLog.getTaxFees());
            }
            if(Objects.isNull(orderLog.getTaxExtraFees())){
                orderLog.setTaxExtraFees(beOrderLog.getTaxExtraFees());
            }
            if(Objects.isNull(orderLog.getCtFees())){
                orderLog.setCtFees(beOrderLog.getCtFees());
            }
            if(Objects.isNull(orderLog.getCtExtrFees())){
                orderLog.setCtExtrFees(beOrderLog.getCtExtrFees());
            }
            if(Objects.isNull(orderLog.getRussclearFees())){
                orderLog.setRussclearFees(beOrderLog.getRussclearFees());
            }
            if(Objects.isNull(orderLog.getCostAmt())){
                orderLog.setCostAmt(beOrderLog.getCostAmt());
            }
            if(Objects.isNull(orderLog.getSalesAmt())){
                orderLog.setSalesAmt(beOrderLog.getSalesAmt());
            }
            if(Objects.isNull(orderLog.getExplaintt())){
                orderLog.setExplaintt(beOrderLog.getExplaintt());
            }
        }else{
            orderLog.setOrderNo(getDate());
        }
        // 保存数据
        orderLogService.save(orderLog);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 获取当前系统日期 yyMMddHHmmss
     *
     * @return Format Date
     */
    public static String getDate() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        Date dateCurrent = new Date();

        String strCurrentDate = formatter.format(dateCurrent);

        return strCurrentDate;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("order:orderLog:detail")
    public String toDetail(@PathVariable("id") OrderLog orderLog, Model model) {
        setAuthFlag(model);
        model.addAttribute("orderLog",orderLog);
        return "/order/orderLog/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("order:orderLog:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (orderLogService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}