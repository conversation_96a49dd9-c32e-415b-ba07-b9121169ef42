package com.linln.admin.foreignOrder.service;

import com.linln.admin.express.domain.Exinfo;
import com.linln.admin.foreignOrder.domain.ForeignOrder;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
public interface ForeignOrderService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<ForeignOrder> getPageList(Example<ForeignOrder> example);

    Specification<ForeignOrder> buildSpecByExample(Example<ForeignOrder> example, Date ruPaymentStartDate, Date ruPaymentEndDate);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    ForeignOrder getById(Long id);

    /**
     * 保存数据
     * @param foreignOrder 实体对象
     */
    ForeignOrder save(ForeignOrder foreignOrder);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);

    public Page<ForeignOrder> getPageListBySpec(Specification<ForeignOrder> spec);
}