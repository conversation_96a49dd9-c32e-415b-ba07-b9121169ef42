<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

</html>

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .layui-form-select .layui-input {
            height: 38px;
        }
    </style>
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-bars"></i> 采购管理</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        </div>
        <div class="layui-card-body">
            <form class="layui-form timo-search-box" action="" id="searchForm">
                <div class="layui-form-pane">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">单据号 код-данных</label>
                                <div class="layui-input-block">
                                    <input type="text" name="orderNo" th:value="${param.orderNo}" placeholder="请输入单据号"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">合同编号 номер конт.(прод.)</label>
                                <div class="layui-input-block">
                                    <input type="text" name="contractNo" th:value="${param.contractNo}"
                                        placeholder="请输入合同编号" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">创建时间 дата создания</label>
                                <div class="layui-input-block" id="createDate">
                                    <input type="text" autocomplete="off" id="createDateStr" class="layui-input"
                                        name="createDateStr" placeholder="开始-结束" th:value="${param.createDateStr}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">收款方 тип оплата</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" name="payeeType" mo:dict="PAYEE_TYPE"
                                        mo-selected="${param.payeeType}" mo-empty="全部"></select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">负责人(中) рук-ль (КНР)</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" type="text" id="cnCustomerSearch"
                                        name="cnCustomer.contName" th:value="${contract.cnCustomer?.contName}"
                                        placeholder="请输入中方负责人" autocomplete="off">
                                    <input type="hidden" name="cnCustomer.id" th:value="${contract.cnCustomer?.id}"
                                        id="cnCustomerId">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">负责人(俄) рук-ль (РФ)</label>
                                <div class="layui-input-block">
                                    <input class="layui-input" type="text" id="ruCustomerSearch"
                                        name="ruCustomer.contName" th:value="${contract.ruCustomer?.contName}"
                                        placeholder="请输入俄方负责人" autocomplete="off">
                                    <input type="hidden" name="ruCustomer.id" th:value="${contract.ruCustomer?.id}"
                                        id="ruCustomerId">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3">
                            <div class="layui-form-item">
                                <label class="layui-form-label">付款日期 ДОЗ</label>
                                <div class="layui-input-block" id="paymentDueDate">
                                    <input type="text" autocomplete="off" id="paymentDueDateStr" class="layui-input"
                                        name="paymentDueDateStr" placeholder="开始-结束"
                                        th:value="${param.paymentDueDateStr}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="text-align: center;">
                            <button type="button" class="layui-btn timo-search-btn">
                                <i class="fa fa-search"></i> 搜索 Поиск
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                <i class="fa fa-refresh"></i> 重置 Сбросить
                            </button>
                            <button type="button" class="layui-btn open-popup" data-title="添加采购合同"
                                th:attr="data-url=@{/psm/contract/add}" data-size="auto">
                                <i class="fa fa-plus"></i> 添加 Добавить
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            <!-- 统计面板 -->
            <div class="layui-card" style="margin-top: 10px;">
                <div class="layui-card-header">
                    <span><i class="fa fa-bar-chart"></i> 数据统计 Статистика данных</span>
                </div>
                <div class="layui-card-body">
                    <div class="layui-row layui-col-space15">
                        <div class="layui-col-md2" style="text-align: center;">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <h3 style="color: #01AAED; margin: 0;">$<span id="totalReceivable">0.00</span></h3>
                                    <p style="margin: 5px 0 0 0; font-size: 12px;">合计收款<br>Общая сумма</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2" style="text-align: center;">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <h3 style="color: #2F4056; margin: 0;">$<span id="productPrice">0.00</span></h3>
                                    <p style="margin: 5px 0 0 0; font-size: 12px;">合计售价<br>Общая цена</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md2" style="text-align: center;">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <h3 style="color: #FF5722; margin: 0;">$<span id="purchasePrice">0.00</span></h3>
                                    <p style="margin: 5px 0 0 0; font-size: 12px;">合计采购价<br>Общая закуп.цена</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="text-align: center;">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <h3 style="color: #FF6600; margin: 0;">$<span id="cnTotalFee">0.00</span></h3>
                                    <p style="margin: 5px 0 0 0; font-size: 12px;">中国总费用<br>Общие расходы КНР</p>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md3" style="text-align: center;">
                            <div class="layui-card">
                                <div class="layui-card-body">
                                    <h3 style="color: #1E9FFF; margin: 0;">$<span id="ruTotalFee">0.00</span></h3>
                                    <p style="margin: 5px 0 0 0; font-size: 12px;">俄罗斯总费用<br>Общие расходы РФ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="timo-table-wrap">
                <table class="layui-table timo-table">
                    <thead>
                        <tr>
                            <th class="timo-table-checkbox">
                                <label class="timo-checkbox"><input type="checkbox">
                                    <i class="layui-icon layui-icon-ok"></i></label>
                            </th>
                            <th>单据号 код-данных</th>
                            <th>创建时间 дата создания</th>
                            <th>单据状态 статус</th>
                            <th>销售合同编号 номер конт.(прод.)</th>
                            <th>总应收款 сумма</th>
                            <th>产品售价 Пр.Ц</th>
                            <th>收款方 тип оплата</th>
                            <th>项目负责人（俄方） рук-ль (РФ)</th>
                            <th>收款日期 дата оплата</th>
                            <th>外贸合同号 номер ВТК</th>
                            <th>采购价 закуп.цена</th>
                            <th>采购付款日期 ДОЗ</th>
                            <th>项目负责人（中方） рук-ль (КНР)</th>
                            <th>运输信息 инфо о логистике</th>
                            <th>货物信息 инфо о грузе</th>
                            <th>业务员</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item:${list}">
                            <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                                    <i class="layui-icon layui-icon-ok"></i></label></td>
                            <td th:text="${item.orderNo}">单据号</td>
                            <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                            <td th:text="${#dicts.keyValue('CONTRACT_STATUS',item.contractStatus)}">单据状态</td>
                            <td th:text="${item.contractNo}">销售合同编号</td>
                            <td th:text="'$' + ${item.totalReceivable}">总应收款</td>
                            <td th:text="'$' + ${item.productPrice}">产品售价</td>
                            <td th:text="${#dicts.keyValue('PAYEE_TYPE',item.payeeType)}">收款方</td>
                            <td th:text="${item.ruCustomer?.contName}">项目负责人（俄方）</td>
                            <td th:text="${#dates.format(item.collectionDate, 'yyyy-MM-dd')}">收款日期</td>
                            <td th:text="${item.foreignContractNo}">外贸合同号</td>
                            <td th:text="'$' + ${item.purchasePrice}">采购价</td>
                            <td th:text="${#dates.format(item.paymentDueDate, 'yyyy-MM-dd')}">采购付款日期</td>
                            <td th:text="${item.cnCustomer?.contName}">项目负责人（中方）</td>
                            <td>
                                <a th:if="${item.transport}" class="open-popup" data-title="物流详情"
                                    th:attr="data-url=@{'/psm/transport/detail/'+${item.transport.id}}" href="#"
                                    th:text="运输信息"></a>
                            </td>
                            <td>
                                <a th:if="${item.goodsInfo}" class="open-popup" data-title="货物详情"
                                    th:attr="data-url=@{'/psm/goodsInfo/detail/'+${item.goodsInfo.id}}" href="#"
                                    th:text="货物信息"></a>
                            </td>
                            <td th:text="${item.salesMan?.nickname}">业务员</td>
                            <td>
                                <a class="open-popup" data-title="编辑采购合同"
                                    th:attr="data-url=@{'/psm/contract/edit/'+${item.id}}" data-size="auto"
                                    href="#">编辑</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div th:replace="/common/fragment :: page"></div>
        </div>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.config({
            base: '/js/'
        }).extend({
            autocomplete: 'autocomplete'
        });
        layui.use(['laydate', 'autocomplete', 'form'], function () {
            var laydate = layui.laydate;
            var autocomplete = layui.autocomplete;
            var form = layui.form;

            $('#resetBtn').on('click', function () {
                $("#searchForm input").val("");
                $("#searchForm select").val("");
                $(".timo-search-btn").click();
            });

            laydate.render({
                elem: '#createDateStr',
                range: true,
                rangeLinked: true
            });

            laydate.render({
                elem: '#paymentDueDateStr',
                range: true,
                rangeLinked: true
            });

            // 初始化俄方负责人autocomplete
            autocomplete.render({
                elem: '#ruCustomerSearch',
                hidden: '#ruCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected ru customer:', data);
                }
            });

            // 初始化中方负责人autocomplete
            autocomplete.render({
                elem: '#cnCustomerSearch',
                hidden: '#cnCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected cn customer:', data);
                }
            });

            // 加载统计数据
            function loadStatistics() {
                var formData = $("#searchForm").serialize();
                $.ajax({
                    url: '/psm/contract/statistics',
                    type: 'GET',
                    data: formData,
                    success: function(result) {
                        if (result.code === 200) {
                            var data = result.data;
                            $('#totalReceivable').text(parseFloat(data.totalReceivable || 0).toFixed(2));
                            $('#productPrice').text(parseFloat(data.productPrice || 0).toFixed(2));
                            $('#purchasePrice').text(parseFloat(data.purchasePrice || 0).toFixed(2));
                            $('#cnTotalFee').text(parseFloat(data.cnTotalFee || 0).toFixed(2));
                            $('#ruTotalFee').text(parseFloat(data.ruTotalFee || 0).toFixed(2));
                        }
                    },
                    error: function() {
                        console.error('加载统计数据失败');
                    }
                });
            }

            // 页面加载完成后加载统计数据
            loadStatistics();

            // 搜索时重新加载统计数据
            $('.timo-search-btn').on('click', function() {
                setTimeout(function() {
                    loadStatistics();
                }, 500);
            });

            // 重置时重新加载统计数据
            $('#resetBtn').on('click', function() {
                setTimeout(function() {
                    loadStatistics();
                }, 100);
            });

        });
    </script>
</body>

</html>
