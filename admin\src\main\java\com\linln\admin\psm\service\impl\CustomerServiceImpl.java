package com.linln.admin.psm.service.impl;

import com.linln.admin.psm.domain.Customer;
import com.linln.admin.psm.repository.CustomerRepository;
import com.linln.admin.psm.service.CustomerService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Service
public class CustomerServiceImpl implements CustomerService {

    @Autowired
    private CustomerRepository customerRepository;

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    @Override
    public Customer getById(Long id) {
        return customerRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Customer> getPageList(Example<Customer> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return customerRepository.findAll(example, page);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @param pageRequest 分页参数
     * @return 返回分页数据
     */
    @Override
    public Page<Customer> getPageList(Example<Customer> example, PageRequest pageRequest) {
        return customerRepository.findAll(example, pageRequest);
    }

    /**
     * 获取分页列表数据
     * @param spec 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Customer> getPageList(Specification<Customer> spec) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return customerRepository.findAll(spec, page);
    }

    /**
     * 保存数据
     * @param customer 实体对象
     */
    @Override
    public Customer save(Customer customer) {
        return customerRepository.save(customer);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return customerRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }
    
    /**
     * 根据关键字搜索客户（OR连接条件）
     * @param keyword 搜索关键字
     * @param pageRequest 分页参数
     * @return 客户列表
     */
    @Override
    public List<Customer> searchByKeyword(String keyword, PageRequest pageRequest) {
        return customerRepository.searchByKeyword(keyword, pageRequest).getContent();
    }
}
