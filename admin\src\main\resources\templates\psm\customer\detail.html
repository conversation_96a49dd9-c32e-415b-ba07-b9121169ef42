<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="timo-detail-page">
        <div class="timo-detail-title">基本信息 Основная информация</div>
        <table class="layui-table timo-detail-table">
            <colgroup>
                <col width="100px"><col>
                <col width="100px"><col>
            </colgroup>
            <tbody>
                <tr>
                    <th>主键ID ID (первичный ключ)</th>
                    <td th:text="${customer.id}"></td>
                    <th>客户信息 Информация о клиентах</th>
                    <td th:text="${customer.custOrg}"></td>
                </tr>
                <tr>
                    <th>业务信息 Деловые данные</th>
                    <td th:text="${customer.custDetail}"></td>
                    <th>联系人 Контакт</th>
                    <td th:text="${customer.contName}"></td>
                </tr>
                <tr>
                    <th>客户所在国家 Страна клиента</th>
                    <td th:text="${#dicts.keyValue('CUST_LOCAL',customer.custCountry)}"></td>
                    <th>客户类型 Тип клиента</th>
                    <td th:text="${#dicts.keyValue('CUST_TYPE',customer.custType)}"></td>
                </tr>
                <tr>
                    <th>联系电话 Контактный телефон</th>
                    <td th:text="${customer.contPhone}" colspan="3"></td>
                </tr>
                <tr>
                    <th>创建者 Создатель</th>
                    <td th:text="${customer.createBy?.nickname}"></td>
                    <th>更新者 Обновивший</th>
                    <td th:text="${customer.updateBy?.nickname}"></td>
                </tr>
                <tr>
                    <th>创建时间 дата создания</th>
                    <td th:text="${#dates.format(customer.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <th>更新时间 Время обновления</th>
                    <td th:text="${#dates.format(customer.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </tr>
            </tbody>
        </table>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
