package com.linln.admin.psm.validator;

import lombok.Data;

import java.io.Serializable;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class CustomerValid implements Serializable {
    @NotEmpty(message = "客户信息不能为空")
    private String custOrg;
    @NotEmpty(message = "业务信息不能为空")
    private String custDetail;
    @NotEmpty(message = "联系人不能为空")
    private String contName;
    @NotEmpty(message = "客户类型不能为空")
    private String custType;
    @Pattern(regexp = "^((17[0-9])|(14[0-9])|(13[0-9])|(15[^4,\\D])|(18[0,5-9]))\\d{8}$", message = "手机号码格式不正确")
    private String contPhone;
}