<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">treeNode.</span>url</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>The URL of node link</p>
			<p class="highlight_red">1. In edit mode (setting.edit.enable = true) , this feature fails. If you must use a similar feature, please use the 'onClick' callback for their own control.</p>
			<p class="highlight_red">2. If you use  the 'onClick' callback function to control opening URL , then set the URL  in the other custom attribute, do not use the 'url' attribute.</p>
			<p>Default: undefined</p>
		</div>
	</div>
	<h3>String Format</h3>
	<div class="desc">
	<p>As same as &lt;a&gt; tag's 'href' attribute.</p>
	</div>
	<h3>Examples of treeNode</h3>
	<h4>1. Set the URL is 'g.cn'</h4>
	<pre xmlns=""><code>var nodes = [
	{ "id":1, "name":"Google CN", "url":"http://g.cn"},
	......
]</code></pre>
</div>
</div>