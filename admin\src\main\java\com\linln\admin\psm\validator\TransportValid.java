package com.linln.admin.psm.validator;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.Digits;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class TransportValid implements Serializable {
    @Digits(integer = 12, fraction = 2, message = "预付款不是数字")
    private BigDecimal prepaymentAmount;
    @Digits(integer = 12, fraction = 2, message = "尾款不是数字")
    private BigDecimal balancePaymentAmount;
    @Digits(integer = 12, fraction = 2, message = "应收款不是数字")
    private BigDecimal receivableAmount;
    @Digits(integer = 12, fraction = 2, message = "实收款不是数字")
    private BigDecimal payeeTypeAmount;
    @Digits(integer = 12, fraction = 2, message = "成本金额不是数字")
    private BigDecimal purAmountCost;
}