<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.data.key.</span>isHidden</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exhide</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>zTree 节点数据保存节点是否隐藏的属性名称。</p>
			<p>默认值："isHidden"</p>
			<p class="highlight_red">v3.5.32+</p>
		</div>
	</div>
	<h3>setting 举例</h3>
	<h4>1. 设置 zTree 显示节点时，将 treeNode 的 hidden 属性当做节点是否隐藏的属性名称</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		key: {
			isHidden: "hidden"
		}
	}
};
......</code></pre>
</div>
</div>