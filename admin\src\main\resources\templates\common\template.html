<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:fragment="header(title, link, style)">
    <th:block th:if="${title == null}">
        <title>后台</title>
    </th:block>
    <th:block th:if="${title != null}">
        <title th:replace="${title}">title</title>
    </th:block>

    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="description" content="后台管理系统" />
    <meta name="keywords" content="后台管理系统, SpringBoot, Jpa, Thymeleaf, Shiro" />

    <link rel="shortcut icon" th:href="@{/mo-favicon.ico}" type="image/x-icon">
    <link rel="stylesheet" th:href="@{/css/plugins/font-awesome-4.7.0/css/font-awesome.min.css}"  media="all">
<!--    <link rel="stylesheet" th:href="@{/lib/layui-v2.3.0/css/layui.css}"  media="all">-->
    <link rel="stylesheet" th:href="@{/lib/layui-v2.11.5/layui/css/layui.css}"  media="all">
    <link rel="stylesheet" th:href="@{/css/main.css}"  media="all">
    <th:block th:if="${link != null}">
        <th:block th:replace="${link}"></th:block>
    </th:block>

    <th:block th:if="${style != null}">
        <th:block th:replace="${style}"></th:block>
    </th:block>
</head>
<body>
    <th:block th:fragment="script">
        <script th:src="@{/js/plugins/jquery-3.3.1.min.js}"></script>
<!--        <script th:src="@{/lib/layui-v2.3.0/layui.js}" charset="utf-8"></script>-->
        <script th:src="@{/lib/layui-v2.11.5/layui/layui.js}" charset="utf-8"></script>
        <script th:src="@{/js/main.js}" charset="utf-8"></script>
    </th:block>
    <script th:inline="javascript">
    window.rootPath = [[@{/}]];
    window.token = localStorage.getItem("token");
    </script>
<th:block th:replace="${script}"></th:block>
<script>
</script>
</body>
</html>