package com.linln.admin.psm.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.linln.admin.psm.domain.Contract;
import com.linln.admin.psm.service.ContractService;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.vo.ResultVo;
import com.linln.component.excel.ExcelUtil;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 数据中心控制器 - 统一数据导出管理
 * <AUTHOR>
 * @since 2025/01/17
 */
@Slf4j
@Controller
@RequestMapping("/psm/datacenter")
public class DataCenterController {

    @Autowired
    private ContractService contractService;

    /**
     * 数据中心首页 - 导出功能选择
     */
    @GetMapping("/index")
    @RequiresPermissions("psm:datacenter:index")
    public String index() {
        log.info("进入数据中心首页");
        return "/psm/datacenter/index";
    }

    /**
     * 跳转到合同数据导出页面
     */
    @GetMapping("/contract")
    @RequiresPermissions("psm:datacenter:contract")
    public String contractExport(Model model, Contract contract,
                               @RequestParam(value = "createDateStr", required = false) String createDateStr,
                               @RequestParam(value = "paymentDueDateStr", required = false) String paymentDueDateStr) {
        log.info("进入合同数据导出页面");
        
        // 将查询条件传回前端用于回显
        model.addAttribute("contract", contract);
        model.addAttribute("createDateStr", createDateStr);
        model.addAttribute("paymentDueDateStr", paymentDueDateStr);
        return "/psm/datacenter/contract";
    }

    /**
     * 获取合同导出预览统计数据
     */
    @GetMapping("/contract/preview")
    @RequiresPermissions("psm:datacenter:contract")
    @ResponseBody
    public ResultVo contractPreview(Contract contract,
                                  @RequestParam(value = "createDateStr", required = false) String createDateStr,
                                  @RequestParam(value = "paymentDueDateStr", required = false) String paymentDueDateStr) {
        // 构建查询条件
        Specification<Contract> spec = buildContractSpecification(contract, createDateStr, paymentDueDateStr);
        
        // 获取统计数据
        Map<String, Object> statistics = contractService.getStatistics(spec);
        
        // 获取总记录数
        List<Contract> contracts = contractService.getListForExport(spec);
        statistics.put("totalCount", contracts.size());
        
        return ResultVoUtil.success(statistics);
    }

    /**
     * 执行合同数据导出
     */
    @PostMapping("/contract/export")
    @RequiresPermissions("psm:datacenter:contract")
    @ResponseBody
    public void executeContractExport(Contract contract,
                                    @RequestParam(value = "createDateStr", required = false) String createDateStr,
                                    @RequestParam(value = "paymentDueDateStr", required = false) String paymentDueDateStr,
                                    @RequestParam(value = "exportFormat", defaultValue = "excel") String exportFormat) {
        log.info("执行合同数据导出, 格式: {}", exportFormat);
        
        // 构建查询条件
        Specification<Contract> spec = buildContractSpecification(contract, createDateStr, paymentDueDateStr);
        
        // 获取导出数据
        List<Contract> exportList = contractService.getListForExport(spec);
        
        // 执行导出
        switch (exportFormat.toLowerCase()) {
            case "excel":
            default:
                ExcelUtil.exportExcel(Contract.class, exportList);
                break;
        }
    }

    /**
     * 构建合同查询条件规范
     */
    private Specification<Contract> buildContractSpecification(Contract contract, String createDateStr, String paymentDueDateStr) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 非老板角色只能查看自己的数据
            if (!ShiroUtil.verifyRole("boss")) {
                User currUser = ShiroUtil.getSubject();
                predicates.add(cb.equal(root.get("salesMan"), currUser));
            }

            // 模糊查询：单据号
            if (StrUtil.isNotBlank(contract.getOrderNo())) {
                predicates.add(cb.like(root.get("orderNo"), "%" + contract.getOrderNo() + "%"));
            }

            // 模糊查询：合同编号
            if (StrUtil.isNotBlank(contract.getContractNo())) {
                predicates.add(cb.like(root.get("contractNo"), "%" + contract.getContractNo() + "%"));
            }

            // 其他查询条件
            if (contract.getPayeeType() != null) {
                predicates.add(cb.equal(root.get("payeeType"), contract.getPayeeType()));
            }

            // 负责人（俄）模糊查询
            if (contract.getRuCustomer() != null && StrUtil.isNotBlank(contract.getRuCustomer().getContName())) {
                predicates.add(cb.like(root.join("ruCustomer").get("contName"), "%" + contract.getRuCustomer().getContName() + "%"));
            }

            // 负责人（中）模糊查询
            if (contract.getCnCustomer() != null && StrUtil.isNotBlank(contract.getCnCustomer().getContName())) {
                predicates.add(cb.like(root.join("cnCustomer").get("contName"), "%" + contract.getCnCustomer().getContName() + "%"));
            }

            // 日期范围查询：创建时间
            if (StrUtil.isNotBlank(createDateStr)) {
                String[] createDateArr = createDateStr.split(" - ");
                Date startDate = DateUtil.parse(createDateArr[0]);
                Date endDate = DateUtil.parse(createDateArr[1]);
                predicates.add(cb.between(root.get("createDate"), startDate, DateUtil.endOfDay(endDate)));
            }

            // 日期范围查询：采购付款日期
            if (StrUtil.isNotBlank(paymentDueDateStr)) {
                String[] paymentDueDateArr = paymentDueDateStr.split(" - ");
                Date startDate = DateUtil.parse(paymentDueDateArr[0]);
                Date endDate = DateUtil.parse(paymentDueDateArr[1]);
                predicates.add(cb.between(root.get("paymentDueDate"), startDate, endDate));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
