<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .layui-laydate {
            z-index: 19999999 !important;
        }
    </style>
</head>
<body>
<div class="layui-form timo-compile">
    <form th:action="@{/express/exinfo/save}">
        <input type="hidden" name="id" th:if="${exinfo}" th:value="${exinfo.id}">
       <div class="layui-form-item">
            <label class="layui-form-label">进度 статус</label>
            <div class="layui-input-inline">
               <select name="progress" lay-verify="">
                    <option value="">请选择进度</option>
                     <option value="建立订单черновик" th:selected="${exinfo?.progress == '建立订单черновик'}">建立订单черновик</option>
                    <option value="已付款оплачен" th:selected="${exinfo?.progress == '已付款оплачен'}">已付款оплачен</option>
                    <option value="运输中 перевозка" th:selected="${exinfo?.progress == '运输中 перевозка'}">运输中 перевозка</option>
                    <option value="完成выполнено" th:selected="${exinfo?.progress == '完成выполнено'}">完成выполнено</option>
                </select>
            </div>
        </div>
		<div class="layui-form-item">
            <label class="layui-form-label">发货人<br />отп-ль</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="sender" placeholder="请输入发货人" th:value="${exinfo?.sender}">
            </div>
        </div>
      <div class="layui-form-item">
            <label class="layui-form-label required">货物编码 код</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="cargoCode" placeholder="请输入货物编码 код" th:value="${exinfo?.cargoCode}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">运单号码<br />№AWB/auto</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="expressNo" placeholder="请输入运单号码" th:value="${exinfo?.expressNo}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货物名称<br />товар</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="cargoName" placeholder="请输入货物名称" th:value="${exinfo?.cargoName}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货物重量 KG</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="cargoWeight" placeholder="请输入货物重量 KG" th:value="${exinfo?.cargoWeight}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货物体积 M3</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="cargoVolume" placeholder="请输入货物体积 M3" th:value="${exinfo?.cargoVolume}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">箱数 <br />к-о место</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="cartonNumber" placeholder="请输入箱数" th:value="${exinfo?.cartonNumber}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">到库日期<br />дата получ.КНР</label>
            <div class="layui-input-inline">
                <input id="reachStoreDate" class="layui-input" type="text" name="reachStoreDate" placeholder="请输入到库日期" th:value="${exinfo?.reachStoreDate}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">启运日期<br />дата отп</label>
            <div class="layui-input-inline">
                <input id="startDate" class="layui-input" type="text" name="startDate" placeholder="请输入启运日期" th:value="${exinfo?.startDate}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">运输方式<br />вид транс</label>
            <div class="layui-input-inline">
                <select name="transType" lay-verify="">
                    <option value="">请选择运输方式</option>
                    <option value="陆运超特快" th:selected="${exinfo?.transType == '陆运超特快'}">陆运超特快</option>
                    <option value="陆运特快" th:selected="${exinfo?.transType == '陆运特快'}">陆运特快</option>
                    <option value="陆运普快" th:selected="${exinfo?.transType == '陆运普快'}">陆运普快</option>
                    <option value="铁路运输" th:selected="${exinfo?.transType == '铁路运输'}">铁路运输</option>
                    <option value="空运" th:selected="${exinfo?.transType == '空运'}">空运</option>
                    <option value="海运" th:selected="${exinfo?.transType == '海运'}">海运</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">到货日期<br />дата Приб.РФ</label>
            <div class="layui-input-inline">
                <input id="arrivalDate" class="layui-input" type="text" name="arrivalDate" placeholder="请输入到货日期" th:value="${exinfo?.arrivalDate}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">应收款$счет</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="number" step="0.01" name="recvAmount" placeholder="请输入总应收款" th:value="${exinfo?.recvAmount}">
            </div>
        </div>
         <div class="layui-form-item">
            <label class="layui-form-label">国外收货人<br />получатель</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="foreignRecv" placeholder="请输入国外收货人" th:value="${exinfo?.foreignRecv}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系电话<br />телефон</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="tel" placeholder="请输入联系电话телефон" th:value="${exinfo?.tel}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">中国仓储<br />ПРР Китай</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="interSundryfees" placeholder="请输入中国仓储费用" th:value="${exinfo?.interSundryfees}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">中国运费<br />дост по Китаю</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="interShippingFee" placeholder="请输入中国运输费用" th:value="${exinfo?.interShippingFee}">
            </div>
        </div>
 <div class="layui-form-item">
            <label class="layui-form-label">国内备注 <br />Комент Китай</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="outerRemark" placeholder="请输入国内备注 Комент Китай" th:value="${exinfo?.outerRemark}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">俄罗斯转运费<br />ПРР РФ</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="outerAmount" placeholder="请输入俄罗斯运费" th:value="${exinfo?.outerAmount}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">俄罗斯清关费<br />расх-Тамож</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="outerClearAmount" placeholder="请输入俄罗斯清关费" th:value="${exinfo?.outerClearAmount}">
            </div>
        </div>
       <div class="layui-form-item">
            <label class="layui-form-label">业务员<br />менеджер</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="oper" placeholder="请输入业务员" th:value="${exinfo?.oper}">
            </div>
        </div>
		  <div class="layui-form-item">
            <label class="layui-form-label">收款日期<br />дата оплата</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" id="paymentDate" name="paymentDate" placeholder="请选择收款日期" th:value="${exinfo?.paymentDate}">
            </div>
        </div>
      <div class="layui-form-item">
            <label class="layui-form-label">收款类型<br />тип оплата</label>
            <div class="layui-input-inline">
                <select name="paymentType" class="select-payment">
                            <option value="">请选择收款类型</option>
                            <option value="DOMESTIC_COMPANY" th:selected="${param.paymentType == 'DOMESTIC_COMPANY'}">中国公司 Китай Юр</option>
                            <option value="DOMESTIC_PERSONAL" th:selected="${param.paymentType == 'DOMESTIC_PERSONAL'}">国内现金 нал Китай</option>
                            <option value="INTERNATIONAL_COMPANY" th:selected="${param.paymentType == 'INTERNATIONAL_COMPANY'}">俄罗斯公司 юр РФ</option>
                            <option value="INTERNATIONAL_PERSONAL" th:selected="${param.paymentType == 'INTERNATIONAL_PERSONAL'}">俄罗斯现金 нал РФ</option>
                        </select>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">付款备注<br /> комент оплаты</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入付款备注комент оплаты" class="layui-textarea" name="remark">[[${exinfo?.remark}]]</textarea>
            </div>
        </div>

        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>

<script th:replace="/common/template :: script"></script>
<script>
    layui.use('laydate', function(){
        var laydate = layui.laydate;

        function initDatePicker(elemId, label) {
            laydate.render({
                elem: '#' + elemId,
                format: 'yyyy-MM-dd',
                position: 'fixed',
                zIndex: 19999999,
                trigger: 'click',
                done: function(value){
                    console.log(label + '日期: ' + value);
                }
            });
        }

        ['paymentDate', 'reachStoreDate', 'startDate', 'arrivalDate'].forEach(function(fieldId){
            initDatePicker(fieldId, fieldId);
        });
    });
</script>
</body>
</html>
