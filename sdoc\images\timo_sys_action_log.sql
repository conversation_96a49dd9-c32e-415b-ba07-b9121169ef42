INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (1, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2019-10-31 15:20:29', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (2, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2019-10-31 17:51:54', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (3, '用户管理', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 1, '更新用户成功：admin', '2019-10-31 18:04:23', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (4, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[admin]用户名或密码错误', '2019-11-01 15:36:05', 'admin', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (5, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[admin]用户名或密码错误', '2019-11-01 15:36:16', 'admin', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (6, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2019-11-01 15:36:37', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (7, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2019-11-06 19:51:41', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (8, '字典管理', 1, '127.0.0.1', 'com.linln.admin.system.controller.DictController', 'save', 'sys_dict', 4, '更新字典：菜单类型', '2019-11-06 20:08:46', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (9, '用户密码', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'editPassword', 'sys_user', 1, '修改用户密码成功admin', '2019-11-06 20:09:17', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (10, '用户密码', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'editPassword', 'sys_user', 2, '修改用户密码成功linln', '2019-11-06 20:09:17', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (216, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 14:18:45', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (217, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 15:03:10', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (218, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 15:07:09', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (219, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[admin]用户名或密码错误', '2021-10-25 15:13:35', 'admin', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (220, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 15:13:39', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (221, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 15:59:44', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (222, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 16:12:19', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (223, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 16:37:03', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (224, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 16:52:49', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (225, '系统异常', 3, '0:0:0:0:0:0:0:1', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NullPointerException
	com.linln.admin.system.controller.MainController.lambda$main$3(MainController.java:82)
	java.util.HashMap.forEach(HashMap.java:1288)
	com.linln.admin.system.controller.MainController.main(MainController.java:81)
	com.linln.admin.system.controller.MainController$$FastClassBySpringCGLIB$$bc0e5c6f.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.system.controller.MainController$$EnhancerBySpringCGLIB$$63953bd3.main(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:745)', '2021-10-25 16:52:51', '系统', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (226, '菜单管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.MenuController', 'save', 'sys_menu', 157, '添加菜单：客户资金管理', '2021-10-25 16:57:08', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (227, '菜单管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.MenuController', 'save', 'sys_menu', 158, '添加菜单：详情', '2021-10-25 16:58:37', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (228, '菜单管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.MenuController', 'save', 'sys_menu', 147, '更新菜单：资金管理', '2021-10-25 17:00:13', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (229, '角色状态', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'status', null, null, '删除ID：[2]', '2021-10-25 17:01:57', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (230, '角色状态', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'status', null, null, '删除ID：[3]', '2021-10-25 17:02:03', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (231, '日志管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'save', 'sys_role', 4, '添加日志成功：平台管理员', '2021-10-25 17:02:22', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (232, '日志管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'save', 'sys_role', 5, '添加日志成功：客户', '2021-10-25 17:04:22', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (233, '角色授权', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'auth', 'sys_role', 4, '角色授权成功：平台管理员', '2021-10-25 17:04:50', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (234, '角色授权', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'auth', 'sys_role', 5, '角色授权成功：客户', '2021-10-25 17:05:03', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (235, '用户状态', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[2]', '2021-10-25 17:05:20', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (236, '用户管理', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 3, '添加用户成功：platformlsz', '2021-10-25 17:14:57', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (237, '角色分配', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 3, '角色分配成功：platformlsz', '2021-10-25 17:15:07', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (238, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[admin]用户名或密码错误', '2021-10-25 17:16:09', 'admin', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (239, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:16:12', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (240, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:16:37', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (241, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:17:07', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (242, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:17:34', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (243, '角色授权', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'auth', 'sys_role', 4, '角色授权成功：平台管理员', '2021-10-25 17:18:05', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (244, '角色授权', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.RoleController', 'auth', 'sys_role', 5, '角色授权成功：客户', '2021-10-25 17:18:14', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (245, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:18:28', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (246, '用户登录', 2, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:18:49', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (247, '角色分配', 1, '0:0:0:0:0:0:0:1', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 3, '角色分配成功：platformlsz', '2021-10-25 17:19:04', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (248, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:19:58', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (249, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:35:35', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (250, '用户管理', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 4, '添加用户成功：customer001', '2021-10-25 17:39:59', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (251, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[customer001]您不是后台管理员！', '2021-10-25 17:46:01', 'customer001', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (252, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[customer001]您不是后台管理员！', '2021-10-25 17:46:12', 'customer001', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (253, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:46:16', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (254, '角色分配', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 4, '角色分配成功：customer001', '2021-10-25 17:47:48', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (255, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:48:13', '客户A', 4);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (256, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 17:54:19', '客户A', 4);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (257, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 19:38:47', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (258, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 19:53:05', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (259, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:10:09', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (260, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:21:09', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (261, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:40:14', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (262, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:46:23', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (263, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:47:56', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (264, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:50:28', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (265, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:52:12', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (266, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:55:10', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (267, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 20:57:14', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (268, '系统异常', 3, '127.0.0.1', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'org.springframework.transaction.CannotCreateTransactionException: Could not open JPA EntityManager for transaction; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection
	org.springframework.orm.jpa.JpaTransactionManager.doBegin(JpaTransactionManager.java:446)
	org.springframework.transaction.support.AbstractPlatformTransactionManager.getTransaction(AbstractPlatformTransactionManager.java:378)
	org.springframework.transaction.interceptor.TransactionAspectSupport.createTransactionIfNecessary(TransactionAspectSupport.java:474)
	org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:289)
	org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:98)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.modules.system.service.impl.MenuServiceImpl$$EnhancerBySpringCGLIB$$8e9199b.getById(<generated>)
	com.linln.devtools.generate.GenerateController.genMenuRule(GenerateController.java:104)
	com.linln.devtools.generate.GenerateController.save(GenerateController.java:89)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:497)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:745)', '2021-10-25 21:04:52', '系统', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (269, '菜单管理', 1, '127.0.0.1', 'com.linln.admin.system.controller.MenuController', 'save', 'sys_menu', 159, '更新菜单：余额调整', '2021-10-25 21:07:17', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (270, '角色授权', 1, '127.0.0.1', 'com.linln.admin.system.controller.RoleController', 'auth', 'sys_role', 4, '角色授权成功：平台管理员', '2021-10-25 21:07:53', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (271, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:09:23', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (272, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:13:45', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (273, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:19:55', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (274, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:21:39', '客户A', 4);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (275, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:22:42', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (276, '用户管理', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 5, '添加用户成功：ct002', '2021-10-25 21:24:02', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (277, '角色分配', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 5, '角色分配成功：ct002', '2021-10-25 21:24:26', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (278, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:29:12', '客户002', 5);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (279, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:31:04', '客户002', 5);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (280, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:31:24', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (281, '用户密码', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'editPassword', 'sys_user', 5, '修改用户密码成功ct002', '2021-10-25 21:31:50', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (282, '用户状态', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '冻结ID：[5]', '2021-10-25 21:32:20', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (283, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[ct002]该账号已被冻结', '2021-10-25 21:32:31', 'ct002', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (284, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 21:32:41', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (285, '系统异常', 3, '127.0.0.1', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'org.springframework.web.method.annotation.MethodArgumentTypeMismatchException: Failed to convert value of type ''java.lang.String'' to required type ''com.linln.admin.demo.domain.T1''; nested exception is org.springframework.core.convert.ConversionFailedException: Failed to convert from type [java.lang.String] to type [@org.springframework.web.bind.annotation.PathVariable com.linln.admin.demo.domain.T1] for value ''8''; nested exception is org.springframework.transaction.CannotCreateTransactionException: Could not open JPA EntityManager for transaction; nested exception is org.hibernate.exception.JDBCConnectionException: Unable to acquire JDBC Connection
	org.springframework.web.method.annotation.AbstractNamedValueMethodArgumentResolver.resolveArgument(AbstractNamedValueMethodArgumentResolver.java:132)
	org.springframework.web.method.support.HandlerMethodArgumentResolverComposite.resolveArgument(HandlerMethodArgumentResolverComposite.java:126)
	org.springframework.web.method.support.InvocableHandlerMethod.getMethodArgumentValues(InvocableHandlerMethod.java:166)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:134)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:897)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:634)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:745)', '2021-10-25 21:39:53', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (286, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:00:01', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (287, '用户状态', 1, '127.0.0.1', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '正常ID：[5]', '2021-10-25 22:00:22', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (288, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:04:03', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (289, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:08:22', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (290, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:11:13', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (291, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:37:15', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (292, '系统异常', 3, '127.0.0.1', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: ""
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:592)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:203)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$5daf4a71.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:497)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:745)', '2021-10-25 22:37:47', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (293, '系统异常', 3, '127.0.0.1', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: ""
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:592)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:203)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$5daf4a71.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:497)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:745)', '2021-10-25 22:38:48', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (294, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:40:09', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (295, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:44:53', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (296, '用户登录', 2, '127.0.0.1', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-25 22:50:00', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (297, '用户登录', 2, '**************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 00:02:50', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (298, '用户密码', 1, '**************', 'com.linln.admin.system.controller.UserController', 'editPassword', 'sys_user', 1, '修改用户密码成功admin', '2021-10-26 00:03:29', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (299, '用户登录', 2, '**************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 00:03:40', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (300, '用户登录', 2, '**************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 00:03:59', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (301, '用户登录', 2, '**************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录失败：[ct002]用户名或密码错误', '2021-10-26 00:04:53', 'ct002', null);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (302, '用户登录', 2, '**************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 00:04:57', '客户002', 5);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (303, '用户登录', 2, '***************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 10:04:17', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (304, '用户管理', 1, '***************', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 6, '添加用户成功：111', '2021-10-26 10:06:50', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (305, '系统异常', 3, '***************', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: "3050.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:210)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$e3ec0001.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:748)', '2021-10-26 10:11:03', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (306, '系统异常', 3, '***************', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: "3050.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:210)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$e3ec0001.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:748)', '2021-10-26 10:11:13', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (307, '系统异常', 3, '***************', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: "3050.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:210)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$e3ec0001.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:748)', '2021-10-26 10:11:25', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (308, '系统异常', 3, '***************', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: "3050.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:210)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$e3ec0001.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:748)', '2021-10-26 10:11:30', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (309, '系统异常', 3, '***************', 'com.linln.component.actionLog.exception.ActionLogProceedAdvice', 'run', null, null, 'java.lang.NumberFormatException: For input string: "3050.5"
	java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	java.lang.Integer.parseInt(Integer.java:580)
	java.lang.Integer.parseInt(Integer.java:615)
	com.linln.admin.demo.controller.T1Controller.getChangeAmount(T1Controller.java:210)
	com.linln.admin.demo.controller.T1Controller.save(T1Controller.java:182)
	com.linln.admin.demo.controller.T1Controller$$FastClassBySpringCGLIB$$86ad6be7.invoke(<generated>)
	org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:749)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor$1.proceed(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:82)
	org.apache.shiro.authz.aop.AuthorizingMethodInterceptor.invoke(AuthorizingMethodInterceptor.java:39)
	org.apache.shiro.spring.security.interceptor.AopAllianceAnnotationsAuthorizingMethodInterceptor.invoke(AopAllianceAnnotationsAuthorizingMethodInterceptor.java:115)
	org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:688)
	com.linln.admin.demo.controller.T1Controller$$EnhancerBySpringCGLIB$$e3ec0001.save(<generated>)
	sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	java.lang.reflect.Method.invoke(Method.java:498)
	org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:189)
	org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:102)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1038)
	org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:61)
	org.apache.shiro.web.servlet.AdviceFilter.executeChain(AdviceFilter.java:108)
	org.apache.shiro.web.servlet.AdviceFilter.doFilterInternal(AdviceFilter.java:137)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.shiro.web.servlet.ProxiedFilterChain.doFilter(ProxiedFilterChain.java:66)
	org.apache.shiro.web.servlet.AbstractShiroFilter.executeChain(AbstractShiroFilter.java:449)
	org.apache.shiro.web.servlet.AbstractShiroFilter$1.call(AbstractShiroFilter.java:365)
	org.apache.shiro.subject.support.SubjectCallable.doCall(SubjectCallable.java:90)
	org.apache.shiro.subject.support.SubjectCallable.call(SubjectCallable.java:83)
	org.apache.shiro.subject.support.DelegatingSubject.execute(DelegatingSubject.java:387)
	org.apache.shiro.web.servlet.AbstractShiroFilter.doFilterInternal(AbstractShiroFilter.java:362)
	org.apache.shiro.web.servlet.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:125)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	com.linln.common.xss.XssFilter.doFilter(XssFilter.java:47)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	java.lang.Thread.run(Thread.java:748)', '2021-10-26 10:11:49', '系统', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (310, '用户管理', 1, '***************', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 6, '更新用户成功：111', '2021-10-26 10:15:33', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (311, '用户管理', 1, '***************', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 7, '添加用户成功：1212121', '2021-10-26 10:16:34', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (312, '用户登录', 2, '***************', 'com.linln.admin.system.controller.LoginController', 'login', null, null, '后台登录成功', '2021-10-26 10:18:27', '超级管理员', 1);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (313, '角色分配', 1, '***************', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 7, '角色分配成功：1212121', '2021-10-26 10:18:36', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (314, '角色分配', 1, '***************', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 7, '角色分配成功：1212121', '2021-10-26 10:18:52', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (315, '用户管理', 1, '***************', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 8, '添加用户成功：lilili', '2021-10-26 10:19:55', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (316, '角色分配', 1, '***************', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 8, '角色分配成功：lilili', '2021-10-26 10:20:05', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (317, '角色分配', 1, '***************', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 8, '角色分配成功：lilili', '2021-10-26 10:21:09', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (318, '角色分配', 1, '***************', 'com.linln.admin.system.controller.UserController', 'auth', 'sys_user', 7, '角色分配成功：1212121', '2021-10-26 10:21:34', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (319, '用户状态', 1, '***************', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[4]', '2021-10-26 10:28:59', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (320, '用户状态', 1, '***************', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[8]', '2021-10-26 10:29:04', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (321, '用户状态', 1, '***************', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[7]', '2021-10-26 10:29:09', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (322, '用户状态', 1, '***************', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[6]', '2021-10-26 10:29:13', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (323, '用户状态', 1, '***************', 'com.linln.admin.system.controller.UserController', 'updateStatus', null, null, '删除ID：[5]', '2021-10-26 10:29:21', '李善振', 3);
INSERT INTO timo.sys_action_log (id, name, type, ipaddr, clazz, method, model, record_id, message, create_date, oper_name, oper_by) VALUES (324, '用户管理', 1, '***************', 'com.linln.admin.system.controller.UserController', 'save', 'sys_user', 9, '添加用户成功：aaaaaa', '2021-10-26 10:30:45', '李善振', 3);