package com.linln.admin.order.service;

import com.linln.admin.order.domain.OrderLog;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/04/11
 */
public interface OrderLogService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<OrderLog> getPageList(Example<OrderLog> example);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    OrderLog getById(Long id);

    /**
     * 保存数据
     * @param orderLog 实体对象
     */
    OrderLog save(OrderLog orderLog);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);
}