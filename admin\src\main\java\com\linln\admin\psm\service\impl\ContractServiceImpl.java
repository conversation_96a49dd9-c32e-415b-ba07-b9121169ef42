package com.linln.admin.psm.service.impl;

import cn.hutool.core.date.DateUtil;
import com.linln.admin.psm.domain.Contract;
import com.linln.admin.psm.repository.ContractRepository;
import com.linln.admin.psm.service.ContractService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractRepository contractRepository;
    
    @Autowired
    private EntityManager entityManager;


    /**
     * 根据ID查询数据
     *
     * @param id 主键ID
     */
    @Override
    public Contract getById(Long id) {
        return contractRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     *
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Contract> getPageList(Example<Contract> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return contractRepository.findAll(example, page);
    }

    /**
     * 获取分页列表数据
     *
     * @param spec 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Contract> getPageList(Specification<Contract> spec) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return contractRepository.findAll(spec, page);
    }

    /**
     * 保存数据
     *
     * @param contract 实体对象
     */
    @Override
    @Transactional
    public Contract save(Contract contract) {
        return contractRepository.save(contract);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return contractRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }

    @Override
    public Contract newInstance() {
        Contract contract = new Contract();
        contract.setOrderNo(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmssSSS"));
        User currUser = ShiroUtil.getSubject();
        if (currUser != null) {
            contract.setSalesMan(currUser);
        }
        return contract;
    }

    /**
     * 根据关键字搜索合同（OR连接条件）
     * @param keyword 搜索关键字
     * @param salesUserId 业务人员ID
     * @param pageRequest 分页参数
     * @return 合同列表
     */
    @Override
    public List<Contract> searchByKeyword(String keyword, Long salesUserId,PageRequest pageRequest) {
        return contractRepository.searchByKeyword(keyword, salesUserId,pageRequest).getContent();
    }

    /**
     * 根据条件统计合同数据
     *
     * @param spec 查询条件
     * @return 统计结果Map
     */
    @Override
    public Map<String, Object> getStatistics(Specification<Contract> spec) {
        Map<String, Object> result = new HashMap<>();
        
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Object[]> query = cb.createQuery(Object[].class);
        Root<Contract> root = query.from(Contract.class);
        
        // 构建统计查询
        query.multiselect(
            cb.coalesce(cb.sum(root.get("totalReceivable")), BigDecimal.ZERO),
            cb.coalesce(cb.sum(root.get("productPrice")), BigDecimal.ZERO),
            cb.coalesce(cb.sum(root.get("purchasePrice")), BigDecimal.ZERO),
            cb.coalesce(cb.sum(
                cb.<BigDecimal>selectCase()
                    .when(cb.isNotNull(root.get("cnCustomer")), root.get("totalReceivable"))
                    .otherwise(BigDecimal.ZERO)
            ), BigDecimal.ZERO),
            cb.coalesce(cb.sum(
                cb.<BigDecimal>selectCase()
                    .when(cb.isNotNull(root.get("ruCustomer")), root.get("totalReceivable"))
                    .otherwise(BigDecimal.ZERO)
            ), BigDecimal.ZERO)
        );
        
        // 应用条件
        if (spec != null) {
            query.where(spec.toPredicate(root, query, cb));
        }
        
        try {
            Object[] data = entityManager.createQuery(query).getSingleResult();
            
            if (data != null && data.length >= 5) {
                result.put("totalReceivable", data[0] != null ? (BigDecimal) data[0] : BigDecimal.ZERO);
                result.put("productPrice", data[1] != null ? (BigDecimal) data[1] : BigDecimal.ZERO);
                result.put("purchasePrice", data[2] != null ? (BigDecimal) data[2] : BigDecimal.ZERO);
                result.put("cnTotalFee", data[3] != null ? (BigDecimal) data[3] : BigDecimal.ZERO);
                result.put("ruTotalFee", data[4] != null ? (BigDecimal) data[4] : BigDecimal.ZERO);
            } else {
                // 如果没有数据，返回全0
                result.put("totalReceivable", BigDecimal.ZERO);
                result.put("productPrice", BigDecimal.ZERO);
                result.put("purchasePrice", BigDecimal.ZERO);
                result.put("cnTotalFee", BigDecimal.ZERO);
                result.put("ruTotalFee", BigDecimal.ZERO);
            }
        } catch (Exception e) {
            // 查询出错时返回全0
            result.put("totalReceivable", BigDecimal.ZERO);
            result.put("productPrice", BigDecimal.ZERO);
            result.put("purchasePrice", BigDecimal.ZERO);
            result.put("cnTotalFee", BigDecimal.ZERO);
            result.put("ruTotalFee", BigDecimal.ZERO);
        }
        
        return result;
    }

    /**
     * 根据条件获取合同列表（用于导出）
     *
     * @param spec 查询条件
     * @return 合同列表
     */
    @Override
    public List<Contract> getListForExport(Specification<Contract> spec) {
        return contractRepository.findAll(spec);
    }
}
