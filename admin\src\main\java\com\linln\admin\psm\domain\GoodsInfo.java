package com.linln.admin.psm.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linln.common.utils.StatusUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.math.BigDecimal;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "psm_goods_info")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class GoodsInfo extends BaseEntity {

    // 单据号
    @Column(name = "order_no", nullable = false)
    private String orderNo;
    // 货物编号
    private String goodsNo;
    // 货物名称
    private String goodsName;
    // 货物描述
    private String goodsDesc;
    // 货物重量（KG）
    private BigDecimal weight;
    // 货物体积（m³）
    private BigDecimal volume;
    // 数量
    private Long quantity;

//    // 货运信息
//    @OneToOne(fetch = FetchType.LAZY)
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
//    @JsonIgnore
//    private Transport transport;
//
//    // 合同信息
//    @OneToOne(fetch = FetchType.LAZY)
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
//    @JsonIgnore
//    private Contract contract;

    // 备注
    private String remark;
}