package com.linln.admin.acct.service.impl;

import com.linln.admin.acct.domain.Info;
import com.linln.admin.acct.repository.InfoRepository;
import com.linln.admin.acct.service.InfoService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
@Service
public class InfoServiceImpl implements InfoService {

    @Autowired
    private InfoRepository infoRepository;

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    @Override
    public Info getById(Long id) {
        return infoRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Info> getPageList(Example<Info> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return infoRepository.findAll(example, page);
    }

    @Override
    public Info getInfoByUserId(Long userId) {
        Info info = new Info();
        info.setUserId(userId);
        return  infoRepository.findOne(Example.of(info)).orElse(null);
    }

    /**
     * 保存数据
     * @param info 实体对象
     */
    @Override
    public Info save(Info info) {
        return infoRepository.save(info);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return infoRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }
}