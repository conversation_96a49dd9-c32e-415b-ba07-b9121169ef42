package com.linln.admin.order.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.StatusUtil;
import com.linln.component.excel.annotation.Excel;
import com.linln.modules.system.domain.User;
import lombok.Data;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2023/04/11
 */
@Data
@Entity
@Table(name="or_order_log")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class OrderLog implements Serializable {
    // 主键ID
    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    private Long id;
    // 备注
    private String remark;
    // 创建时间
    @CreatedDate
    private Date createDate;
    // 更新时间
    @LastModifiedDate
    private Date updateDate;
    // 创建者
    @CreatedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="create_by")
    @JsonIgnore
    private User createBy;
    // 更新者
    @LastModifiedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="update_by")
    @JsonIgnore
    private User updateBy;
    // 数据状态
    private Byte status = StatusEnum.OK.getCode();
    // 订单号码
    @Excel("订单号码")
    private String orderNo;
    @Excel("日期 дата")
    private String markup;
    // 客户
    @Excel("客户 клиент")
    private String customer;
    // 经理
    @Excel("工号  USER CN")
    private String manager;
    // 货物编号
    @Excel("货物编码 код партия")
    private String goodsNo;
    // 国内订单金额
    @Excel("订单金额 цена китай")
    private String internalOrdAmt;
    // 采购费
    @Excel("采购费 закупок КЧ")
    private String buyFees;
    // 国内费用（包含装卸等等）
    @Excel("国内支出 ПРР CN")
    private String internalFees;
    // 国际运费
    @Excel("国际运费 COC")
    private String internationalFees;
    // 合计中国花费转账
    private String trfFees;
    // 国内情况说明
    @Excel("备注 примечание ")
    private String internalRemark;
    // 委托人
    private String clienttt;
    @Excel("国外 USER RU")
    private String userRuss;
    // 关税增值税
    @Excel("ПП 关税")
    private String taxFees;
    // 关增附加费
    @Excel("ПП %")
    private String taxExtraFees;
    // 合同金额
    @Excel("合同 ИТС")
    private String ctFees;
    // 合同附加费
    @Excel("ИТС %")
    private String ctExtrFees;
    // 俄罗斯清关服务费
    @Excel("КЧ БН 服务")
    private String russclearFees;
    // 俄罗斯当地额外费用
    private String russExtrFees;
    // 合计成本
    @Excel("ПРР и авто")
    private String costAmt;
    // 销售总价
    @Excel("销售价 продажа")
    private String salesAmt;
    // 利润
    private String profit;
    // 说明
    @Excel("说明 примечание")
    private String explaintt;
    // 预留1
    private String r1;
    // 预留2
    private String r2;
    // 预留3
    private String r3;
    // 预留4
    private String r4;
}