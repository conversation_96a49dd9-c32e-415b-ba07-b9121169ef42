<div class="apiDetail">
<div>
	<h2><span>Function(filter, isSingle, parentNode, invokeParam)</span><span class="path">zTreeObj.</span>getNodesByFilter</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Search the single node's data or collection of nodes's data by custom rules.</p>
			<p class="highlight_red">Can be customized complex search rules.</p>
			<p class="highlight_red">Please use zTree object to executing the method.</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>filter</b><span>Function</span></h4>
	<p>Custom search function. e.g. function filter(node) {...}</p>
	<p>filter's parameter: node (node's data -- JSO<PERSON>)</p>
	<p>filter's return: boolean (true means: match the rules; false means: don't match the rules)</p>
	<h4 class="topLine"><b>isSingle</b><span>Boolean</span></h4>
	<p>isSingle = true means: search only one node</p>
	<p>isSingle = false means: search the array of the nodes</p>
	<p class="highlight_red">If this parameter is omitted, as same as false</p>
	<h4 class="topLine"><b>parentNode</b><span>JSON</span></h4>
	<p>The search range, you can search node from a parent node's child nodes.</p>
	<p class="highlight_red">If this parameter is omitted, zTree will search node from all nodes.</p>
	<h4 class="topLine"><b>invokeParam</b><span>anything</span></h4>
	<p>Custom data object by user, used to calculate in the filter function.</p>
	<h4 class="topLine"><b>Return </b><span>Array(JSON) / JSON</span></h4>
	<p>If isSingle = true, will return the first node's data (JSON) what be matched. If no match, return null.</p>
	<p>If isSingle = false, will return the array of all nodes's data what be matched. if no match, return [ ].</p>
	</div>
	<h3>Examples of function</h3>
	<h4>1. Search the nodes which their 'name' contains 'test' and 'level' is 2.</h4>
	<pre xmlns=""><code>function filter(node) {
    return (node.level == 2 && node.name.indexOf("test")>-1);
}
......
var treeObj = $.fn.zTree.getZTreeObj("tree");
var node = treeObj.getNodesByFilter(filter, true); // search only one node
var nodes = treeObj.getNodesByFilter(filter); // search the array of the nodes
</code></pre>
</div>
</div>