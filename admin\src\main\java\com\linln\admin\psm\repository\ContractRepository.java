package com.linln.admin.psm.repository;

import com.linln.admin.psm.domain.Contract;
import com.linln.modules.system.repository.BaseRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface ContractRepository extends BaseRepository<Contract, Long>, JpaSpecificationExecutor<Contract> {

    /**
     * 根据关键字搜索合同（OR连接条件）
     *
     * @param keyword  搜索关键字
     * @param salesUserId 业务员ID
     * @param pageable 分页参数
     * @return 合同分页列表
     */
    @Query("SELECT c FROM Contract c WHERE (c.orderNo LIKE %:keyword% OR c.contractNo LIKE %:keyword%) AND c.status = '1' AND (:salesUserId IS NULL OR c.salesMan.id = :salesUserId)")
    Page<Contract> searchByKeyword(@Param("keyword") String keyword, @Param("salesUserId") Long salesUserId, Pageable pageable);

    /**
     * 一条SQL统计所有数据（全部数据）
     * 返回顺序：总收款, 总售价, 总采购价, 中国费用, 俄罗斯费用
     */
    @Query(value = "SELECT " +
            "COALESCE(SUM(c.total_receivable), 0) as totalReceivable, " +
            "COALESCE(SUM(c.product_price), 0) as productPrice, " +
            "COALESCE(SUM(c.purchase_price), 0) as purchasePrice, " +
            "COALESCE(SUM(CASE WHEN c.cn_customer_id IS NOT NULL THEN c.total_receivable ELSE 0 END), 0) as cnTotalFee, " +
            "COALESCE(SUM(CASE WHEN c.ru_customer_id IS NOT NULL THEN c.total_receivable ELSE 0 END), 0) as ruTotalFee " +
            "FROM psm_contract c WHERE c.status = '1'", nativeQuery = true)
    Object[] getStatisticsData();
}
