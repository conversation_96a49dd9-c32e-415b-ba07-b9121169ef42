<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo"
      xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 日志管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">类型</label>
                    <div class="layui-input-block timo-search-status">
                        <select class="timo-search-select" name="type" mo:dict="LOG_TYPE" mo-selected="${param.type}"
                                mo-empty="全部"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">日志名称 </label>
                    <div class="layui-input-block">
                        <input type="text" name="name" th:value="${param.name}" placeholder="请输入日志名称" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="pull-right screen-btn-group">
                <a class="layui-btn ajax-get" data-msg="您是否确认清空日志" th:href="@{/system/actionLog/status/delete}"
                   shiro:hasPermission="system:actionLog:status">
                    <i class="fa fa-trash"></i> 清空日志</a>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table timo-table-fixed">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th class="sortable" data-field="name">日志名称</th>
                    <th class="sortable" data-field="operBy">操作人</th>
                    <th class="sortable" data-field="type">日志类型</th>
                    <th class="sortable" data-field="ipaddr">IP地址</th>
                    <th class="sortable" data-field="message">日志消息</th>
                    <th class="sortable" data-field="createDate">记录时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.name}">日志名称</td>
                    <td th:text="${item.operName}">操作人</td>
                    <td th:text="${#dicts.keyValue('LOG_TYPE', item.type)}">日志类型</td>
                    <td th:text="${item.ipaddr}">IP地址</td>
                    <td th:text="${item.message}">日志消息</td>
                    <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                    <td>
                        <a class="open-popup" data-title="详细信息"
                           th:attr="data-url=@{'/system/actionLog/detail/'+${item.id}}"
                           shiro:hasPermission="system:actionLog:detail" data-size="800,600"
                           href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除"
                           th:href="@{/system/actionLog/status/delete(ids=${item.id})}"
                           shiro:hasPermission="system:actionLog:status">删除</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>