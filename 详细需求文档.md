# 跨境电商物流管理系统 - 详细需求文档

## 1. 系统概述

### 1.1 项目背景
跨境电商物流管理系统，主要用于管理中俄贸易业务中的采购、物流、资金等全流程业务。

### 1.2 系统目标
- 实现采购到物流的全流程管理
- 支持多币种资金管理和汇率转换
- 提供完整的成本核算和统计分析
- 支持中俄双语显示

## 2. 总体技术约定

### 2.1 安全要求
- **会话管理**：用户会话保持10分钟，超时自动退出重新登录
- **密码策略**：新用户必须强制修改初始密码，90天内必须修改密码

### 2.2 界面规范
- **字体要求**：俄语内容使用Times New Roman或Arial字体，保持一致性
- **多语言**：所有查询、展示字段均采用中俄双语显示
- **组件要求**：前端JS、CSS、日期组件等不允许外网引用，必须内部部署

### 2.3 数据规范
- **日期格式**：统一使用"YYYY-MM-DD hh24:mi:ss"格式
- **金额精度**：
  - 普通金额：Decimal(10,2)
  - 汇率：Decimal(10,5)
- **排序规则**：查询、导出数据按create_time倒序排列
- **数据关联**：采用主键关联，禁止使用业务数据关联

### 2.4 权限控制
- **数据隔离**：业务员只能查询自己的数据（含导出、统计）
- **管理员权限**：管理员可查询全部数据
- **操作权限**：DIV弹窗、金额操作等需要单独配置权限

### 2.5 操作规范
- **数据确认**：涉及数据变更需要二次确认
- **变更记录**：需记录变更前后数据，用于争议查询
- **页面刷新**：DIV遮罩事务处理后需刷新原页面

## 3. 功能模块详细需求

### 3.1 基本信息管理

#### 3.1.1 客户管理

**业务规则：**
- 客户姓名+业务信息+联系人组成联合唯一键
- 遇到冲突时需要区别处理

**功能需求：**

1. **客户信息维护**
   - 新增/编辑/删除客户信息
   - 支持AJAX智能提示选择
   - INPUT框支持模糊匹配（客户信息、业务信息、联系人任意信息）

2. **客户信息查询**
   - 支持客户姓名模糊查询
   - 支持业务信息筛选
   - 支持联系人信息查询

3. **数据关联**
   - 合同客户名称来源于此表
   - 物流发货人信息来源于此表
   - 收货人信息来源于此表

**界面要求：**
- INPUT框填写时自动匹配并展示候选项
- 只能选择已存在的客户，不能手动填写
- 支持点选确认

#### 3.1.2 字典管理

**功能需求：**

1. **字典维护**
   - 支持新增/编辑/删除字典项
   - 支持字典项排序
   - 支持启用/禁用状态控制

2. **字典分类**（详见字典项明细表）
   - 客户所在国家 (CUST_LOCAL)
   - 客户类型 (CUST_TYPE)
   - 合同状态 (PUR_CON_STATUS)
   - 合同收款方 (PUR_PAYEE_TYPE)
   - 币种 (CUR_TYPE)
   - 业务类型 (BIZ_TYPE)
   - 资金流向 (FUND_FLOW)
   - 成本类型 (COST_TYPE)
   - 运输状态 (TRANS_STATUS)
   - 物流入账状态 (TRANS_PAYEE_ACC_STATUS)
   - 物流收款类型 (TRANS_PAYEE_TYPE)
   - 货物描述 (GOODS_DESC)
   - 物流付款发生地 (TRANS_LOC_TYPE)
   - 物流付款类型 (TRANS_PAYMENT_TYPE)
   - 货运方式 (TRANS_TYPE)

#### 3.1.3 汇率管理

**业务规则：**
- 每个币种维护相对美元汇率
- 汇率表示：1该货币单位 = X美元
- 汇率精度：小数点后5位

**功能需求：**

1. **汇率维护**
   - 币种信息维护（作为字典项）
   - 汇率信息实时维护
   - 汇率历史记录查询

2. **汇率应用**
   - 所有金额录入时选择币种
   - 系统自动按汇率折算美元
   - 支持手动调整折算后金额

**界面要求：**
- 编辑、维护页面支持实时汇率更新
- 汇率变更实时影响币种下拉框
- 提供汇率计算器功能

### 3.2 采购信息管理

#### 3.2.1 采购管理

**业务规则：**
- 支持预付款、尾款管理
- 资金数据自动记录到资金表
- 成本变更时实时更新采购主表

**功能需求：**

1. **采购单管理**
   - 新增/编辑/删除采购单
   - 合同金额、实收金额维护
   - 采购状态管理

2. **金额管理**
   - 点击金额弹出DIV遮罩页面
   - 源币种自动折算美元（参考价）
   - 支持手动调整实际价格
   - 提交后刷新采购查询页面

3. **成本管理**
   - 成本信息单独页面操作
   - 支持多种成本类型
   - 成本变更实时计算求和并更新主表

**查询需求：**
- 商户名称模糊查询
- 合同状态筛选
- 合同签订日期范围
- 合同完成日期范围
- 业务员模糊查询

**统计需求：**
```
A项 合同金额总计 $XXX 成本$XXX 实收款 $XXXX
├─ A1境内收款 总计 $XXX 成本$XXX 实收款 $XXXX
└─ A2境外收款 总计 $XXX 成本$XXX 实收款 $XXXX
备注：A = A1 + A2
```

**界面要求：**
- 鼠标悬停合同金额、实收金额时弹出DIV（不遮罩）
- 鼠标悬停成本金额时弹出DIV（不遮罩）
- 鼠标移开时DIV消失
- 编辑时不能修改合同编号
- 需要验证业务编号合法性和权限

#### 3.2.2 出库管理

**功能需求：**

1. **出库单管理**
   - 新增/编辑出库信息
   - 关联采购单信息
   - 出库状态管理

2. **金额处理**
   - 参照通用金额维护页面
   - 支持多币种金额录入

**查询需求：**
- 采购单号模糊查询
- 出库状态筛选
- 出库日期范围查询

**统计需求：**
```
A 成本总计XX元，销售金额XX元
├─ A1其中合同完结 成本XX元，销售金额XX元
│   ├─ A11 合同境内收款 成本XX元，销售金额XX元
│   └─ A12 合同境外收款 成本XX元，销售金额XX元
└─ A2其中合同非完结 成本XX元，销售金额XX元
    ├─ A21 合同境内收款 成本XX元，销售金额XX元
    └─ A22 合同境外收款 成本XX元，销售金额XX元
备注：A11 + A12 = A1, A21 + A22 = A2, A1 + A2 = A
```

### 3.3 物流管理

#### 3.3.1 物流信息管理

**业务规则：**
- 采购合同可为空，填写时需有效性验证
- 货物编码唯一
- 收货人、发货人只能选择，不能填写
- 业务员取当前登录人员ID

**功能需求：**

1. **物流基本信息**
   - 新增/编辑物流信息
   - 关联采购合同（可选）
   - 运输状态管理

2. **货物信息维护**
   - 货物编号只读显示
   - 点击时DIV悬浮遮罩填写
   - 填写完毕更新货物名称并刷新
   - 鼠标悬停货物名称展示详细信息

3. **货运信息**
   - 编辑、展示同货物信息
   - 支持多种货运方式

4. **成本管理**
   - 金额信息展示同采购模块
   - 付款详情DIV效果
   - 付款明细单独页面维护

5. **客户信息关联**
   - 收货人、发货人从客户信息中AJAX选择
   - 支持智能提示，只能选择不能填写

**查询需求：**
- 采购单号模糊查询
- 运单号模糊查询
- 货物编号模糊查询
- 运输状态筛选
- 入账状态筛选
- 收款类型筛选
- 货物名称模糊查询
- 收款日期范围

**统计需求：**
```
A收款合计XX美元，B成本合计XX元
├─ A1境内合计收款XX元，B1成本XX元
└─ A2境外合计收款XX元，B2成本XX元
备注：A = A1 + A2，B = B1 + B2

境内收款合计：XX美元（物流表）
境内付款合计：XX美元（付款表）
境外付款合计：XX美元（付款表）

境外收款合计：XX美元（物流表）
境内付款合计：XX美元（付款表）
境外付款合计：XX美元（付款表）
```

**导出需求：**
- 支持查询结果导出
- 导出页面同查询结果
- 同时导出货物信息

#### 3.3.2 货物信息管理

**业务规则：**
- 可通过物流表填写，也可在货物信息页面填写
- 单独填写时需验证货物编号有效性
- 通过物流表时只读展示

**功能需求：**

1. **货物基本信息**
   - 货物编号管理（唯一性）
   - 货物描述维护
   - 货物规格信息

2. **关联管理**
   - 与物流信息关联
   - 支持一对多关系

#### 3.3.3 物流付款管理

**功能需求：**

1. **付款记录管理**
   - 新增/编辑付款记录
   - 付款状态跟踪
   - 付款凭证管理

**查询需求：**
- 付款发生地筛选
- 付款类型筛选
- 运输状态筛选
- 货物编码查询

### 3.4 资金管理

#### 3.4.1 成本列表

**功能需求：**

1. **成本记录管理**
   - 各种成本类型录入
   - 成本分类统计
   - 成本趋势分析

2. **成本计算**
   - 自动汇总计算
   - 实时更新主表数据

#### 3.4.2 资金列表

**功能需求：**

1. **资金流水管理**
   - 资金进出记录
   - 资金流向跟踪
   - 操作人记录

2. **资金统计**
   - 按币种统计
   - 按业务类型统计
   - 按时间段统计

## 4. 数据库设计要求

### 4.1 主要数据表

1. **客户信息表** (cust_info)
   - cust_org, cust_detail, cont_name 联合唯一

2. **采购主表** (purchase_main)
   - 与成本子表 1:N 关系
   - 与出库表 1:N 关系

3. **成本记录表** (cost_record)
   - 关联采购主表

4. **资金明细表** (amount_detail)
   - 记录所有资金流水

5. **采购出库表**
   - 关联采购主表

6. **物流主表** (trans_main)
   - 与货运信息表 1:N 关系
   - 与成本表 1:N 关系
   - 与付款明细表 1:N 关系

7. **货运信息表**
   - 关联物流主表

8. **货运付款明细表**
   - 关联物流主表

### 4.2 数据表关系
- 一对多、一对一关系采用主键关联
- 禁止使用业务数据关联
- 所有表包含create_time字段（记录创建时间）

## 5. 技术实现要求

### 5.1 前端要求
- 支持AJAX智能提示
- DIV遮罩和弹出功能
- 响应式设计
- 中俄双语支持

### 5.2 后端要求
- 权限控制细粒度到操作级别
- 数据变更前后记录保存
- 事务处理确保数据一致性
- 支持并发访问

### 5.3 性能要求
- 统计信息一条SQL解决
- 页面响应时间不超过3秒
- 支持大数据量导出

## 6. 质量要求

### 6.1 可用性
- 系统可用性≥99%
- 支持7×24小时运行

### 6.2 安全性
- 用户权限严格控制
- 数据传输加密
- 操作日志完整记录

### 6.3 扩展性
- 支持业务规则灵活配置
- 支持新的币种和汇率
- 支持新的业务类型扩展

## 7. 交付要求

### 7.1 文档交付
- 系统部署文档
- 用户操作手册
- 接口文档
- 数据库设计文档

### 7.2 培训要求
- 系统管理员培训
- 最终用户培训
- 运维人员培训

## 8. 验收标准

### 8.1 功能验收
- 所有功能模块正常运行
- 数据统计准确无误
- 权限控制有效

### 8.2 性能验收
- 响应时间符合要求
- 并发用户数达标
- 数据导出功能正常

### 8.3 安全验收
- 权限测试通过
- 数据安全测试通过
- 系统安全扫描通过
