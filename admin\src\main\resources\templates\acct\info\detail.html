<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="timo-detail-page">
        <div class="timo-detail-title">基本信息</div>
        <table class="layui-table timo-detail-table">
            <colgroup>
                <col width="100px"><col>
                <col width="100px"><col>
            </colgroup>
            <tbody>
                <tr>
                    <th>余额</th>
                    <td th:text="${info.balance}"></td>
                    <th>汇款总金额</th>
                    <td th:text="${info.historyAmount}"></td>
                </tr>
                <tr>
                    <th>客户号</th>
                    <td th:text="${info.userId}"></td>
                    <th>客户名称</th>
                    <td th:text="${info.userName}"></td>
                </tr>
                <tr>
                    <th>创建者</th>
                    <td th:text="${info.createBy?.nickname}"></td>
                    <th>更新者</th>
                    <td th:text="${info.updateBy?.nickname}"></td>
                </tr>
                <tr>
                    <th>创建时间</th>
                    <td th:text="${#dates.format(info.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <th>更新时间</th>
                    <td th:text="${#dates.format(info.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </tr>
                <tr>
                    <th>备注</th>
                    <td th:text="${info.remark}" colspan="3"></td>
                </tr>
            </tbody>
        </table>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>