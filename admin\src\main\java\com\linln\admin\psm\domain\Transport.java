package com.linln.admin.psm.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linln.common.utils.StatusUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "psm_transport")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class Transport extends BaseEntity {

    // 单据号
    @Column(name = "order_no", nullable = false)
    private String orderNo;

    // 运输方式
    private String transportType;

    // 运输公司
    private String transportCompany;

    // 中国总费用
    private BigDecimal chinaTotalCost;

    // 中国经办人
    private String chinaHandler;

    // 俄罗斯总费用
    private BigDecimal russiaTotalCost;

    // 俄罗斯经办人
    private String russiaHandler;

    // 启运日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date departureDate;

    // 送达日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    // 备注
    private String remark;

//    // 物流货物信息
//    @OneToOne(fetch = FetchType.LAZY)
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
//    @JsonIgnore
//    private GoodsInfo goodsInfo;
//
//    // 合同信息
//    @OneToOne(fetch = FetchType.LAZY)
//    @NotFound(action = NotFoundAction.IGNORE)
//    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
//    @JsonIgnore
//    private Contract contract;
}