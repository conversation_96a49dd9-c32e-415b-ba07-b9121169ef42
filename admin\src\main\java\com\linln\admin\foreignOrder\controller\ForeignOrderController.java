package com.linln.admin.foreignOrder.controller;

import com.linln.admin.foreignOrder.domain.ForeignOrder;
import com.linln.admin.foreignOrder.service.ForeignOrderService;
import com.linln.admin.foreignOrder.validator.ForeignOrderValid;
import com.linln.common.constant.AdminConst;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.Role;
import com.linln.modules.system.domain.User;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.util.StringUtils;

import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Controller
@RequestMapping("/foreignOrder/foreignOrder")
public class ForeignOrderController {

    @Autowired
    private ForeignOrderService foreignOrderService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("foreignOrder:foreignOrder:index")
    public String index(Model model,
                        @RequestParam(required = false) String ruContractNo,
                        @RequestParam(required = false) String cnRuContractNo,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ruPaymentStartDate,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate ruPaymentEndDate
    ) {

        User loginUser = ShiroUtil.getSubject();
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();

        ForeignOrder probe = new ForeignOrder();
        probe.setRuContractNo(ruContractNo);
        probe.setCnRuContractNo(cnRuContractNo);

        if (!loginUser.getId().equals(AdminConst.ADMIN_ID)
                && subjectRoles.stream().noneMatch(x -> "purchasingManager".equalsIgnoreCase(x.getName()))) {
            probe.setCreator(loginUser.getId());
        }

        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("ruContractNo", match -> match.contains())
                .withMatcher("cnRuContractNo", match -> match.contains());
        Example<ForeignOrder> example = Example.of(probe, matcher);

        Date startDate = ruPaymentStartDate != null ? Date.valueOf(ruPaymentStartDate) : null;
        Date endDate = ruPaymentEndDate != null ? Date.valueOf(ruPaymentEndDate) : null;

        Specification<ForeignOrder> spec = foreignOrderService.buildSpecByExample(example, startDate, endDate);

        Page<ForeignOrder> list = foreignOrderService.getPageListBySpec(spec);

        Subject subject = SecurityUtils.getSubject();
        boolean internalFlag = subject.hasRole("Chain") || subject.hasRole("admin");

        model.addAttribute("internalFlag", internalFlag);
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);

        return "/foreignOrder/foreignOrder/index";
    }



    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("foreignOrder:foreignOrder:add")
    public String toAdd(Model model) {
        Subject subject = SecurityUtils.getSubject();
        boolean internalFlag = subject.hasRole("Chain") || subject.hasRole("admin");
        model.addAttribute("internalFlag", internalFlag);
        return "/foreignOrder/foreignOrder/add";
    }


    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("foreignOrder:foreignOrder:edit")
    public String toEdit(@PathVariable("id") ForeignOrder foreignOrder, Model model) {
        Subject subject = SecurityUtils.getSubject();
        boolean internalFlag = subject.hasRole("Chain") || subject.hasRole("admin");
        model.addAttribute("internalFlag", internalFlag);
        model.addAttribute("foreignOrder", foreignOrder);
        return "/foreignOrder/foreignOrder/add";
    }


    /**
     * 保存添加/修改的数据
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"foreignOrder:foreignOrder:add", "foreignOrder:foreignOrder:edit"})
    @ResponseBody
    public ResultVo save(@Validated ForeignOrderValid valid, ForeignOrder foreignOrder) {
        User loginUser = ShiroUtil.getSubject();
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        boolean expressadmin = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("purchasingManager", x.getName()));
        if (!expressadmin && !loginUser.getId().equals(AdminConst.ADMIN_ID)
                && subjectRoles.stream().noneMatch(x -> "purchasing".equalsIgnoreCase(x.getName()))) {
            return ResultVoUtil.error("无权限操作");
        }
        // 复制保留无需修改的数据
        if (foreignOrder.getId() != null) {
            ForeignOrder beForeignOrder = foreignOrderService.getById(foreignOrder.getId());
            EntityBeanUtil.copyProperties(beForeignOrder, foreignOrder);
        }
        foreignOrder.setCreator(loginUser.getId());
        // 保存数据
        foreignOrderService.save(foreignOrder);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("foreignOrder:foreignOrder:detail")
    public String toDetail(@PathVariable("id") ForeignOrder foreignOrder, Model model) {
        Subject subject = SecurityUtils.getSubject();
        boolean internalFlag = subject.hasRole("Chain") || subject.hasRole("admin");
        model.addAttribute("internalFlag", internalFlag);
        model.addAttribute("foreignOrder",foreignOrder);
        return "/foreignOrder/foreignOrder/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("foreignOrder:foreignOrder:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (foreignOrderService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}