<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
  <style>
    .layui-laydate {
      z-index: 19999999 !important;
    }
  </style>
</head>
<body>
<div class="layui-form timo-compile">
  <form th:action="@{/foreignOrder/foreignOrder/save}" method="post" lay-verify="">
    <input type="hidden" name="id" th:if="${foreignOrder}" th:value="${foreignOrder.id}">

    <div class="layui-form-item">
      <label class="layui-form-label required">业务员</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="operatorName" placeholder="请输入业务员" th:value="${foreignOrder?.operatorName}" lay-verify="required">
      </div>
    </div>

    <fieldset class="layui-elem-field layui-field-title"><legend>俄罗斯信息</legend></fieldset>
    <div class="layui-form-item">
      <label class="layui-form-label required">销售合同号</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="ruContractNo" placeholder="请输入销售合同号" th:value="${foreignOrder?.ruContractNo}" lay-verify="required">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label required">售价</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="ruSalePrice" placeholder="请输入售价" th:value="${foreignOrder?.ruSalePrice}" lay-verify="required">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label required">收款方</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="ruPayee" placeholder="请输入收款方" th:value="${foreignOrder?.ruPayee}" lay-verify="required">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">收款日期</label>
      <div class="layui-input-inline">
        <input id="ruPaymentDate" class="layui-input" type="text" name="ruPaymentDate" placeholder="请选择收款日期"
               th:value="${#dates.format(foreignOrder?.ruPaymentDate, 'yyyy-MM-dd')}">

      </div>
    </div>

    <fieldset class="layui-elem-field layui-field-title"><legend>中俄贸易信息</legend></fieldset>
    <div class="layui-form-item">
      <label class="layui-form-label required">外贸合同号</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="cnRuContractNo" placeholder="请输入外贸合同号" th:value="${foreignOrder?.cnRuContractNo}" lay-verify="required">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label required">采购价</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="purchasePrice" placeholder="请输入采购价" th:value="${foreignOrder?.purchasePrice}" lay-verify="required">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">采购付款日期</label>
      <div class="layui-input-inline">
        <input id="purchasePaymentDate" class="layui-input" type="text" name="purchasePaymentDate"
               placeholder="请选择付款日期"
               th:value="${#dates.format(foreignOrder?.purchasePaymentDate, 'yyyy-MM-dd')}">
      </div>
    </div>

    <fieldset class="layui-elem-field layui-field-title"><legend>运输与报关</legend></fieldset>
    <div class="layui-form-item">
      <label class="layui-form-label">运输方式</label>
      <div class="layui-input-inline">
        <select name="transportMode">
          <option value="">请选择运输方式</option>
          <option value="陆运特快" th:selected="${foreignOrder?.transportMode == '陆运特快'}">陆运特快</option>
          <option value="陆运普快" th:selected="${foreignOrder?.transportMode == '陆运普快'}">陆运普快</option>
          <option value="铁路运输" th:selected="${foreignOrder?.transportMode == '铁路运输'}">铁路运输</option>
          <option value="空运" th:selected="${foreignOrder?.transportMode == '空运'}">空运</option>
          <option value="海运" th:selected="${foreignOrder?.transportMode == '海运'}">海运</option>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">运费</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="transportCost" placeholder="请输入运费" th:value="${foreignOrder?.transportCost}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">杂费</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="miscellaneousCost" placeholder="请输入杂费" th:value="${foreignOrder?.miscellaneousCost}">
      </div>
    </div>
    <div class="layui-form-item layui-form-text">
      <label class="layui-form-label">报关备注</label>
      <div class="layui-input-block">
        <textarea placeholder="请输入报关备注" class="layui-textarea" name="declarationRemark">[[${foreignOrder?.declarationRemark}]]</textarea>
      </div>
    </div>

    <fieldset class="layui-elem-field layui-field-title"><legend>货物信息</legend></fieldset>
    <div class="layui-form-item">
      <label class="layui-form-label">品名</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="productName" placeholder="请输入品名" th:value="${foreignOrder?.productName}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">重量(KG)</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="weight" placeholder="请输入重量" th:value="${foreignOrder?.weight}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">体积(M³)</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="volume" placeholder="请输入体积" th:value="${foreignOrder?.volume}">
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label">数量</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" name="quantity" placeholder="请输入数量" th:value="${foreignOrder?.quantity}">
      </div>
    </div>

    <fieldset class="layui-elem-field layui-field-title" th:if="${internalFlag}"><legend>中国内贸信息</legend></fieldset>

    <div class="layui-form-item" th:if="${internalFlag}">
      <label class="layui-form-label">内贸合同号</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="cnDomesticContractNo" placeholder="请输入内贸合同号" th:value="${foreignOrder?.cnDomesticContractNo}">
      </div>
    </div>

    <div class="layui-form-item" th:if="${internalFlag}">
      <label class="layui-form-label required">内贸合同价</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="number" step="0.01" name="cnDomesticPrice" placeholder="请输入合同价" th:value="${foreignOrder?.cnDomesticPrice}" lay-verify="required">
      </div>
    </div>

    <div class="layui-form-item" th:if="${internalFlag}">
      <label class="layui-form-label required">付款日期</label>
      <div class="layui-input-inline">
        <input id="cnPaymentDate" class="layui-input" type="text" name="cnPaymentDate" placeholder="请选择付款日期"
               th:value="${#dates.format(foreignOrder?.cnPaymentDate, 'yyyy-MM-dd')}">
      </div>
    </div>

    <div class="layui-form-item" th:if="${internalFlag}">
      <label class="layui-form-label required">付款方</label>
      <div class="layui-input-inline">
        <input class="layui-input" type="text" name="cnPayer" placeholder="请输入付款方" th:value="${foreignOrder?.cnPayer}" lay-verify="required">
      </div>
    </div>


    <div class="layui-form-item timo-finally">
      <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
      <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
    </div>
  </form>
</div>

<script th:replace="/common/template :: script"></script>
<script th:replace="/common/template :: script"></script>
<script>
  layui.use(['laydate', 'form'], function(){
    var laydate = layui.laydate;
    var form = layui.form;

    // 初始化日期控件
    laydate.render({
      elem: '#ruPaymentDate',
      format: 'yyyy-MM-dd',
      position: 'fixed',
      zIndex: 19999999,
      trigger: 'click',
      done: function(value){
        toggleOtherRequired(value);  // 选择日期后调用
      }
    });

    laydate.render({
      elem: '#purchasePaymentDate',
      format: 'yyyy-MM-dd',
      position: 'fixed',
      zIndex: 19999999,
      trigger: 'click'
    });

    laydate.render({
      elem: '#cnPaymentDate',
      format: 'yyyy-MM-dd',
      position: 'fixed',
      zIndex: 19999999,
      trigger: 'click'
    });

    // 核心逻辑：动态加上必填
    function toggleOtherRequired(value) {
      if (value) {
        // 除了ruPaymentDate本身，其他所有输入框、下拉框、文本域加上必填
        document.querySelectorAll('.layui-form input[name]:not([name="ruPaymentDate"]), .layui-form select[name], .layui-form textarea[name]').forEach(function(el) {
          el.setAttribute('lay-verify', 'required');
        });
      } else {
        // 可选：如果不需要必填，可以移除
        document.querySelectorAll('.layui-form input[name]:not([name="ruPaymentDate"]), .layui-form select[name], .layui-form textarea[name]').forEach(function(el) {
          el.removeAttribute('lay-verify');
        });
      }
      form.render('select');  // 重新渲染select
    }

    // 页面初始化：如果已有ruPaymentDate值，自动触发一次
    var ruDate = document.getElementById('ruPaymentDate').value;
    if (ruDate) {
      toggleOtherRequired(ruDate);
    }
  });
</script>

</body>
</html>
