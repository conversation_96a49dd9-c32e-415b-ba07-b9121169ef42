package com.linln.admin.psm.domain;

import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.StatusUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "psm_customer")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class Customer extends BaseEntity {
    // 数据状态
    private Byte status = StatusEnum.OK.getCode();
    // 客户信息
    private String custOrg;
    // 业务信息
    private String custDetail;
    // 联系人
    private String contName;
    // 客户所在国家
    private String custCountry;
    // 客户类型
    private String custType;
    // 联系电话
    private String contPhone;
}