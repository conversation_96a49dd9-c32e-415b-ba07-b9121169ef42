<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 资金管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <div class="layui-form entity-type">
                        <select name="c11" mo:list="${userList}" mo-selected="" mo-empty="全部"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search">查询</i>
                    </button>
                </div>
            </div>
            <div class="pull-right screen-btn-group">
                <button class="layui-btn open-popup" data-title="汇款" th:attr="data-url=@{/demo/t1/add2}" data-size="auto">
                    <i class="fa fa-plus"></i> 汇款</button>
                <button class="layui-btn open-popup" data-title="添加交易记录" th:attr="data-url=@{/demo/t1/add}" data-size="auto">
                    <i class="fa fa-plus"></i> 添加交易记录</button>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th>收入/支出</th>
                    <th>交易后余额</th>
                    <th>日期</th>
                    <th>客户号</th>
                    <th>客户名称</th>
                    <th>汇款金额</th>
                    <th>货物重量</th>
                    <th>体积</th>
                    <th>货款</th>
                    <th>国内运输款</th>
                    <th>国际运输款</th>
                    <th>装卸费</th>
                    <th>验货费</th>
                    <th>报关费</th>
                    <th>其他费用</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.c15}">收入/支出</td>
                    <td th:text="${item.c14}">交易后余额</td>
                    <td th:text="${item.c10}">日期</td>
                    <td th:text="${item.c11}">客户号</td>
                    <td th:text="${item.c12}">客户名称</td>
                    <td th:text="${item.c13}">汇款金额</td>
                    <td th:text="${item.c1}">货物重量</td>
                    <td th:text="${item.c2}">体积</td>
                    <td th:text="${item.c3}">货款</td>
                    <td th:text="${item.c4}">国内运输款</td>
                    <td th:text="${item.c5}">国际运输款</td>
                    <td th:text="${item.c6}">装卸费</td>
                    <td th:text="${item.c7}">验货费</td>
                    <td th:text="${item.c8}">报关费</td>
                    <td th:text="${item.c9}">其他费用</td>
                    <td>
                        <a class="open-popup" data-title="编辑资金管理" th:attr="data-url=@{'/demo/t1/edit/'+${item.id}}" data-size="auto" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/demo/t1/detail/'+${item.id}}" data-size="800,600" href="#">详细</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
