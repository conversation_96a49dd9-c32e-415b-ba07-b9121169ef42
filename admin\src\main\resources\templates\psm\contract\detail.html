<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .timo-detail-page {
            padding: 20px;
        }
        .timo-detail-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-left: 10px;
            border-left: 4px solid #009688;
        }
        .timo-detail-table {
            margin-bottom: 30px;
        }
        .timo-detail-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            text-align: right;
            padding-right: 15px;
            width: 150px;
        }
        .timo-detail-table td {
            padding-left: 15px;
            color: #666;
        }
        .detail-section {
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="timo-detail-page">
        
        <!-- 基本信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">基本信息 Основная информация</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col>
                    <col width="150px"><col>
                </colgroup>
                <tbody>
                    <tr>
                        <th>单据号 код-данных</th>
                        <td th:text="${contract.orderNo}"></td>
                        <th>创建时间 дата создания</th>
                        <td th:text="${#dates.format(contract.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    </tr>
                    <tr>
                        <th>销售合同编号 номер конт.(прод.)</th>
                        <td th:text="${contract.contractNo}"></td>
                        <th>外贸合同号 номер ВТК</th>
                        <td th:text="${contract.foreignContractNo}"></td>
                    </tr>
                    <tr>
                        <th>单据状态 статус</th>
                        <td th:text="${#dicts.keyValue('CONTRACT_STATUS', contract.contractStatus)}"></td>
                        <th>收款方 тип оплата</th>
                        <td th:text="${#dicts.keyValue('PAYEE_TYPE', contract.payeeType)}"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 财务信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">财务信息 Финансовая информация</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col>
                    <col width="150px"><col>
                </colgroup>
                <tbody>
                    <tr>
                        <th>总应收款 сумма</th>
                        <td th:text="'$' + ${contract.totalReceivable}"></td>
                        <th>产品售价 Пр.Ц</th>
                        <td th:text="'$' + ${contract.productPrice}"></td>
                    </tr>
                    <tr>
                        <th>采购价 закуп.цена</th>
                        <td th:text="'$' + ${contract.purchasePrice}"></td>
                        <th>收款日期 дата оплата</th>
                        <td th:text="${#dates.format(contract.collectionDate, 'yyyy-MM-dd')}"></td>
                    </tr>
                    <tr>
                        <th>采购付款日期 ДОЗ</th>
                        <td th:text="${#dates.format(contract.paymentDueDate, 'yyyy-MM-dd')}"></td>
                        <th></th>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 项目信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">项目信息 Информация о проекте</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col>
                    <col width="150px"><col>
                </colgroup>
                <tbody>
                    <tr>
                        <th>项目负责人（俄方） рук-ль (РФ)</th>
                        <td th:text="${contract.ruCustomer?.contName}"></td>
                        <th>项目负责人（中方） рук-ль (КНР)</th>
                        <td th:text="${contract.cnCustomer?.contName}"></td>
                    </tr>
                    <tr>
                        <th>业务员 менеджер</th>
                        <td th:text="${contract.salesMan?.username}"></td>
                        <th></th>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 物流货物信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">物流货物信息 Информация о логистике и товарах</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col>
                    <col width="150px"><col>
                </colgroup>
                <tbody>
                    <tr>
                        <th>运输信息 инфо о логистике</th>
                        <td th:text="${contract.transport?.orderNo}"></td>
                        <th>货物信息 инфо о грузе</th>
                        <td th:text="${contract.goodsInfo?.goodsName}"></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 系统信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">系统信息 Системная информация</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col>
                    <col width="150px"><col>
                </colgroup>
                <tbody>
                    <tr>
                        <th>创建者 Создатель</th>
                        <td th:text="${contract.createBy?.nickname}"></td>
                        <th>更新者 Обновивший</th>
                        <td th:text="${contract.updateBy?.nickname}"></td>
                    </tr>
                    <tr>
                        <th>更新时间 Время обновления</th>
                        <td th:text="${#dates.format(contract.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <th></th>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 备注信息 -->
        <div class="detail-section">
            <div class="timo-detail-title">备注信息 Примечание</div>
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="150px"><col colspan="3">
                </colgroup>
                <tbody>
                    <tr>
                        <th>备注 Примечание</th>
                        <td th:text="${contract.remark}" colspan="3"></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
