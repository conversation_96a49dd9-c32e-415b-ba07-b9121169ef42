package com.linln.admin.psm.repository;

import com.linln.admin.psm.domain.Customer;
import com.linln.modules.system.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface CustomerRepository extends BaseRepository<Customer, Long> {

    /**
     * 根据关键字搜索客户（OR连接条件）
     *
     * @param keyword  搜索关键字
     * @param pageable 分页参数
     * @return 客户分页列表
     */
    @Query("SELECT c FROM Customer c WHERE (c.custOrg LIKE %:keyword% OR c.custDetail LIKE %:keyword% OR c.contName LIKE %:keyword%) AND c.status = '1'")
    Page<Customer> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);
}
