<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>editNameFlag</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.exedit</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to save the node editing name status. It is valid when <span class="highlight_red">[setting.edit.enable = true]</span></p>
			<p class="highlight_red">Do not initialize or modify it, it is an internal argument.</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p>true means: node is being edited.</p>
	<p>false means: node is not being edited.</p>
	</div>
</div>
</div>