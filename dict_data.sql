INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('客户所在国家', 'CUST_LOCAL', 2, 'CN:中国,RU:俄罗斯', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('客户类型', 'CUST_TYPE', 2, 'INI:个人,COM:公司', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('合同状态', 'PUR_CON_STATUS', 2, 'INIT:初始化,FINISH:已完结,CANCLE:作废', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('合同收款方', 'PUR_PAYEE_TYPE', 2, 'LOCAL:境内公司,OVERSEA:境外公司', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('币种', 'CUR_TYPE', 2, 'RMB:人民币,RUB:卢布,USD:美元', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('业务类型', 'BIZ_TYPE', 2, 'PUR:采购,TRANS:物流', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('资金流向', 'FUND_FLOW', 2, 'MINUS:负相关,PLUS:正相关', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('成本类型', 'COST_TYPE', 2, 'TAX_FREE:退税,PROFIT_BACK:返点,CASH_PAY:现金,CARGO_DAM:货损,COMPLAINT:投诉,CARGO_DELAY:延误', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('运输状态', 'TRANS_STATUS', 2, 'INIT:初始化,ROADING:运输中,FINISH:已完结', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('物流入账状态', 'TRANS_PAYEE_ACC_STATUS', 2, 'INIT:初始化,FINISH:已完结', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('物流收款类型', 'TRANS_PAYEE_TYPE', 2, 'L_P:国内个人,L_C:国内公司,O_P:国外个人,O_C:国外公司', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('货物描述', 'GOODS_DESC', 2, 'DATA_LINE:数据线,BRAKE_PADS:刹车片', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('物流付款发生地', 'TRANS_LOC_TYPE', 2, 'LOCAL:境内公司,OVERSEA:境外公司', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('物流付款类型', 'TRANS_PAYMENT_TYPE', 2, 'LOC_TRANS_FEES:国内运费,LOC_TRANS_STO:国内仓储,LOC_TRANS_PER:国内杂费,OVE_TRANS_FEES:国外运维,OVE_TRANS_CUST:国外清关费', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
INSERT INTO timo.sys_dict (title, name, `type`, value, remark, create_date, update_date, create_by, update_by, status) VALUES('货运方式', 'TRANS_TYPE', 2, 'ROAD_EXP:陆运特快,ROAD_EXP_SUP:陆运超快,ROAD_EXP_ORI:陆运普快,RAIL_EXP:铁路,SEA_EXP:海运,AIR_TYPE:空运', '', '2025-07-26 20:49:18', '2025-07-26 20:49:18', 1, 1, 1);
