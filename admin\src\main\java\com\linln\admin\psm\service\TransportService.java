package com.linln.admin.psm.service;

import com.linln.admin.psm.domain.Customer;
import com.linln.admin.psm.domain.Transport;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface TransportService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<Transport> getPageList(Example<Transport> example);

    /**
     * 获取分页列表数据
     * @param spec 查询实例
     * @return 返回分页数据
     */
    Page<Transport> getPageList(Specification<Transport> spec);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    Transport getById(Long id);

    /**
     * 保存数据
     * @param transport 实体对象
     */
    Transport save(Transport transport);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);
}