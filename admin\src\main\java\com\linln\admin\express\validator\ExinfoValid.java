package com.linln.admin.express.validator;

import lombok.Data;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/03/28
 */
@Data
public class ExinfoValid implements Serializable {
    @NotEmpty(message = "货物编码 код不能为空")
    private String cargoCode;
    @NotEmpty(message = "应收款（人民币） сумма不能为空")
    @Digits(integer = 10, fraction = 2, message = "金额格式错误（整数部分最多10位，小数部分最多2位）")
    private String recvAmount;
}