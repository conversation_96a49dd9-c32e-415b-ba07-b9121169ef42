<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <link rel="stylesheet" th:href="@{/lib/zTree_v3/css/zTreeStyle/zTreeStyle.css}" type="text/css">
</head>
<body>
<div class="layui-form timo-compile">
    <form th:action="@{/system/user/save}">
        <input type="hidden" name="id" th:if="${user}" th:value="${user.id}"/>
        <div class="layui-form-item">
            <label class="layui-form-label required">用户名</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="username"  placeholder="请输入用户名" th:value="${user?.username}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">用户昵称</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="nickname" placeholder="请输入用户昵称" th:value="${user?.nickname}">
            </div>
        </div>
        <div class="layui-form-item" th:if="!${user}">
            <label class="layui-form-label required">用户密码</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="password" name="password" placeholder="请输入用户密码">
            </div>
        </div>
        <div class="layui-form-item" th:if="!${user}">
            <label class="layui-form-label required">确认密码</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="password" name="confirm" placeholder="再一次输入密码">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">所在部门</label>
            <div class="layui-input-inline">
                <input class="layui-input select-tree" th:attr="data-url=@{/system/dept/list}, data-value=${user?.dept?.id}"
                       type="text" name="dept"  placeholder="请选择所在部门" th:value="${user?.dept?.title}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">电话号码</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="phone" placeholder="请输入电话号码" th:value="${user?.phone}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">邮箱</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="email" placeholder="请输入邮箱" th:value="${user?.email}">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">选择性别</label>
            <div class="layui-input-inline">
                <input type="radio" name="sex" value="1" title="男" checked><div class="layui-unselect layui-form-radio layui-form-radioed"><i class="layui-anim layui-icon"></i><div>男</div></div>
                <input type="radio" name="sex" value="2" title="女" th:checked="${user?.sex} eq 2"><div class="layui-unselect layui-form-radio"><i class="layui-anim layui-icon"></i><div>女</div></div>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="remark">[[${user?.remark}]]</textarea>
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
<script type="text/javascript" th:src="@{/js/plugins/jquery-2.2.4.min.js}"></script>
<script type="text/javascript" th:src="@{/lib/zTree_v3/js/jquery.ztree.core.min.js}"></script>
<script type="text/javascript" th:src="@{/js/timoTree.js}"></script>
<script type="text/javascript">
    // 树形菜单
    $.fn.selectTree();
</script>
</body>
</html>