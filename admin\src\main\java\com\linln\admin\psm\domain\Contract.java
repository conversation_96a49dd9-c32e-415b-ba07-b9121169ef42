package com.linln.admin.psm.domain;

import com.linln.common.utils.StatusUtil;
import com.linln.modules.system.domain.User;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "psm_contract")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class Contract extends BaseEntity {
    // 单据号
    @Column(name = "order_no")
    private String orderNo;
    // 合同编号
    private String contractNo;
    // 合同状态
    private String contractStatus;
    // 外贸合同编号
    private String foreignContractNo;
    // 实际收款日期
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date collectionDate;
    // 采购付款日期（合同约定最迟付款日期）
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date paymentDueDate;
    // 收款方
    private String payeeType;

    // 产品价格
    private BigDecimal productPrice;
    // 采购价格
    private BigDecimal purchasePrice;
    // 总应收款
    private BigDecimal totalReceivable;

    // 俄方商户 ID (可选字段，可以为null)
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "ru_customer_id")
    private Customer ruCustomer;

    // 中国商户 ID (可选字段，可以为null)
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "cn_customer_id")
    private Customer cnCustomer;

    // 业务员 (可选字段，可以为null)
    @ManyToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "sales_user_id")
    private User salesMan;

    // 备注
    private String remark;

    @OneToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
    private GoodsInfo goodsInfo;

    @OneToOne(fetch = FetchType.LAZY)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "order_no", referencedColumnName = "order_no", insertable = false, updatable = false)
    private Transport transport;
}