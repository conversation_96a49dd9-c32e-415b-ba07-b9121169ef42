<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <title>采购合同数据导出</title>
    <style>
        .layui-form-select .layui-input {
            height: 38px;
        }
        .export-config-card {
            margin-top: 15px;
        }
        .preview-stats {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            margin: 10px 0;
        }
        .preview-stats .stat-item {
            display: inline-block;
            margin-right: 20px;
            color: #666;
        }
        .preview-stats .stat-value {
            font-weight: bold;
            color: #333;
        }
        .export-btn-area {
            text-align: center;
            padding: 20px;
        }
        .back-btn {
            margin-right: 10px;
        }
    </style>
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-download"></i> 采购合同数据导出 Экспорт данных контрактов</span>
            <div style="float: right;">
                <button type="button" class="layui-btn layui-btn-sm back-btn" onclick="goBack()">
                    <i class="fa fa-arrow-left"></i> 返回数据中心
                </button>
                <i class="layui-icon layui-icon-refresh refresh-btn"></i>
            </div>
        </div>
        <div class="layui-card-body">
            <!-- 筛选条件 -->
            <form class="layui-form" action="" id="exportForm">
                <div class="layui-card">
                    <div class="layui-card-header">
                        <span><i class="fa fa-filter"></i> 筛选条件 Условия фильтрации</span>
                    </div>
                    <div class="layui-card-body">
                        <div class="layui-form-pane">
                            <div class="layui-row layui-col-space10">
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">单据号 код-данных</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="orderNo" th:value="${param.orderNo}" placeholder="请输入单据号"
                                                autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">合同编号 номер конт.(прод.)</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="contractNo" th:value="${param.contractNo}"
                                                placeholder="请输入合同编号" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">创建时间 дата создания</label>
                                        <div class="layui-input-block" id="createDate">
                                            <input type="text" autocomplete="off" id="createDateStr" class="layui-input"
                                                name="createDateStr" placeholder="开始-结束" th:value="${param.createDateStr}">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">收款方 тип оплата</label>
                                        <div class="layui-input-block">
                                            <select class="layui-select" name="payeeType" mo:dict="PAYEE_TYPE"
                                                mo-selected="${param.payeeType}" mo-empty="全部"></select>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">负责人(中) рук-ль (КНР)</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" type="text" id="cnCustomerSearch"
                                                name="cnCustomer.contName" th:value="${contract.cnCustomer?.contName}"
                                                placeholder="请输入中方负责人" autocomplete="off">
                                            <input type="hidden" name="cnCustomer.id" th:value="${contract.cnCustomer?.id}"
                                                id="cnCustomerId">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">负责人(俄) рук-ль (РФ)</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input" type="text" id="ruCustomerSearch"
                                                name="ruCustomer.contName" th:value="${contract.ruCustomer?.contName}"
                                                placeholder="请输入俄方负责人" autocomplete="off">
                                            <input type="hidden" name="ruCustomer.id" th:value="${contract.ruCustomer?.id}"
                                                id="ruCustomerId">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">付款日期 ДОЗ</label>
                                        <div class="layui-input-block" id="paymentDueDate">
                                            <input type="text" autocomplete="off" id="paymentDueDateStr" class="layui-input"
                                                name="paymentDueDateStr" placeholder="开始-结束"
                                                th:value="${param.paymentDueDateStr}">
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md12" style="text-align: center;">
                                    <button type="button" class="layui-btn" id="previewBtn">
                                        <i class="fa fa-eye"></i> 预览统计 Предварительный просмотр
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                        <i class="fa fa-refresh"></i> 重置 Сбросить
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>

            <!-- 数据预览 -->
            <div class="layui-card export-config-card" id="previewCard" style="display: none;">
                <div class="layui-card-header">
                    <span><i class="fa fa-bar-chart"></i> 数据预览 Предварительный просмотр данных</span>
                </div>
                <div class="layui-card-body">
                    <div class="preview-stats">
                        <div class="stat-item">
                            <span>总记录数：</span>
                            <span class="stat-value" id="totalCount">0</span> 条
                        </div>
                        <div class="stat-item">
                            <span>合计收款：</span>
                            $<span class="stat-value" id="previewTotalReceivable">0.00</span>
                        </div>
                        <div class="stat-item">
                            <span>合计售价：</span>
                            $<span class="stat-value" id="previewProductPrice">0.00</span>
                        </div>
                        <div class="stat-item">
                            <span>合计采购价：</span>
                            $<span class="stat-value" id="previewPurchasePrice">0.00</span>
                        </div>
                    </div>
                    <div class="preview-stats">
                        <div class="stat-item">
                            <span>中国总费用：</span>
                            $<span class="stat-value" id="previewCnTotalFee">0.00</span>
                        </div>
                        <div class="stat-item">
                            <span>俄罗斯总费用：</span>
                            $<span class="stat-value" id="previewRuTotalFee">0.00</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导出配置 -->
            <div class="layui-card export-config-card" id="exportConfigCard" style="display: none;">
                <div class="layui-card-header">
                    <span><i class="fa fa-cog"></i> 导出配置 Настройки экспорта</span>
                </div>
                <div class="layui-card-body">
                    <form class="layui-form" id="exportConfigForm">
                        <div class="layui-row layui-col-space10">
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">导出格式</label>
                                    <div class="layui-input-block">
                                        <select name="exportFormat" lay-verify="required">
                                            <option value="excel" selected>Excel (.xlsx)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">文件名</label>
                                    <div class="layui-input-block">
                                        <input type="text" name="fileName" placeholder="默认自动生成" 
                                               autocomplete="off" class="layui-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="export-btn-area">
                        <button type="button" class="layui-btn layui-btn-lg" id="executeExportBtn">
                            <i class="fa fa-download"></i> 开始导出 Начать экспорт
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script th:replace="/common/template :: script"></script>
    <script>
        layui.config({
            base: '/js/'
        }).extend({
            autocomplete: 'autocomplete'
        });
        layui.use(['laydate', 'autocomplete', 'form', 'layer'], function () {
            var laydate = layui.laydate;
            var autocomplete = layui.autocomplete;
            var form = layui.form;
            var layer = layui.layer;

            // 返回数据中心
            window.goBack = function() {
                window.location.href = '/psm/datacenter/index';
            };

            // 重置按钮
            $('#resetBtn').on('click', function () {
                $("#exportForm input").val("");
                $("#exportForm select").val("");
                $('#previewCard').hide();
                $('#exportConfigCard').hide();
                form.render('select');
            });

            // 日期控件
            laydate.render({
                elem: '#createDateStr',
                range: true,
                rangeLinked: true
            });

            laydate.render({
                elem: '#paymentDueDateStr',
                range: true,
                rangeLinked: true
            });

            // 初始化俄方负责人autocomplete
            autocomplete.render({
                elem: '#ruCustomerSearch',
                hidden: '#ruCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected ru customer:', data);
                }
            });

            // 初始化中方负责人autocomplete
            autocomplete.render({
                elem: '#cnCustomerSearch',
                hidden: '#cnCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected cn customer:', data);
                }
            });

            // 预览统计数据 (更新API路径)
            $('#previewBtn').on('click', function() {
                var formData = $("#exportForm").serialize();
                var loadingIndex = layer.load(2, { content: '正在获取预览数据...' });
                
                $.ajax({
                    url: '/psm/datacenter/contract/preview',
                    type: 'GET',
                    data: formData,
                    success: function(result) {
                        layer.close(loadingIndex);
                        if (result.code === 200) {
                            var data = result.data;
                            $('#totalCount').text(data.totalCount || 0);
                            $('#previewTotalReceivable').text(parseFloat(data.totalReceivable || 0).toFixed(2));
                            $('#previewProductPrice').text(parseFloat(data.productPrice || 0).toFixed(2));
                            $('#previewPurchasePrice').text(parseFloat(data.purchasePrice || 0).toFixed(2));
                            $('#previewCnTotalFee').text(parseFloat(data.cnTotalFee || 0).toFixed(2));
                            $('#previewRuTotalFee').text(parseFloat(data.ruTotalFee || 0).toFixed(2));
                            
                            $('#previewCard').show();
                            $('#exportConfigCard').show();
                            
                            if (data.totalCount == 0) {
                                layer.msg('没有找到符合条件的数据！');
                                $('#executeExportBtn').addClass('layui-btn-disabled');
                            } else {
                                $('#executeExportBtn').removeClass('layui-btn-disabled');
                            }
                        } else {
                            layer.msg('获取预览数据失败：' + (result.message || '未知错误'));
                        }
                    },
                    error: function() {
                        layer.close(loadingIndex);
                        layer.msg('获取预览数据失败，请重试！');
                    }
                });
            });

            // 执行导出 (更新API路径)
            $('#executeExportBtn').on('click', function() {
                if ($(this).hasClass('layui-btn-disabled')) {
                    layer.msg('没有可导出的数据！');
                    return;
                }
                
                var formData = $("#exportForm").serialize();
                var configData = $("#exportConfigForm").serialize();
                var exportData = formData + '&' + configData;
                
                layer.confirm('确定要导出数据吗？', {
                    btn: ['确定导出', '取消'],
                    icon: 3,
                    title: '确认导出'
                }, function(index) {
                    layer.close(index);
                    
                    // 创建隐藏表单提交导出请求
                    var form = $('<form>')
                        .attr('method', 'post')
                        .attr('action', '/psm/datacenter/contract/export')
                        .attr('target', '_blank');
                    
                    // 添加表单数据
                    exportData.split('&').forEach(function(pair) {
                        var keyValue = pair.split('=');
                        if (keyValue.length === 2) {
                            form.append($('<input>')
                                .attr('type', 'hidden')
                                .attr('name', decodeURIComponent(keyValue[0]))
                                .attr('value', decodeURIComponent(keyValue[1])));
                        }
                    });
                    
                    $('body').append(form);
                    form.submit();
                    form.remove();
                    
                    layer.msg('导出任务已提交，请稍候下载...');
                });
            });
        });
    </script>
</body>

</html>
