<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.linln.component</groupId>
    <artifactId>thymeleaf</artifactId>
    <packaging>jar</packaging>
    <name>组件:thymeleaf</name>

    <parent>
        <groupId>com.linln</groupId>
        <artifactId>component</artifactId>
        <version>2.0.3</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.linln</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.modules</groupId>
            <artifactId>system</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--thymeleaf视图模板框架-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!--shiro模板视图权限标签扩展-->
        <dependency>
            <groupId>com.github.theborakompanioni</groupId>
            <artifactId>thymeleaf-extras-shiro</artifactId>
            <version>${thymeleaf-shiro.version}</version>
        </dependency>

    </dependencies>
</project>