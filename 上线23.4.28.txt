INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (169, '订单管理', 0, '[0]', '/order/orderLog/index', 'order:orderLog:index', 'layui-icon layui-icon-note', 1, 8, '', '2023-04-11 10:49:30', '2023-04-12 21:57:29', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (170, '添加', 169, '[0],[169]', '/order/orderLog/add', 'order:orderLog:add', null, 3, 1, null, '2023-04-11 10:49:32', '2023-04-11 10:49:32', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (171, '编辑', 169, '[0],[169]', '/order/orderLog/edit', 'order:orderLog:edit', null, 3, 1, null, '2023-04-11 10:49:32', '2023-04-11 10:49:32', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (172, '详细', 169, '[0],[169]', '/order/orderLog/detail', 'order:orderLog:detail', null, 3, 1, null, '2023-04-11 10:49:32', '2023-04-11 10:49:32', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (173, '修改状态', 169, '[0],[169]', '/order/orderLog/status', 'order:orderLog:status', null, 3, 1, null, '2023-04-11 10:49:32', '2023-04-11 10:49:32', 1, 1, 1);


create table timo.or_order_log
(
    id                 bigint auto_increment
        primary key,
    remark             varchar(255)      null,
    create_date        datetime          null,
    update_date        datetime          null,
    create_by          bigint            null,
    update_by          bigint            null,
    status             tinyint default 1 not null,
    order_no           varchar(255)      null,
    customer           varchar(255)      null,
    manager            varchar(255)      null,
    goods_no           varchar(255)      null,
    internal_ord_amt   varchar(255)      null,
    buy_fees           varchar(255)      null,
    internal_fees      varchar(255)      null,
    international_fees varchar(255)      null,
    trf_fees           varchar(255)      null,
    internal_remark    varchar(255)      null,
    clienttt           varchar(255)      null,
    user_russ          varchar(255)      null,
    tax_fees           varchar(255)      null,
    tax_extra_fees     varchar(255)      null,
    ct_fees            varchar(255)      null,
    ct_extr_fees       varchar(255)      null,
    russclear_fees     varchar(255)      null,
    russ_extr_fees     varchar(255)      null,
    markup             varchar(255)      null,
    cost_amt           varchar(255)      null,
    sales_amt          varchar(255)      null,
    profit             varchar(255)      null,
    explaintt          varchar(255)      null,
    r1                 varchar(255)      null,
    r2                 varchar(255)      null,
    r3                 varchar(255)      null,
    r4                 varchar(255)      null,
    constraint or_order_log_ord_no
        unique (order_no),
    constraint FK9h3u1g4yx7ssu8j4x4xquex4x
        foreign key (update_by) references timo.sys_user (id),
    constraint FKqsa7g3y6ipjmn7v8d6j2x7x0w
        foreign key (create_by) references timo.sys_user (id)
)
    charset = utf8mb4;

