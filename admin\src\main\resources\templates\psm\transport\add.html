<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .customer-autocomplete {
            position: relative;
        }

        .customer-info-row {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .form-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .form-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #009688;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
        }

        .form-col {
            flex: 1;
            min-width: 300px;
            padding: 0 10px;
        }

        .layui-form-item {
            margin-bottom: 15px;
        }

        .form-buttons {
            text-align: center;
            padding: 20px 0;
        }

        .form-buttons .layui-btn {
            margin: 0 10px;
            padding: 0 30px;
        }

        .form-buttons .ajax-submit {
            background-color: #009688;
        }

        .form-buttons .close-popup {
            background-color: #777;
        }

        .readonly-field {
            background-color: #f5f5f5 !important;
            cursor: not-allowed;
        }

        .readonly-field:focus {
            background-color: #f5f5f5 !important;
        }

        .amount-field {
            text-align: right;
        }

        .field-error {
            color: #ff5722;
            font-size: 12px;
            margin-top: 2px;
        }

        .field-success {
            color: #4caf50;
        }
    </style>
</head>

<body>
    <div class="layui-form timo-compile">
        <form th:action="@{/psm/transport/save}">
            <input type="hidden" name="id" th:if="${transport}" th:value="${transport.id}">
            <div class="layui-form-item">
                <label class="layui-form-label">单据号 код-данных</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" id="orderNoSearch" placeholder="请输入单据号进行搜索"
                        th:value="${transport?.orderNo}" autocomplete="off"
                        th:readonly="${transport?.orderNo != null && transport?.orderNo != ''}"
                        th:classappend="${transport?.orderNo != null && transport?.orderNo != ''} ? 'readonly-field' : ''">
                    <input type="hidden" name="orderNo" th:value="${transport?.orderNo}" id="orderNo">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">运输方式 вид транс</label>
                <div class="layui-input-inline">
                    <select class="timo-search-select" name="transportType" mo:dict="TRANSPORT_TYPE"
                        mo-selected="${transport?.transportType}" mo-empty=""></select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">运输公司 Трансп.компания</label>
                <div class="layui-input-inline">
                    <select class="timo-search-select" name="transportCompany" mo:dict="TRANSPORT_COMPANY"
                        mo-selected="${transport?.transportCompany}" mo-empty=""></select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">中国总费用 Итого(КНР)</label>
                <div class="layui-input-inline layui-input-wrap">
                    <div class="layui-input-prefix">$</div>
                    <input class="layui-input amount-field" type="text" name="chinaTotalCost" id="chinaTotalCost"
                           placeholder="请输入中国总费用 " lay-affix="number" lay-precision="2" step="0.01" min="0" max="*********"
                           th:value="${transport?.chinaTotalCost}">
                </div>
                <div id="chinaTotalCostError" class="field-error" style="display:none;"></div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">俄罗斯总费用 Итого(РФ)</label>
                <div class="layui-input-inline layui-input-wrap">
                    <div class="layui-input-prefix">$</div>
                    <input class="layui-input amount-field" type="text" name="russiaTotalCost" id="russiaTotalCost"
                           placeholder="请输入俄罗斯总费用 " lay-affix="number" lay-precision="2" step="0.01" min="0" max="*********"
                           th:value="${transport?.russiaTotalCost}">
                </div>
                <div id="russiaTotalCostError" class="field-error" style="display:none;"></div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">中国经办人 ОЛ (КНР)</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="chinaHandler" placeholder="请输入中国经办人"
                        th:value="${transport?.chinaHandler}">
                </div>
            </div>            
            <div class="layui-form-item">
                <label class="layui-form-label">俄罗斯经办人 ОЛ (РФ)</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="russiaHandler" placeholder="请输入俄罗斯经办人"
                        th:value="${transport?.russiaHandler}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">启运日期 дата отп</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" id="departureDate" name="departureDate" placeholder="请输入启运日期"
                        th:value="${#dates.format(transport?.departureDate, 'yyyy-MM-dd')}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">送达日期 Дата Доставки</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" id="deliveryDate" name="deliveryDate" placeholder="请输入送达日期"
                        th:value="${#dates.format(transport?.deliveryDate, 'yyyy-MM-dd')}">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注 Примечание</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入内容" class="layui-textarea"
                        name="remark">[[${transport?.remark}]]</textarea>
                </div>
            </div>
            <div class="layui-form-item timo-finally">
                <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存 держать</button>
                <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭 закрытие</button>
            </div>
        </form>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.config({
            base: '/js/'
        }).extend({
            autocomplete: 'autocomplete'
        });
        layui.use(['jquery', 'autocomplete', 'laydate', 'form'], function () {
            var $ = layui.jquery;
            var autocomplete = layui.autocomplete;
            var laydate = layui.laydate;
            var form = layui.form;

            function initDatePicker(elemId, label) {
                laydate.render({
                    elem: '#' + elemId,
                    format: 'yyyy-MM-dd',
                    position: 'fixed',
                    zIndex: 19999999,
                    trigger: 'click',
                    done: function (value) {
                        console.log(label + '日期: ' + value);
                    }
                });
            }

            // 初始化合同autocomplete
            autocomplete.render({
                elem: '#orderNoSearch',
                hidden: '#orderNo',
                url: '/psm/contract/search',
                field: 'orderNo',
                valueField: 'orderNo',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.orderNo || '') + '-' + (item.contractNo || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected contract:', data);
                }
            });

            ['departureDate', 'deliveryDate'].forEach(function (fieldId) {
                initDatePicker(fieldId, fieldId);
            });

        });
    </script>
</body>
