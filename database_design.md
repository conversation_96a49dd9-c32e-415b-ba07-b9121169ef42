# 物流管理系统数据库设计方案 (V2)

## 1. 数据库实体关系图 (ERD)

```mermaid
erDiagram
    customer {
        INT id PK "主键ID"
        VARCHAR customer_name "客户名称"
        VARCHAR contact_person "联系人"
        VARCHAR phone "电话"
    }

    trans_main {
        INT id PK "主键ID"
        VARCHAR trans_contract_code "物流合同号"
        VARCHAR status "合同状态"
        INT shipper_id FK "发货人ID"
        INT consignee_id FK "收货人ID"
        DATETIME contract_date "合同日期"
    }

    goods_info {
        INT id PK "主键ID"
        INT trans_main_id FK "物流合同ID"
        VARCHAR description "货物描述"
        DECIMAL weight_kg "重量"
        DECIMAL volume_cbm "体积"
        INT package_count "件数"
    }

    waybill_info {
        INT id PK "主键ID"
        INT trans_main_id FK "物流合同ID"
        VARCHAR waybill_code "运单号"
        VARCHAR carrier "承运商"
        VARCHAR tracking_status "追踪状态"
        DATETIME departure_date "发货日期"
    }

    cost_info {
        INT id PK "主键ID"
        INT trans_main_id FK "物流合同ID"
        VARCHAR fee_type "费用类型"
        DECIMAL amount "金额"
        VARCHAR currency "币种"
        DATETIME payment_date "付款日期"
    }

    customer ||--|{ trans_main : "发货"
    customer ||--|{ trans_main : "收货"
    trans_main ||--o{ goods_info : "包含"
    trans_main ||--o{ waybill_info : "包含"
    trans_main ||--o{ cost_info : "包含"
```

## 2. 完整 `CREATE TABLE` SQL 脚本

```sql
-- -----------------------------------------------------
-- 数据库设计：物流管理系统 (V2 - 一对多模型)
-- 设计师：Roo
-- 设计日期：2025-07-30
-- -----------------------------------------------------

-- 1. 客户/联系人表 (customer)
-- 描述: 存储发货人（shipper）和收货人（consignee）等联系人信息。
-- -----------------------------------------------------
CREATE TABLE `customer` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `customer_name` VARCHAR(255) NOT NULL COMMENT '客户/公司名称',
  `contact_person` VARCHAR(100) NULL COMMENT '联系人姓名',
  `phone` VARCHAR(50) NULL COMMENT '联系电话',
  `address` VARCHAR(500) NULL COMMENT '地址',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) COMMENT = '客户/联系人信息表';


-- -----------------------------------------------------
-- 2. 物流主表/合同表 (trans_main)
-- 描述: 系统的核心表，记录每一个物流合同的概览信息。
-- -----------------------------------------------------
CREATE TABLE `trans_main` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trans_contract_code` VARCHAR(100) NOT NULL COMMENT '物流合同号',
  `status` VARCHAR(50) NOT NULL COMMENT '合同状态 (如: 待执行, 执行中, 已完成)',
  `shipper_id` INT NOT NULL COMMENT '发货人ID (外键关联 customer.id)',
  `consignee_id` INT NOT NULL COMMENT '收货人ID (外键关联 customer.id)',
  `contract_date` DATE NULL COMMENT '合同签订日期',
  `remarks` TEXT NULL COMMENT '备注信息',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_trans_contract_code` (`trans_contract_code` ASC) COMMENT '物流合同号必须唯一',
  INDEX `idx_status` (`status` ASC) COMMENT '为合同状态创建索引',
  CONSTRAINT `fk_trans_main_shipper`
    FOREIGN KEY (`shipper_id`)
    REFERENCES `customer` (`id`)
    ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_trans_main_consignee`
    FOREIGN KEY (`consignee_id`)
    REFERENCES `customer` (`id`)
    ON DELETE RESTRICT ON UPDATE CASCADE
) COMMENT = '物流主表 (物流合同)';


-- -----------------------------------------------------
-- 3. 货物信息表 (goods_info)
-- 描述: 记录与物流合同相关的货物详情，与物流主表为一对多关系。
-- -----------------------------------------------------
CREATE TABLE `goods_info` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trans_main_id` INT NOT NULL COMMENT '物流合同ID (外键关联 trans_main.id)',
  `description` VARCHAR(500) NOT NULL COMMENT '货物描述',
  `weight_kg` DECIMAL(10, 2) NULL COMMENT '总重量 (KG)',
  `volume_cbm` DECIMAL(10, 3) NULL COMMENT '总体积 (CBM)',
  `package_count` INT NULL COMMENT '总箱数/件数',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_goods_trans_main_id` (`trans_main_id` ASC) COMMENT '为物流合同ID创建索引',
  CONSTRAINT `fk_goods_info_trans_main`
    FOREIGN KEY (`trans_main_id`)
    REFERENCES `trans_main` (`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT = '货物信息表';


-- -----------------------------------------------------
-- 4. 运单信息表 (waybill_info)
-- 描述: 记录合同下的多个运单信息，与物流主表为一对多关系。
-- -----------------------------------------------------
CREATE TABLE `waybill_info` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trans_main_id` INT NOT NULL COMMENT '物流合同ID (外键关联 trans_main.id)',
  `waybill_code` VARCHAR(100) NOT NULL COMMENT '运单号',
  `carrier` VARCHAR(150) NULL COMMENT '承运商',
  `tracking_status` VARCHAR(50) NULL COMMENT '追踪状态 (如: 已揽收, 运输中, 派送中, 已签收)',
  `departure_date` DATETIME NULL COMMENT '发货日期',
  `arrival_date` DATETIME NULL COMMENT '预计到达日期',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `uk_waybill_code` (`waybill_code` ASC) COMMENT '运单号应唯一',
  INDEX `idx_waybill_trans_main_id` (`trans_main_id` ASC) COMMENT '为物流合同ID创建索引',
  CONSTRAINT `fk_waybill_info_trans_main`
    FOREIGN KEY (`trans_main_id`)
    REFERENCES `trans_main` (`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT = '运单信息表';


-- -----------------------------------------------------
-- 5. 成本信息表 (cost_info)
-- 描述: 记录与物流合同相关的多项成本，与物流主表为一对多关系。
-- -----------------------------------------------------
CREATE TABLE `cost_info` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trans_main_id` INT NOT NULL COMMENT '物流合同ID (外键关联 trans_main.id)',
  `fee_type` VARCHAR(100) NOT NULL COMMENT '费用类型 (如: 运费, 仓储费, 保险费, 清关费)',
  `amount` DECIMAL(18, 4) NOT NULL COMMENT '金额',
  `currency` VARCHAR(10) NOT NULL DEFAULT 'CNY' COMMENT '币种 (如: CNY, USD)',
  `payment_date` DATE NULL COMMENT '付款日期',
  `remarks` VARCHAR(500) NULL COMMENT '费用备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_cost_trans_main_id` (`trans_main_id` ASC) COMMENT '为物流合同ID创建索引',
  CONSTRAINT `fk_cost_info_trans_main`
    FOREIGN KEY (`trans_main_id`)
    REFERENCES `trans_main` (`id`)
    ON DELETE CASCADE ON UPDATE CASCADE
) COMMENT = '成本信息表';