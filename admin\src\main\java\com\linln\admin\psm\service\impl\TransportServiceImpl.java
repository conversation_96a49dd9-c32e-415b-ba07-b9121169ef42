package com.linln.admin.psm.service.impl;

import com.linln.admin.psm.domain.Transport;
import com.linln.admin.psm.repository.TransportRepository;
import com.linln.admin.psm.service.TransportService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Service
public class TransportServiceImpl implements TransportService {

    @Autowired
    private TransportRepository transportRepository;

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    @Override
    public Transport getById(Long id) {
        return transportRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<Transport> getPageList(Example<Transport> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return transportRepository.findAll(example, page);
    }

    @Override
    public Page<Transport> getPageList(Specification<Transport> spec) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return transportRepository.findAll(spec, page);
    }

    /**
     * 保存数据
     * @param transport 实体对象
     */
    @Override
    public Transport save(Transport transport) {
        return transportRepository.save(transport);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return transportRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }
}