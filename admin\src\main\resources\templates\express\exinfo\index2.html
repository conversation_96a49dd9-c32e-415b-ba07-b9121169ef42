<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 货物信息</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">货物编码 код</label>
                    <div class="layui-input-block">
                        <input type="text" name="cargoCode" th:value="${param.cargoCode}" placeholder="请输入货物编码 код" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="timo-detail-page" th:if="${detail}">
            <table class="layui-table timo-detail-table">
                <colgroup>
                    <col width="100px"><col>
                    <col width="100px"><col>
                </colgroup>
                <tbody>
                <tr>
                    <th style="width: 120px;">货物编码 код</th>
                    <td th:text="${detail.cargoCode}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">发货人 отп-ль</th>
                    <td th:text="${detail.sender}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">货物名称товар</th>
                    <td th:text="${detail.cargoName}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">货物重量 KG</th>
                    <td th:text="${detail.cargoWeight}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">箱数 к-о место</th>
                    <td th:text="${detail.cartonNumber}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">货物体积M3</th>
                    <td th:text="${detail.cargoVolume}"></td>
                </tr>
                <tr>
                    <th style="width: 120px;">启运日期дата отп</th>
                    <td th:text="${detail.startDate}"></td>
                </tr>
                <tr>
                    <th style="width: 128px;">运输方式вид транс</th>
                    <td th:text="${detail.transType}"></td>
                </tr>
<!--                <tr>-->
<!--                    <th style="width: 120px;">应收款 сумма</th>-->
<!--                    <td th:text="${detail.recvAmount}"></td>-->
<!--                </tr>-->
                <!--                <tr>-->
                <!--                    <th style="width: 120px;">备注</th>-->
                <!--                    <td th:text="${detail.remark}"></td>-->
                <!--                </tr>-->
                </tbody>
            </table>
        </div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>