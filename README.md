# Timo 后台管理系统

## 项目简介
Timo 是一个基于 Spring Boot 2.1.4 的后台管理系统，用于快速搭建企业级后台管理平台。

## 解决 Maven 控制台乱码问题

在 Windows 系统中运行 Maven 命令时可能会出现控制台乱码，为了解决这个问题，我们已经在项目中添加了以下配置：

1. 在 [.mvn/jvm.config](file:///D:/Code/psm/.mvn/jvm.config) 文件中添加了 `-Dfile.encoding=UTF-8` 参数
2. 项目 pom.xml 中已配置：
   - [project.build.sourceEncoding](file:///D:/Code/psm/pom.xml#L27-L27) = UTF-8
   - [project.reporting.outputEncoding](file:///D:/Code/psm/pom.xml#L28-L28) = UTF-8
   - [maven.compiler.encoding](file:///D:/Code/psm/pom.xml#L29-L29) = UTF-8

如果仍然出现乱码，建议在执行 Maven 命令前先执行以下命令设置控制台编码：
```cmd
chcp 65001
```

或者使用 IDEA 内置的 Maven 支持来运行命令，避免乱码问题。

## 技术选型

- 后端技术：SpringBoot + Spring Data Jpa + Thymeleaf + Shiro + Jwt + EhCache

- 前端技术：Layui + Jquery  + zTree + Font-awesome

## 全新的项目结构

![项目结构图](https://oscimg.oschina.net/oscnet/4e8e47b3801ba98767dc25a1a6efbb522fe.jpg)

## 功能列表

- 用户管理：用于管理后台系统的用户，可进行增删改查等操作。
- 角色管理：分配权限的最小单元，通过角色给用户分配权限。
- 菜单管理：用于配置系统菜单，同时也作为权限资源。
- 部门管理：通过不同的部门来管理和区分用户。
- 字典管理：对一些需要转换的数据进行统一管理，如：男、女等。
- 行为日志：用于记录用户对系统的操作，同时监视系统运行时发生的错误。
- 文件上传：内置了文件上传接口，方便开发者使用文件上传功能。
- 代码生成：可以帮助开发者快速开发项目，减少不必要的重复操作，花更多精力注重业务实现。
- 表单构建：通过拖拽的方式快速构建一个表单模块。
- 数据接口：根据业务代码自动生成相关的api接口文档

## 安装教程

- ##### 环境及插件要求

   - Jdk8+
   - Mysql5.5+
   - Maven
   - Lombok<font color="red">（重要）</font>

- ##### 导入项目

   - IntelliJ IDEA：Import Project -> Import Project from external model -> Maven
   - Eclipse：Import -> Exising Mavne Project


- ##### 运行项目

  - 通过Java应用方式运行admin模块下的com.linln.admin.BootApplication文件
  - 数据库配置：数据库名称timo   用户root    密码root
  - 访问地址：http://localhost:8080/
  - 默认帐号密码：admin/123456

## 使用说明

1. 使用文档：sdoc/使用文档.docx
2. 开发手册：[TIMO开发文档](http://**************/docs)、[看云文档](https://www.kancloud.cn/timo/timo-doc)
3. SQL文件：sdoc/timo.sql

## 演示地址
演示地址： [http://**************](http://**************)

## 预览图

![项目结构图](https://oscimg.oschina.net/oscnet/584b70844227ad813eb8f10bd452fad015c.jpg)

![登录页面](https://oscimg.oschina.net/oscnet/55b1a88090da20735b67ec91a9bcbafc48a.jpg)

![用户管理](https://oscimg.oschina.net/oscnet/91d2f63ac18b34773ddb7f20b25d0c9c539.jpg)

![菜单管理](https://oscimg.oschina.net/oscnet/ac6c1a0521acb2c22c76130057bd97dfd3f.jpg)

![字典管理](https://oscimg.oschina.net/oscnet/ce428dc1a62c6d591ac6bb5ed10e32caf39.jpg)

![行为日志](https://oscimg.oschina.net/oscnet/8b41f93fad654f81349d9572c1630f6fe1f.jpg)

![代码生成](https://oscimg.oschina.net/oscnet/f355fa74868080440299fa4453e9b7ea399.jpg)