package com.linln.admin.express.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.StatusUtil;
import com.linln.component.excel.annotation.Excel;
import com.linln.modules.system.domain.User;
import lombok.Data;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2022/03/28
 */
@Data
@Entity
@Table(name="or_exinfo")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
@Excel("物流信息")
public class Exinfo implements Serializable {
    // 主键ID
    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    @Excel("id")
    private Long id;
    // 进度
    @Excel("进度")
    private String progress;
    // 发货人 отп-ль
    @Excel("发货人 отп-ль")
    private String sender;
    // 发货人电话тел
    @Excel("发货人电话тел")
    private String senderTel;
    // 货物编码 код
    @Excel("货物编码 код")
    private String cargoCode;
    // 运单号码
    @Excel("运单号码")
    private String expressNo;
    // 货物名称товар
    @Excel("货物名称товар")
    private String cargoName;
    // 货物重量 KG
    @Excel("货物重量 KG")
    private String cargoWeight;
    // 货物体积M3
    @Excel("货物体积M3")
    private String cargoVolume;
    // 箱数cartonNumber
    @Excel("箱数 к-о место")
    private String cartonNumber;
    // 到库日期
    @Excel("到库日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String reachStoreDate;
    // 启运日期дата отп
    @Excel("启运日期дата отп")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String startDate;
    // 运输方式вид транс
    @Excel("运输方式вид транс")
    private String transType;
    // 到货日期
    @Excel("到货日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String arrivalDate;
    // 应收款 сумма
    @Excel("应收款 сумма")
    private String recvAmount;
    // 国外收货人
    @Excel("国外收货人")
    private String foreignRecv;
    // 联系电话
    @Excel("联系电话")
    private String tel;
    // 国内应付
    @Excel("国内应付")
    private String internalAmount;
    // 国外应付 = 国外转运费 + 国外清关费
    @Excel("国外转运费")
    private String outerAmount;
    // 国外应付
    @Excel("国外清关费")
    private String outerClearAmount;
    // 国外应付
    @Excel("国外应付备注")
    private String outerRemark;
    // 业务员
    @Excel("业务员")
    private String oper;
    // 备注
    @Excel("备注")
    private String remark;
    // 创建时间
    @CreatedDate
    @Excel("创建时间")
    private Date createDate;
    // 更新时间
    @LastModifiedDate
    private Date updateDate;
    @Excel("收款日期")
    private String paymentDate;
    @Excel("收款类型")
    private String paymentType;
    @Excel("中国仓储")
    private String interSundryfees;

    @Excel("中国运费")
    private String interShippingFee;
    private Long creator;
    // 创建者
    @CreatedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="create_by")
    @JsonIgnore
    private User createBy;
    // 更新者
    @LastModifiedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="update_by")
    @JsonIgnore
    private User updateBy;
    // 数据状态
    private Byte status = StatusEnum.OK.getCode();

    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reachStoreStartDate;

    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate reachStoreEndDate;

    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateStartDate;

    @Transient
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDateEndDate;



}