<div class="apiDetail">
<div>
	<h2><span>Function(event, treeId, treeNode)</span><span class="path">setting.callback.</span>onCheck</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.excheck</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to capture the check or uncheck event when check or uncheck the checkbox and radio.</p>
			<p class="highlight_red">If you set 'setting.callback.beforeCheck',and return false, zTree will not change check state, and will not trigger the 'onCheck' callback.</p>
			<p>Default: null</p>
		</div>
	</div>
	<h3>Function Parameter Descriptions</h3>
	<div class="desc">
	<h4><b>event</b><span>js event Object</span></h4>
	<p>event Object</p>
	<h4 class="topLine"><b>treeId</b><span>String</span></h4>
	<p>zTree unique identifier: <b class="highlight_red">treeId</b>.</p>
	<h4 class="topLine"><b>treeNode</b><span>JSON</span></h4>
	<p>JSON data object of the node which is checked or unchecked</p>
	</div>
	<h3>Examples of setting & function</h3>
	<h4>1. When check or uncheck the checkbox and radio, alert info about 'tId' and 'name' and 'checked'.</h4>
	<pre xmlns=""><code>function myOnCheck(event, treeId, treeNode) {
    alert(treeNode.tId + ", " + treeNode.name + "," + treeNode.checked);
};
var setting = {
	callback: {
		onCheck: myOnCheck
	}
};
......</code></pre>
</div>
</div>