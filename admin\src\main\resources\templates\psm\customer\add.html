<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="layui-form timo-compile">
    <form th:action="@{/psm/customer/save}">
        <input type="hidden" name="id" th:if="${customer}" th:value="${customer.id}">
        <div class="layui-form-item">
            <label class="layui-form-label">客户信息 Информация о клиентах</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="custOrg" placeholder="请输入客户信息" th:value="${customer?.custOrg}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">业务信息 Деловые данные</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="custDetail" placeholder="请输入业务信息" th:value="${customer?.custDetail}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系人 Контакт</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="contName" placeholder="请输入联系人" th:value="${customer?.contName}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户所在国家 Страна клиента</label>
            <div class="layui-input-inline">
                <select class="timo-search-select" name="custCountry" mo:dict="CUST_LOCAL" mo-selected="${customer?.custCountry}"
                        mo-empty="全部"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户类型 Тип клиента</label>
            <div class="layui-input-inline">
                <select class="timo-search-select" name="custType" mo:dict="CUST_TYPE" mo-selected="${customer?.custType}"
                        mo-empty="全部"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">联系电话 Контактный телефон</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="contPhone" placeholder="请输入联系电话" th:value="${customer?.contPhone}">
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存 держать</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭 закрытие</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
