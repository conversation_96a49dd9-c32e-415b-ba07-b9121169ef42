@charset "UTF-8";
/* 通用样式 */
a{
    cursor: pointer;
}
.required {
	position: relative;
}
.required:after {
	position: absolute;
	transform: translateY(8%);
	right: 4px;
	font-size: 18px;
	color: #ea644a;
	content: '*';
}

/* 滚动条样式 */
::-webkit-scrollbar-track {
	background-color: #F5F5F5;
}
::-webkit-scrollbar {
	width: 6px;
    height: 6px;
	background-color: #F5F5F5;
}
::-webkit-scrollbar-thumb {
	background-color: #999;
}

/* 导航栏样式 */
.layui-layout-admin .layui-header{
	height: 50px;
	background-color: #009688;
}
.layui-layout-admin .layui-header .layui-logo{
	width: 220px;
	color: #ffffff;
	line-height: 50px;
	background-color: #007d71;
	overflow: hidden;
	-webkit-transition: left 0.3s ease, width 0.3s ease;
	-o-transition: left 0.3s ease, width 0.3s ease;
	transition: left 0.3s ease, width 0.3s ease;
}
.layui-layout-admin .layui-header .side-toggle{
	left: 220px;
	display: block;
    padding: 0 20px;
    height: 50px;
    line-height: 50px;
    color: #ffffff;
    -webkit-transition: left 0.3s ease, width 0.3s ease;
	-o-transition: left 0.3s ease, width 0.3s ease;
	transition: left 0.3s ease, width 0.3s ease;
}
.layui-layout-admin .layui-header .side-toggle:hover{
	background-color: #00635a;
}
.layui-layout-admin .layui-header .layui-nav .layui-nav-item{
	line-height: 50px;
}
.layui-layout-admin .layui-header .layui-nav .layui-nav-item>a{
	color: #ffffff;
	padding: 0 16px;
	-webkit-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}
.layui-layout-admin .layui-header .layui-nav .layui-nav-item>a:hover{
	background-color: #038478;
}
.layui-layout-admin .layui-header .layui-nav .layui-nav-item>a>.fa{
	font-size: 18px;
}
.layui-layout-admin .layui-header .layui-nav .layui-this:after{
	background: none;
}
.layui-layout-admin .layui-header .layui-nav .timo-search{
	position: relative;
	margin:0 16px;
}
.layui-layout-admin .layui-header .layui-nav .timo-search-input{
	border: 0;
	width: 190px;
	height: 30px;
	padding: 5px 10px;
	padding-right: 30px;
	border-radius: 2px;
	background-color: rgba(255, 255, 255, 0.8);
}
.layui-layout-admin .layui-header .layui-nav .timo-search-button{
	position: absolute;
	right: 0;
	top: 10px;
	bottom: 10px;
	padding: 0 10px;
	border: 0;
	color: rgba(0, 0, 0, 0.8);
	background: none;
	cursor: pointer;
}
.layui-layout-admin .layui-header .layui-nav .timo-screen-full>.fa{
	position: relative;
	top: 3px;
}
.layui-layout-admin .layui-header .layui-nav .timo-screen-full.full-on>.fa:before{
	content: "\e758";
}
.layui-layout-admin .layui-header .layui-nav .timo-nav-user>a{
	padding-right: 28px;
}
.layui-layout-admin .layui-header .layui-nav .timo-nav-user>a .layui-nav-more{
	right: 12px;
}
.layui-layout-admin .layui-header .layui-nav-bar{
	background-color: rgba(0,0,0,0);
}
.layui-layout-admin .layui-header .layui-nav-child{
	top:55px;
	left: -60px;
	right: 0;
	padding: 0;
	margin-top: 2px;
	text-align: center;
	box-shadow: initial;
	border: initial;
	background-color: initial;
}
.layui-layout-admin .layui-header .timo-nav-child-box{
	float: right;
	min-width: 120px;
	padding: 5px 0;
	box-shadow: 0 2px 4px rgba(0,0,0,.12);
	border: 1px solid #d2d2d2;
	background-color: #fff;
}
.layui-layout-admin .layui-header .layui-nav-child a{
	padding: 0;
}
.layui-layout-admin .layui-header .layui-nav-child .fa{
	margin-right: 10px;
	color: #666666;
}

/* 个人中心页面 */
.user-info-page .user-info{
    position: absolute;
    left: 0;
    width: 180px;
    height: 380px;
    border-right: 1px solid #cccccc;
    padding:0 20px;
}
.user-info-page .user-info .user-avatar-box{
    position: relative;
    margin: 0 auto;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid #d9dde1;
    cursor: pointer;
}
.user-info-page .user-info .user-avatar-box .user-avatar{
    width: 100%;
    height: 100%;
}
.user-info-page .user-info .user-avatar-box .edit-avatar{
    position: absolute;
	left: 0;
    bottom: -34px;
    width: 100%;
    height: 34px;
    line-height: 34px;
    color: #ffffff;
    text-align: center;
    background-color: rgba(0,0,0,0.6);
    transition: bottom 0.2s;
}
.user-info-page .user-info .user-avatar-box:hover .edit-avatar{
    bottom: 0;
}
.user-info-page .user-info .detail-info{
    margin-top: 14px;
    line-height: 1.8;
    font-size: 16px;
}
.user-info-page .user-info .detail-info li{
    padding-left: 12px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    cursor: default;
}
.user-info-page .user-edit{
    position: absolute;
    left: 220px;
    right: 0;
    height: 380px;
    opacity: 0.7;
}
.user-info-page .canvas-panel{
    display: none;
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    cursor: move;
    overflow: hidden;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC);
}
.user-info-page .canvas-bg{
    position: absolute;
}
.user-info-page .canvas-shade{
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.3);
}
.user-info-page .canvas-crop{
    position: absolute;
    top: 50%;
    left: 50%;
    width: 280px;
    height: 280px;
    background-color: #FFFFFF;
    border: 1px solid rgb(40, 121, 255);
    -webkit-transform: translate(-141px, -141px);
    transform: translate(-141px,-141px);
}
.user-info-page .canvas-group{
    position: absolute;
    right: 14px;
    bottom: 10px;
	display: none;
}
.user-info-page .canvas-group .layui-btn-primary:hover{
    opacity: 1;
}

/* 侧边栏 */
.layui-layout-admin .layui-side{
	top: 50px;
	width: 220px;
    background-color: #222d32!important;
    -webkit-transition: left 0.3s ease, width 0.3s ease;
	-o-transition: left 0.3s ease, width 0.3s ease;
	transition: left 0.3s ease, width 0.3s ease;
}
.layui-layout-admin .layui-side .layui-side-scroll{
	width: 240px;
}
.layui-layout-admin .layui-side .layui-nav-tree{
	width: 220px;
}
.layui-layout-admin .layui-side .layui-side-user{
	display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 20px;
    margin-top: 10px;
    margin-bottom: 10px;
    color: #fff;
}
.layui-layout-admin .layui-side .layui-side-user-avatar{
    cursor: pointer;
	-webkit-box-flex: 0;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    margin-right: 15px;
    width: 48px;
    border-radius: 50%;
    -webkit-transition: all 0.2s;
	-o-transition: all 0.2s;
	transition: all 0.2s;
}
.layui-layout-admin .layui-side .layui-side-user-name{
	font-size: 16px;
}
.layui-layout-admin .layui-side .layui-side-user-designation{
    margin-top: 2px;
}
.layui-layout-admin .layui-side .layui-side-user-designation:before{
	content:"";
	display:inline-block;
	width: 10px;
	height: 10px;
	background-color: #5FB878;
	border-radius: 50%;
	margin-right: 2px;
}
.layui-layout-admin .layui-side .layui-nav{
	background-color: #222d32;
}
.layui-layout-admin .layui-side a{
	font-size: 15px;
}
.layui-layout-admin .layui-side a .layui-icon,
.layui-layout-admin .layui-side a .fa{
	display: inline-block;
	font-size: 15px;
	width: 24px;
}
.layui-layout-admin .layui-side a .fa{
	font-size: 16px;
}
.layui-layout-admin .layui-side .layui-nav-item>a:hover{
	background-color: #222d32;
}
.layui-layout-admin .layui-side .layui-nav-child{
	padding: 4px 0;
}
.layui-layout-admin .layui-side .layui-nav-child a{
	padding-left: 44px;
}
.layui-layout-admin .layui-side .layui-nav-child .layui-nav-child a{
	padding-left: 64px;

}

/* 主体区域 */
.layui-layout-admin .layui-body{
    top: 50px;
    left: 220px;
    margin: 0;
	bottom: 0;
	-webkit-transition: left 0.3s ease, width 0.3s ease;
	-o-transition: left 0.3s ease, width 0.3s ease;
	transition: left 0.3s ease, width 0.3s ease;
}
.layui-layout-admin .layui-body .layui-tab-title{
	z-index: 999;
	background-color: #ffffff;
	-webkit-box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
    box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}
.layui-layout-admin .layui-body .layui-tab-title li{
    padding-left: 16px;
    padding-right: 8px;
    border-right: 1px solid #f6f6f6;
}
.layui-layout-admin .layui-body .layui-tab-title li:hover{
    background-color: #f8f8f8;
}
.layui-layout-admin .layui-body .layui-tab-title .layui-this{
    color: #009688;
    background-color: #f8f8f8;
}
.layui-layout-admin .layui-body .layui-tab-title .layui-this:after{
    border-bottom: 2px solid #009688;
}
.layui-layout-admin .layui-body .layui-tab-title .layui-icon-home{
	text-align: left;
	font-size: 15px;
	display: inline-block;
    width: 22px;
}
.layui-layout-admin .layui-body .layui-tab-title .layui-tab-close{
	width: 16px;
	height: 16px;
	line-height: 18px;
    font-weight: bold;
}
.layui-layout-admin .layui-body .layui-tab-title .layui-tab-close:hover{
    background-color: #cccccc;
}
.layui-layout-admin .layui-body .layui-tab-content,
.layui-layout-admin .layui-body .layui-tab-content .layui-tab-item,
.layui-layout-admin .layui-body .layui-tab-content .layui-layout-iframe
{
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}
.layui-layout-admin .layui-body .layui-tab-content{
	top: 40px;
	padding: 0;
	z-index: 998;
}
.layui-layout-admin .layui-body .layui-tab-content .layui-layout-iframe{
	width: 100%;
    height: 100%;
}

/* 其他 */
.layui-layout-admin .layui-header .layui-logo-mini,
.layui-layout-admin .side-toggle .layui-icon-spread-left,
.timo-checkbox input[type="checkbox"]{
	display: none;
}
.timo-checkbox{
    display: block;
    width: 18px;
    height: 18px;
}
.timo-checkbox .layui-icon{
    display: block;
    width: 16px;
    height: 16px;
    line-height: 16px;
    cursor: pointer;
}
.timo-checkbox .layui-icon:before{
    display: block;
    width: 100%;
    height: 100%;
    text-align: center;
    border: 1px solid #d2d2d2;
    font-size: 12px;
    border-radius: 2px;
    color: #fff;
    background-color: #fff;
}
.timo-checkbox input[type="checkbox"]:checked + .layui-icon:before{
    border-color: #009688;
    background-color: #009688;
}

/* 收起侧边栏 */
@media (min-width: 767px) {
	.layui-layout-admin.layui-side-shrink .layui-logo,
	.layui-layout-admin.layui-side-shrink .layui-side {
		width: 50px;
	}
	.layui-layout-admin.layui-side-shrink .layui-side:hover{
		width: 220px;
	}
	.layui-layout-admin.layui-side-shrink .layui-side:hover .layui-nav-title{
		display: inline;
	}
	.layui-layout-admin.layui-side-shrink .layui-header .side-toggle,
	.layui-layout-admin.layui-side-shrink .layui-body {
		left: 50px;
	}
	.layui-layout-admin.layui-side-shrink .layui-side-user {
		padding: 10px;
	}
	.layui-layout-admin.layui-side-shrink .layui-side-user .layui-side-user-avatar {
		width: 30px;
	}
	.layui-layout-admin.layui-side-shrink .layui-side .layui-nav .layui-nav-item a {
		padding: 0 17px;
	}
	.layui-layout-admin.layui-side-shrink .layui-logo-mini,
	.layui-layout-admin.layui-side-shrink .side-toggle .layui-icon-spread-left {
		display: inline;
	}
	.layui-layout-admin.layui-side-shrink .layui-logo-lg,
	.layui-layout-admin.layui-side-shrink .layui-side .layui-nav-title,
	.layui-layout-admin.layui-side-shrink .layui-side .layui-nav-more,
	.layui-layout-admin.layui-side-shrink .layui-side .layui-nav-itemed > .layui-nav-child,
	.layui-layout-admin.layui-side-shrink .layui-side-user-name,
	.layui-layout-admin.layui-side-shrink .layui-side-user-designation,
	.layui-layout-admin.layui-side-shrink .side-toggle .layui-icon-shrink-right {
		display: none;
	}
}

/* 自适应小屏幕 */
@media (max-width: 767px){
	.layui-layout-admin .layui-side{
		left: -220px;
	}
	.layui-layout-admin .layui-body,
	.layui-layout-admin .layui-header .side-toggle{
		left: 0;
	}
	.layui-layout-admin .layui-header .layui-logo{
		display: none;
	}
	.layui-layout-admin.layui-side-shrink .layui-side{
		left: 0;
	}
}

/* 页面通用样式 */
.timo-layout-page{
	background-color: #eeeeee;
	padding: 18px;
}
.timo-layout-page .timo-card-header{
	border-radius: 0;
	border-top: 4px solid #d2d2d2;
	border-bottom: 1px solid #e2e2e2;
}
.timo-layout-page .timo-card-header .refresh-btn{
	position: absolute;
	top:8px;
	right: 8px;
	width: 25px;
	height: 25px;
	line-height: 26px;
	cursor: pointer;
	font-weight: bold;
	text-align: center;
	border-radius: 4px;
}
.timo-layout-page .timo-card-header .refresh-btn:hover{
	background-color: #cccccc;
}
.timo-layout-page .timo-card-screen{
	margin-bottom: 8px;
}
.timo-layout-page .timo-card-screen.put-row .screen-btn-group{
	clear: both;
	float: left;
	width: 100%;
	margin-top: 4px;
}
.timo-layout-page .timo-card-screen .screen-btn-group{
    margin-right: 1px;
}
.timo-layout-page .timo-card-screen .screen-btn-group .layui-btn{
    margin-top: 5px;
    margin-bottom: 5px;
}
.timo-layout-page .timo-card-screen.put-row .screen-btn-group .btn-group-left{
	float: left;
	margin-left: -2px;
}
.timo-layout-page .timo-card-screen.put-row .screen-btn-group .btn-group-left button{
    margin-right: 6px;
}
.timo-layout-page .timo-card-screen.put-row .screen-btn-group .btn-group-right{
	float: right;
}
.timo-search-status{
	width: 70px;
}
.timo-search-status .timo-search-select{
	width: 70px;
	height: 38px;
	padding-left: 12px;
	border-color: #e6e6e6;
	cursor: pointer;
}
.timo-layout-page .layui-form-pane .layui-inline{
    margin: 5px 0;
}
.timo-layout-page .layui-form-pane .layui-form-label{
	width: auto;
	background-color: #fff;
	border-color: #009688;
	color: #009688;
	font-weight: bold;
}
.timo-layout-page .layui-form-pane input,
.timo-layout-page .layui-form-pane select{
	border-color: #009688;
	border-radius: 0 2px 2px 0;
}
.timo-layout-page .layui-form-pane input:hover,
.timo-layout-page .layui-form-pane input:focus{
	border-color: #009688 !important;
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(0, 150, 136, 0.6);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(0, 150, 136, 0.6);
}
.timo-layout-page .layui-form-pane .layui-input-block{
	margin-left: 0px;
	float: left;
}
/* 优化搜索框中select元素的样式，使其与label标签高度对齐 */
.timo-search-box .layui-form-pane .layui-form-label {
    height: 38px;
    line-height: 20px;
    padding: 9px 12px;
    box-sizing: border-box;
}

.timo-search-box .layui-form-pane .layui-input-block {
    height: 38px;
    box-sizing: border-box;
    margin-right: 10px;
}

.timo-search-box .timo-search-select {
    height: 38px;
    padding: 0 10px;
    box-sizing: border-box;
}
.timo-layout-page .screen-btn-group .layui-btn{
	padding: 0 14px;
}
.timo-layout-page .timo-table-wrap{
	overflow-y: auto;
    margin: 5px 0;
}
.timo-layout-page .timo-table{
	color: #000000;
    margin: 0;
}
.timo-layout-page .timo-table.timo-table-fixed{
    table-layout: fixed;
}
.timo-layout-page .timo-table a{
	color: #009688;
}
.timo-layout-page .timo-table a:hover {
	color: #004a43;
	text-decoration: underline;
}
.timo-layout-page .timo-table th,
.timo-layout-page .timo-table td{
	padding: 12px;
	border: 1px solid #dee2e6;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.timo-layout-page .timo-table th{
	vertical-align: bottom;
	border-bottom: 2px solid #dee2e6;
	font-weight: bold;
	background-color: #ffffff;
}
.timo-layout-page .timo-table .timo-table-checkbox{
    width: 20px;
}
.timo-layout-page .timo-table .timo-checkbox{
    margin: 0 1px;
}
.timo-layout-page .timo-table .timo-table-null{
	text-align: center;
}
.timo-layout-page .timo-table .sortable{
    cursor: pointer;
    padding-right: 18px;
    background:#FFFFFF url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAQAAADYWf5HAAAAkElEQVQoz7X QMQ5AQBCF4dWQSJxC5wwax1Cq1e7BAdxD5SL+Tq/QCM1oNiJidwox0355mXnG/DrEtIQ6azioNZQxI0ykPhTQIwhCR+BmBYtlK7kLJYwWCcJA9M4qdrZrd8pPjZWPtOqdRQy320YSV17OatFC4euts6z39GYMKRPCTKY9UnPQ6P+GtMRfGtPnBCiqhAeJPmkqAAAAAElFTkSuQmCC") no-repeat right;
}
.timo-layout-page .timo-table .sortable.asc{
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAZ0lEQVQ4y2NgGLKgquEuFxBPAGI2ahhWCsS/gDibUoO0gPgxEP8H4ttArEyuQYxAPBdqEAxPBImTY5gjEL9DM+wTENuQahAvEO9DMwiGdwAxOymGJQLxTyD+jgWDxCMZRsEoGAVoAADeemwtPcZI2wAAAABJRU5ErkJggg==");
}
.timo-layout-page .timo-table .sortable.desc{
    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAAZUlEQVQ4y2NgGAWjYBSggaqGu5FA/BOIv2PBIPFEUgxjB+IdQPwfC94HxLykus4GiD+hGfQOiB3J8SojEE9EM2wuSJzcsFMG4ttQgx4DsRalkZENxL+AuJQaMcsGxBOAmGvopk8AVz1sLZgg0bsAAAAASUVORK5CYII=");
}

.timo-layout-page .page-info{
	padding-top: 12px;
	vertical-align: center;
}
.timo-layout-page .page-number{
	display: inline-block;
	padding: 3px;
	padding-top: 1px;
	border-radius: 2px;
	cursor: pointer;
	border: 1px solid #e2e2e2;
	height: 24px;
	margin-left: 6px;
}
.btn-group{
    position: relative;
    display: inline-block;
    vertical-align: middle;
}
.caret {
    display: inline-block;
    width: 0;
    height: 0;
    margin-left: 2px;
    vertical-align: middle;
    border-top: 4px dashed;
    border-top: 4px solid\9;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
}
.btn-group .layui-nav-child{
    top: initial;
    left: -20px;
    right: 0;
    margin-top: 2px;
}
.btn-group .layui-nav-child a{
    display: block;
    padding: 0 20px;
}
.btn-group .layui-nav-child a:hover {
    background-color: #f2f2f2;
    color: #000;
}
.btn-group.show .layui-nav-child{
    display: block;
}
.timo-layout-page .screen-btn-group .layui-btn{
    margin-left: 2px;
}
.timo-layout-page .screen-btn-group .btn-group .layui-btn{
    padding-right: 12px;
}
.timo-layout-page .screen-btn-group .btn-group .caret{
    margin-left: 4px;
}

/* 分页样式 */
.pagination {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	padding-left: 0;
	list-style: none;
	border-radius: 0.25rem;
	float: right;
	margin-top: 4px;
	margin-bottom: 8px;
}
.page-link {
	position: relative;
	display: block;
	padding: 8px 14px;
	margin-left: -1px;
	line-height: 1.25;
	color: #009688;
	background-color: #FFF;
	border: 1px solid #dee2e6;
}
.page-link:hover{
	background-color: #dddddd;
}
.page-item:first-child .page-link {
	margin-left: 0;
	border-top-left-radius: 3px;
	border-bottom-left-radius: 3px;
}
.page-item:last-child .page-link {
	border-top-right-radius: 3px;
	border-bottom-right-radius: 3px;
}
.page-item.active .page-link {
	z-index: 1;
	color: #FFF;
	background-color: #009688;
	border-color: #009688;
}
.page-item .breviary{
	font-weight: bold;
}

/* 添加/编辑页面 */
.timo-compile{
	padding-top: 20px;
}
.timo-compile .layui-input-inline{
	width: 240px;
}
.timo-compile .layui-input-inline input:hover,
.timo-compile .layui-input-block input:hover,
.timo-compile .layui-input-inline textarea:hover,
.timo-compile .layui-input-block textarea:hover{
    border-color: #009688!important;
}
.timo-compile .layui-input-info{
    float: left;
    margin-left: 4px;
    height: 38px;
    line-height: 38px;
    color: #747474;
}
.timo-compile .layui-form-text{
	padding-right: 20px;
}
.timo-compile .layui-form-text .layui-textarea{
	max-width: 360px;
}
.timo-compile .layui-form-text .layui-textarea.textarea-fill{
	max-width: initial;
}
.timo-compile .timo-finally{
	border-top: 1px solid #ddd;
	padding: 14px 0 0 0;
    margin: 10px 20px 20px;
}
.timo-compile .timo-finally .layui-btn{
	padding: 0 14px;
}
.timo-compile .timo-finally .btn-secondary{
	color: #FFF;
	background-color: #737c85;
	border-color: #737c85;
}

/* 详细页面 */
.timo-detail-page{
	padding: 12px;
}
.timo-detail-title{
	padding:10px 15px;
	font-weight: bold;
	border: 1px solid #c9ced1;
	border-bottom: none;
}
.timo-detail-table{
	color: #000000;
    margin: 0 0 12px;
}
.timo-detail-table th{
	font-weight: bold;
}
.timo-detail-table th,
.timo-detail-table td{
	border: 1px solid #c9ced1;
}
.timo-detail-table tbody tr:hover{
	background-color: #ffffff;
}

/* 树形数据展示 */
.timo-tree-table tbody{
	visibility: hidden;
}
.timo-nav-tree{
	margin-top: 6px;
	margin-right: 15px;
	border: 1px solid #dee2e6;
	max-height: 590px;
	overflow-y: auto;
}
.timo-nav-tree .layui-card-header{
	border-bottom: 1px solid #dee2e6;
}
.timo-tree .toggle-fill{
	width: 16px;
	display: inline-block;
}
.timo-tree .toggle-icon{
	color: #555555;
	width: 16px;
	cursor: pointer;
	text-align: center;
}
.timo-tree .tree-hidd{
	display: none;
}
.timo-tree .tree-active{
	background-color: #f2f2f2;
}
.timo-tree .ztree span.tree-add{
	margin-left:2px;
	background-position:-144px 0;
	vertical-align:top;
	*vertical-align:middle
}

/* 树形选择器 */
.selectContent{
	display: none;
	position: absolute;
	z-index: 999;
	padding-bottom: 4px;
	overflow-y: scroll;
	max-height: 210px;
	background-color: #FAFAFA;
	border: 1px solid rgb(204, 204, 204);
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.select-tree{
    cursor: pointer;
}
.select-tree + .layui-edge{
	position: absolute;
	right: 10px;
	top: 50%;
	margin-top: -3px;
    border-width: 6px;
    border-top: 6px solid #c2c2c2;

}

/* 上传图片展示 */
.upload-show{
	clear: both;
	overflow: hidden;
	margin-left: 110px;
}
.upload-show .upload-item{
    position: relative;
	float: left;
}
.upload-show .upload-item:after {
    content: '';
    position: absolute;
    top: 12px;
    left: 0;
    right: 12px;
    bottom: 0;
    background-color: rgba(0,0,0,0.25);
}
.upload-show .upload-item.succeed:after{
    display: none;
}
.upload-show .upload-item.error:after {
    background-color: rgba(255, 10, 0, 0.25);
}
.upload-show .upload-item img{
	height: 84px;
    margin-top: 12px;
	margin-right: 12px;
	padding: 8px;
	border: 1px solid #cccccc;
}
.upload-show .upload-item .upload-item-close{
    display: block;
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 999999;
    color: #FFFFFF;
    background-color: rgba(0, 150, 136, 0.69);
    font-size: 11px;
    width: 14px;
    height: 14px;
    text-align: center;
    line-height: 15px;
    cursor: pointer;
}
.upload-show .upload-item .upload-item-close:hover{
    background-color: #009688;
}