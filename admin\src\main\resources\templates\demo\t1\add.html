<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="layui-form timo-compile">
    <form th:action="@{/demo/t1/save}">
        <input type="hidden" name="id" th:if="${t1}" th:value="${t1.id}">
        <div class="layui-form-item">
            <label class="layui-form-label">日期</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="c10" placeholder="请输入日期" th:value="${t1?.c10}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户</label>
            <div class="layui-input-inline">
                <select name="c11" mo:list="${userList}" th:mo-selected="${t1?.c11}" th:value="${t1?.c11}"></select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">货款</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="c3" placeholder="请输入货款" th:value="${t1?.c3}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">运费</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="c4" placeholder="请输入运费" th:value="${t1?.c4}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">其他费用</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="c9" placeholder="请输入其他费用" th:value="${t1?.c9}">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="remark">[[${t1?.remark}]]</textarea>
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
