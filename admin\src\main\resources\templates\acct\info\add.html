<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="layui-form timo-compile">
    <form th:action="@{/acct/info/save}">
        <input type="hidden" name="id" th:if="${info}" th:value="${info.id}">
        <div class="layui-form-item">
            <label class="layui-form-label">余额</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="balance" placeholder="请输入余额" th:value="${info?.balance}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">汇款总金额</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="historyAmount" placeholder="请输入余额" th:value="${info?.historyAmount}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户号</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="userId" placeholder="请输入客户号" th:value="${info?.userId}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="userName" placeholder="请输入客户名称" th:value="${info?.userName}">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="remark">[[${info?.remark}]]</textarea>
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>