package com.linln.admin.foreignOrder.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.StatusUtil;
import com.linln.modules.system.domain.User;
import lombok.Data;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Data
@Entity
@Table(name="or_foreign_order")
@EntityListeners(AuditingEntityListener.class)
@Where(clause = StatusUtil.NOT_DELETE)
public class ForeignOrder implements Serializable {
    // 主键ID
    @Id
    @GeneratedValue(strategy=GenerationType.IDENTITY)
    private Long id;
    // 俄罗斯销售合同号
    private String ruContractNo;
    // 俄罗斯售价
    private BigDecimal ruSalePrice;
    // 俄罗斯收款方
    private String ruPayee;
    // 俄罗斯收款日期
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date ruPaymentDate;
    // 外贸合同号
    private String cnRuContractNo;
    // 采购价
    private BigDecimal purchasePrice;
    // 采购付款日期
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date purchasePaymentDate;
    // 运输方式
    private String transportMode;
    // 运费
    private BigDecimal transportCost;
    // 杂费
    private BigDecimal miscellaneousCost;
    // 报关备注
    private String declarationRemark;
    // 品名
    private String productName;
    // 重量（KG）
    private BigDecimal weight;
    // 体积（M³）
    private BigDecimal volume;
    // 数量
    private BigDecimal quantity;
    // 中国内贸合同号
    private String cnDomesticContractNo;
    // 中国内贸合同价
    private BigDecimal cnDomesticPrice;
    // 中国付款日期
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date cnPaymentDate;
    // 中国付款方
    private String cnPayer;
    // 业务员
    private String operatorName;
    // 创建时间
    @CreatedDate
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    // 更新时间
    @LastModifiedDate
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
    // 数据状态
    private Byte status = StatusEnum.OK.getCode();

    private Long creator;
    // 创建者
    @CreatedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action= NotFoundAction.IGNORE)
    @JoinColumn(name="create_by")
    @JsonIgnore
    private User createBy;
    // 更新者
    @LastModifiedBy
    @ManyToOne(fetch=FetchType.LAZY)
    @NotFound(action=NotFoundAction.IGNORE)
    @JoinColumn(name="update_by")
    @JsonIgnore
    private User updateBy;
}