<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <link rel="stylesheet" th:href="@{/lib/zTree_v3/css/zTreeStyle/zTreeStyle.css}" type="text/css">
</head>
<body>
<div class="layui-form timo-compile">
    <form th:action="@{/system/dept/save}">
        <input type="hidden" name="id" th:if="${dept}" th:value="${dept.id}"/>
        <div class="layui-form-item">
            <label class="layui-form-label required">部门名称</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="title"  placeholder="请输入标题" th:value="${dept?.title}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label required">父级部门</label>
            <div class="layui-input-inline">
                <input class="layui-input select-tree" th:attr="data-url=@{/system/dept/list}, data-value=${pDept?.id}" type="text" name="pid"  placeholder="请输入父级部门" th:value="${pDept?.title}">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-inline">
                <select class="select-sort" name="sort"
                        th:attr="data-url=@{/system/dept/sortList}, data-id=${dept?.id}, data-sort=${dept?.sort}" lay-verify="sort"></select>
            </div>
            <div class="layui-input-info">（之后）</div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea placeholder="请输入内容" class="layui-textarea" name="remark">[[${dept?.remark}]]</textarea>
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
<script type="text/javascript" th:src="@{/js/plugins/jquery-2.2.4.min.js}"></script>
<script type="text/javascript" th:src="@{/lib/zTree_v3/js/jquery.ztree.core.min.js}"></script>
<script type="text/javascript" th:src="@{/js/timoTree.js}"></script>
<script type="text/javascript">
    layui.use(['form'], function () {
        window.form = layui.form;
        // 初始化排序下拉选项
        var pid = $(".select-tree").data('value');
        if (pid != undefined){
            sortRender({id: pid});
        }
    });

    // 初始化下拉树
    $.fn.selectTree({
        rootTree: '顶级',
        // 选中后事件
        onSelected: sortRender
    });

    // 更新渲染排序下拉选项
    function sortRender(treeNode) {
        var pid = treeNode.id;
        var sort = $(".select-sort");
        var id = sort.data('id') ? sort.data('id') : 0;
        var url = sort.data('url') + "/" + pid + "/" + id;
        $.get(url, function (result) {
            var options = '';
            var sortNum = Object.keys(result).length;
            if(pid === $(".select-tree").data('value') && sort.data('sort')){
                sortNum = sort.data('sort') - 1;
            }
            result[0] = "首位";
            for(var key in result){
                var selected = sortNum == key ? "selected=''" : "";
                options += "<option value='"+ key +"' " + selected + ">"+ result[key] +"</option>";
            }
            sort.html(options);
            form.render('select');
        });
    }
</script>
</body>
</html>