<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .layui-inline {
            margin-right: 10px !important;
        }

        .date-range-group {
            display: flex;
            align-items: center;
        }

        .date-range-group .layui-form-label {
            width: auto;
            padding-right: 10px;
        }

        .date-range-group .layui-input-inline {
            width: 140px;
        }

        .date-range-group .layui-form-mid {
            padding: 0 6px;
            color: #666;
            white-space: nowrap;
        }
        .select-payment {
            height: 38px;
            padding: 0 10px;
            border: 1px solid #e6e6e6;
            border-radius: 2px;
        }
    </style>
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 物流管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        <a th:href="@{/express/exinfo/export}"><i class="fa fa-download"></i></a>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">发货人отп-ль</label>
                    <div class="layui-input-block">
                        <input type="text" name="sender" th:value="${param.sender}" placeholder="请输入发货人 отп-ль"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">运单编号NO</label>
                    <div class="layui-input-block">
                        <input type="text" name="expressNo" th:value="${param.expressNo}" placeholder="运单编号"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">货物编码код</label>
                    <div class="layui-input-block">
                        <input type="text" name="cargoCode" th:value="${param.cargoCode}"
                               placeholder="请输入货物编码 код" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">业务员менеджер</label>
                    <div class="layui-input-block">
                        <input type="text" name="oper" th:value="${param.oper}" placeholder="请输入业务员"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline date-range-group">
                    <label class="layui-form-label">到库日期дата получ.КНР</label>
                    <div class="layui-input-inline">
                        <input type="text" id="reachStoreStartDate" name="reachStoreStartDate"
                               th:value="${param.reachStoreStartDate}" class="layui-input" placeholder="起始日期">
                    </div>
                    <div class="layui-form-mid">至</div>
                    <div class="layui-input-inline">
                        <input type="text" id="reachStoreEndDate" name="reachStoreEndDate"
                               th:value="${param.reachStoreEndDate}" class="layui-input" placeholder="结束日期">
                    </div>

                    <label class="layui-form-label" style="margin-left: 10px">启运日期дата отп</label>
                    <div class="layui-input-inline">
                        <input type="text" id="startDateStartDate" name="startDateStartDate"
                               th:value="${param.startDateStartDate}" class="layui-input" placeholder="起始日期">
                    </div>
                    <div class="layui-form-mid">至</div>
                    <div class="layui-input-inline">
                        <input type="text" id="startDateEndDate" name="startDateEndDate"
                               th:value="${param.startDateEndDate}" class="layui-input" placeholder="结束日期">
                    </div>

                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">收款类型тип оплата</label>
                    <div class="layui-input-inline ">
                        <select name="paymentType" class="select-payment">
                            <option value="">请选择</option>
                            <option value="DOMESTIC_COMPANY" th:selected="${param.paymentType == 'DOMESTIC_COMPANY'}">中国公司 Китай Юр</option>
                            <option value="DOMESTIC_PERSONAL" th:selected="${param.paymentType == 'DOMESTIC_PERSONAL'}">中国现金 нал Китай</option>
                            <option value="INTERNATIONAL_COMPANY" th:selected="${param.paymentType == 'INTERNATIONAL_COMPANY'}">俄罗斯公司 юр РФ</option>
                            <option value="INTERNATIONAL_PERSONAL" th:selected="${param.paymentType == 'INTERNATIONAL_PERSONAL'}">俄罗斯现金 нал РФ</option>
                        </select>
                    </div>
                </div>


                <button class="layui-btn timo-search-btn" style="margin-left: 10px">
                    <i class="fa fa-search"></i>
                </button>

            </div>
            <div class="pull-right screen-btn-group">
                <button class="layui-btn open-popup" data-title="添加物流管理" th:attr="data-url=@{/express/exinfo/add}"
                        data-size="auto">
                    <i class="fa fa-plus"></i> 添加
                </button>
                <div class="btn-group">
                    <button class="layui-btn">操作<span class="caret"></span></button>
                    <dl class="layui-nav-child layui-anim layui-anim-upbit">
                        <dd><a class="ajax-status" th:href="@{/express/exinfo/status/delete}">删除</a></dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th>进度<br /> статус </th>
                    <th>货物编码 <br />код</th>
                    <th>运单号码<br />№ AWB/auto</th>
                    <th>发货人 <br />отп-ль</th>
                    <th>货物名称<br />товар</th>
                    <th>货物重量<br /> KG</th>
                    <th>货物体积<br />M3</th>
                    <th>箱数<br />к-о место</th>
                    <th>到库日期<br />дата получ.КНР</th>
                    <th>启运日期 <br />дата отп</th>
                    <th>运输方式<br />вид транс</th>
                    <th>到货日期<br />дата Приб.РФ</th>
                    <th>应收款$<br /> сумма $</th>
                    <th>国外收货人<br />получатель</th>
                    <th>联系电话<br />телефон</th>
                    <th>中国仓储<br />ПРР Китай</th>
                    <th>中国运输<br />дост по Китаю</th>
                    <th>中国支出<br />затрат китай</th>
                    <th>俄罗斯转运费<br />ПРР РФ</th>
                    <th>俄罗斯清关费<br />расх-Тамож</th> 
                    <th>业务员<br />менеджер</th>
                    <th>收款日期<br />дата оплата</th>
                    <th>收款类型<br />тип оплата</th>
					<th>付款备注<br /> комент оплаты</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>

  <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.progress}">进度 статус</td>
                    <td th:text="${item.cargoCode}">货物编码 код</td>
                    <td th:text="${item.expressNo}">运单号码№ AWB/auto</td>
                    <td th:text="${item.sender}">发货人 отп-ль</td>
                    <td th:text="${item.cargoName}">货物名称товар</td>
                    <td th:text="${item.cargoWeight}">货物重量 KG</td>
                    <td th:text="${item.cargoVolume}">货物体积M3</td>
                    <td th:text="${item.cargoVolume}">箱数к-о место</td>
                    <td th:text="${item.reachStoreDate}">到库日期дата получ.КНР</td>
                    <td th:text="${item.startDate}">启运日期дата отп</td>
                    <td th:text="${item.transType}">运输方式вид транс</td>
                    <td th:text="${item.arrivalDate}">到货日期</td>
                    <td th:text="${item.recvAmount}">总应收款$ сумма</td>
                    <td th:text="${item.foreignRecv}">国外收货人</td>
                    <td th:text="${item.tel}">联系电话телефон</td>
                    <td th:text="${item.interSundryfees}">中国仓储费</td>
                    <td th:text="${item.interShippingFee}">中国运费</td>
                    <td th:text="${item.internalAmount}">中国支出затрат китай</td>
                    <td th:text="${item.outerAmount}">俄罗斯转运费</td>
                    <td th:text="${item.outerClearAmount}">俄罗斯清关费</td>
                    <td th:text="${item.oper}">业务员</td>
                    <td th:text="${item.paymentDate}">收款日期дата оплата</td>
                    <td th:switch="${item.paymentType}">
                        <span th:case="'DOMESTIC_COMPANY'">中国公司 Китай Юр </span>
                        <span th:case="'DOMESTIC_PERSONAL'">中国现金 нал Китай</span>
                        <span th:case="'INTERNATIONAL_COMPANY'">俄罗斯公司 юр РФ </span>
                        <span th:case="'INTERNATIONAL_PERSONAL'">俄罗斯现金 нал РФ</span>
                    </td>
                       <td th:text="${item.outerRemark}">付款备注комент оплаты</td>
                    <td>
                        <a class="open-popup" data-title="编辑物流管理"
                           th:attr="data-url=@{'/express/exinfo/edit/'+${item.id}}" data-size="auto" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息"
                           th:attr="data-url=@{'/express/exinfo/detail/'+${item.id}}" data-size="800,600"
                           href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除"
                           th:href="@{/express/exinfo/status/delete(ids=${item.id})}">删除</a>
                    </td>
                </tr>

                </tbody>
            </table>
        </div>
        <div class="layui-row" style="padding: 6px 10px; font-size: 13px; color: #333;">
    <span style="margin-right: 20px;">
        总应收款：<b th:text="${summary != null ? #numbers.formatDecimal(summary.recvAmount, 1, 2) : '0.00'}">0.00</b>$
    </span>
            <span style="margin-right: 20px;">
        未到账款：<b th:text="${summary != null ? #numbers.formatDecimal(summary.recvUnpaid, 1, 2) : '0.00'}">0.00</b>$
    </span>
            <span style="margin-right: 20px;">
        国内应付：<b th:text="${summary != null ? #numbers.formatDecimal(summary.internalAmount, 1, 2) : '0.00'}">0.00</b>$
    </span>
            <span>
        国外应付：<b th:text="${summary != null ? #numbers.formatDecimal(summary.outerAmountTotal, 1, 2) : '0.00'}">0.00</b>$
    </span>
        </div>

        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
<script>
    layui.use('laydate', function () {
        var laydate = layui.laydate;
        // 初始化所有日期选择器
        laydate.render({
            elem: '#reachStoreStartDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
        laydate.render({
            elem: '#reachStoreEndDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
        laydate.render({
            elem: '#startDateStartDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
        laydate.render({
            elem: '#startDateEndDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
    });
</script>
</body>
</html>