package com.linln.admin.psm.service.impl;

import com.linln.admin.psm.domain.GoodsInfo;
import com.linln.admin.psm.repository.GoodsInfoRepository;
import com.linln.admin.psm.service.GoodsInfoService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GoodsInfoServiceImpl implements GoodsInfoService {
    @Autowired
    private GoodsInfoRepository goodsInfoRepository;

    @Override
    public Page<GoodsInfo> getPageList(Example<GoodsInfo> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return goodsInfoRepository.findAll(example, page);
    }

    @Override
    public GoodsInfo getById(Long id) {
        return goodsInfoRepository.findById(id).orElse(null);
    }

    @Override
    public GoodsInfo save(GoodsInfo goodsInfo) {
        return goodsInfoRepository.save(goodsInfo);
    }

    @Override
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return goodsInfoRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }
}
