<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>check_Focus</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.excheck</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Used to record the status which the checkbox or radio get focus. It is valid when <span class="highlight_red">[setting.check.enable = true]</span></p>
			<p class="highlight_red">Do not initialize or modify it, it is an internal argument.</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p>true means: mouse move over the checkbox</p>
	<p>false means: mouse move out the checkbox</p>
	</div>
</div>
</div>