layui.define(['jquery'], function(exports){
    var $ = layui.jquery;
    
    // 创建Autocomplete构造函数
    var Autocomplete = function(options) {
        this.config = $.extend({
            elem: '',                 // 输入框元素
            hidden: '',               // 隐藏域元素（可选）
            url: '',                  // 数据请求地址
            field: 'name',            // 显示字段名
            valueField: 'id',         // 值字段名
            minLength: 1,             // 最小触发长度
            delay: 300,               // 延迟时间（毫秒）
            template: null,           // 自定义模板函数
            processData: null,        // 数据处理函数
            onSelect: null            // 选中回调函数
        }, options);
        
        this.elem = $(this.config.elem);
        this.target = this.config.target ? $(this.config.target) : null;
        this.hidden = this.config.hidden ? $(this.config.hidden) : null;
        this.timer = null;
        this.suggestions = null;
        
        this.init();
    };
    
    // 初始化方法
    Autocomplete.prototype.init = function() {
        var that = this;
        
        // 创建建议列表容器
        this.suggestions = $('<div class="layui-autocomplete">');
        
        // 将建议列表添加到输入框后面
        this.elem.after(this.suggestions);
        
        // 绑定事件
        this.bindEvents();
    };
    
    // 绑定事件
    Autocomplete.prototype.bindEvents = function() {
        var that = this;
        
        // 输入事件处理
        this.elem.on('input', function() {
            clearTimeout(that.timer);
            that.timer = setTimeout(function() {
                that.search();
            }, that.config.delay);
        });
        
        // 失去焦点时隐藏建议列表
        this.elem.on('blur', function() {
            setTimeout(function() {
                that.suggestions.hide();
            }, 150);
        });
        
        // 键盘事件处理
        this.elem.on('keydown', function(e) {
            that.handleKeydown(e);
        });
        
        // 点击建议项处理
        this.suggestions.on('click', 'li', function() {
            that.selectItem($(this));
        });
    };
    
    // 搜索方法
    Autocomplete.prototype.search = function() {
        var that = this;
        var keyword = this.elem.val().trim();

        // 优化逻辑：只要用户输入触发了新的搜索，就立即清空上一次的隐藏值。
        // 这确保了ID值只有在用户从列表中明确选择时才有效。
        if (that.hidden) {
            that.hidden.val('');
        }
        
        // 如果关键字长度不足，隐藏建议列表
        if(keyword.length < this.config.minLength) {
            this.suggestions.hide();
            return;
        }
        
        // 发送AJAX请求获取数据
        $.get(this.config.url + '?keyword=' + encodeURIComponent(keyword), function(res) {
            // 处理响应数据
            var data = [];
            if (res.code === 200) {
                // 如果定义了数据处理函数，则使用它处理数据
                if (typeof that.config.processData === 'function') {
                    data = that.config.processData(res.data);
                } else {
                    // 默认处理方式：直接使用data字段
                    data = res.data || [];
                }
                
                if(data.length > 0) {
                    that.showSuggestions(data);
                } else {
                    that.suggestions.hide();
                }
            } else {
                that.suggestions.hide();
            }
        }).fail(function() {
            that.suggestions.hide();
        });
    };
    
    // 显示建议列表
    Autocomplete.prototype.showSuggestions = function(data) {
        var that = this;
        
        // 清空建议列表
        this.suggestions.empty();
        
        // 添加建议项
        $.each(data, function(index, item) {
            var li = $('<li>').data('item', item);
            
            // 如果定义了自定义模板，则使用它渲染列表项
            if (typeof that.config.template === 'function') {
                li.append(that.config.template(item));
            } else {
                // 默认模板
                var displayText = item[that.config.field] || '';
                li.append($('<div>').text(displayText));
                
                // 如果有描述信息，则添加描述行
                if (item.description) {
                    var descRow = $('<div>').addClass('autocomplete-desc-row')
                                           .css({
                                               fontSize: '12px',
                                               color: '#666',
                                               marginTop: '2px'
                                           });
                    descRow.append($('<span>').text(item.description));
                    li.append(descRow);
                }
            }
            
            that.suggestions.append(li);
        });
        
        // 添加选中样式
        this.suggestions.find('li').hover(function() {
            $(this).addClass('selected');
        }, function() {
            $(this).removeClass('selected');
        });
        
        // 显示建议列表
        this.suggestions.show();
        
        // 设置建议列表位置
        var offset = this.elem.position();
        var height = this.elem.outerHeight();
        this.suggestions.css({
            position: 'absolute',
            top: offset.top + height,
            left: offset.left,
            width: this.elem.outerWidth(),
            zIndex: 999,
            background: '#fff',
            border: '1px solid #e6e6e6',
            borderTop: 'none',
            maxHeight: '300px',
            overflowY: 'auto'
        });
        
        // 为建议项添加样式
        this.suggestions.find('li').css({
            padding: '8px 15px',
            cursor: 'pointer',
            borderBottom: '1px solid #f2f2f2'
        });
        
        // 为最后一个建议项移除边框
        this.suggestions.find('li:last-child').css({
            borderBottom: 'none'
        });
        
        // 为选中的建议项添加样式
        var existingStyle = $('#layui-autocomplete-style');
        if (existingStyle.length === 0) {
            var style = $('<style id="layui-autocomplete-style">').text('.layui-autocomplete li.selected { background-color: #f2f2f2; }');
            $('head').append(style);
        }
    };
    
    // 处理键盘事件
    Autocomplete.prototype.handleKeydown = function(e) {
        var items = this.suggestions.find('li');
        var selected = this.suggestions.find('li.selected');
        
        if(items.length === 0) return;
        
        // 向下箭头
        if(e.keyCode === 40) {
            e.preventDefault();
            var next = selected.length ? selected.next() : items.first();
            if(next.length) {
                selected.removeClass('selected');
                next.addClass('selected');
            }
        }
        // 向上箭头
        else if(e.keyCode === 38) {
            e.preventDefault();
            var prev = selected.length ? selected.prev() : items.last();
            if(prev.length) {
                selected.removeClass('selected');
                prev.addClass('selected');
            }
        }
        // 回车键
        else if(e.keyCode === 13) {
            e.preventDefault();
            if(selected.length) {
                this.selectItem(selected);
            }
        }
        // ESC键
        else if(e.keyCode === 27) {
            this.suggestions.hide();
        }
    };
    
    // 选择项目
    Autocomplete.prototype.selectItem = function(item) {
        var data = item.data('item');
        var value = data[this.config.valueField];
        var displayText = data[this.config.field];
        
        // 填充表单字段
        this.elem.val(displayText);
        
        if(this.hidden) {
            this.hidden.val(value);
        }
        
        if(this.target) {
            this.target.val(displayText);
        }
        
        // 隐藏建议列表
        this.suggestions.hide();
        
        // 执行回调函数
        if(typeof this.config.onSelect === 'function') {
            this.config.onSelect(data);
        }
    };
    
    // 导出模块
    exports('autocomplete', {
        render: function(options) {
            return new Autocomplete(options);
        }
    });
});
