package com.linln.admin.express.repository;

import com.linln.admin.express.domain.Exinfo;
import com.linln.modules.system.repository.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/28
 */
public interface ExinfoRepository extends BaseRepository<Exinfo, Long> {

}