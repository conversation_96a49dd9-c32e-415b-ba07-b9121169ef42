package com.linln.admin.psm.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.linln.admin.psm.domain.Contract;
import com.linln.admin.psm.service.ContractService;
import com.linln.admin.psm.validator.ContractValid;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.vo.ResultVo;
import com.linln.component.excel.ExcelUtil;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/07/25
 */
@Slf4j
@Controller
@RequestMapping("/psm/contract")
public class ContractController {

    @Autowired
    private ContractService contractService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("psm:contract:index")
    public String index(Model model, Contract contract,
                        @RequestParam(value = "createDateStr", required = false) String createDateStr,
                        @RequestParam(value = "paymentDueDateStr", required = false) String paymentDueDateStr) {
        log.info("搜索采购合同: {}, createDateStr: {}, paymentDueDateStr: {}",
                JSONUtil.toJsonStr(contract), createDateStr, paymentDueDateStr);

        // 构建查询规则
        Specification<Contract> spec = buildSpecification(contract, createDateStr, paymentDueDateStr);

        // 5. 调用新的 Service 方法进行查询
        Page<Contract> list = contractService.getPageList(spec);

        // 封装数据
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        // 将日期参数也传回前端，用于回显
        model.addAttribute("createDateStr", createDateStr);
        model.addAttribute("paymentDueDateStr", paymentDueDateStr);
        return "/psm/contract/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("psm:contract:add")
    public String toAdd(Model model) {
        Contract contract = contractService.newInstance();
        log.info("去新增采购合同: {}", contract);
        model.addAttribute("contract", contract);
        return "/psm/contract/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("psm:contract:edit")
    public String toEdit(@PathVariable("id") Contract contract, Model model) {
        log.info("编辑采购合同: {}", contract);
        model.addAttribute("contract", contract);
        return "/psm/contract/add";
    }

    /**
     * 保存添加/修改的数据
     *
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"psm:contract:add", "psm:contract:edit"})
    @ResponseBody
    public ResultVo save(@Validated ContractValid valid, Contract contract) {
        log.info("保存采购合同: {}", contract);
        // 复制保留无需修改的数据
        if (contract.getId() != null) {
            Contract beContract = contractService.getById(contract.getId());
            EntityBeanUtil.copyProperties(beContract, contract);
        }

        // 保存数据
        contractService.save(contract);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("psm:contract:detail")
    public String toDetail(@PathVariable("id") Contract contract, Model model) {
        model.addAttribute("contract", contract);
        return "/psm/contract/detail";
    }

    /**
     * 搜索合同信息，用于autocomplete功能
     */
    @GetMapping("/search")
    @ResponseBody
    public ResultVo search(@RequestParam("keyword") String keyword) {
        // 使用自定义JPQL查询实现OR连接的模糊搜索
        Long salesUserId = null;
        if (!ShiroUtil.verifyRole("boss")) {
            User currUser = ShiroUtil.getSubject();
            salesUserId = currUser.getId();
        }

        List<Contract> contracts = contractService.searchByKeyword(keyword, salesUserId, PageRequest.of(0, 10));
        return ResultVoUtil.success(contracts);
    }

    /**
     * 获取统计数据
     */
    @GetMapping("/statistics")
    @RequiresPermissions("psm:contract:index")
    @ResponseBody
    public ResultVo getStatistics(Contract contract,
                                  @RequestParam(value = "createDateStr", required = false) String createDateStr,
                                  @RequestParam(value = "paymentDueDateStr", required = false) String paymentDueDateStr) {
        // 构建查询规则
        Specification<Contract> spec = buildSpecification(contract, createDateStr, paymentDueDateStr);

        // 获取统计数据
        return ResultVoUtil.success(contractService.getStatistics(spec));
    }

    /**
     * 构建查询规则
     */
    private Specification<Contract> buildSpecification(Contract contract, String createDateStr, String paymentDueDateStr) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 非老板角色只能查看自己的数据
            if (!ShiroUtil.verifyRole("boss")) {
                User currUser = ShiroUtil.getSubject();
                predicates.add(cb.equal(root.get("salesMan"), currUser));
            }

            // 模糊查询：单据号
            if (StrUtil.isNotBlank(contract.getOrderNo())) {
                predicates.add(cb.like(root.get("orderNo"), "%" + contract.getOrderNo() + "%"));
            }

            // 模糊查询：合同编号
            if (StrUtil.isNotBlank(contract.getContractNo())) {
                predicates.add(cb.like(root.get("contractNo"), "%" + contract.getContractNo() + "%"));
            }

            // 其他精确匹配的查询条件可以继续在这里添加，例如：
            if (contract.getPayeeType() != null) {
                predicates.add(cb.equal(root.get("payeeType"), contract.getPayeeType()));
            }

            // 修改后的负责人（俄）模糊查询逻辑
            if (contract.getRuCustomer() != null && StrUtil.isNotBlank(contract.getRuCustomer().getContName())) {
                predicates.add(cb.like(root.join("ruCustomer").get("contName"), "%" + contract.getRuCustomer().getContName() + "%"));
            }

            // 修改后的负责人（中）模糊查询逻辑
            if (contract.getCnCustomer() != null && StrUtil.isNotBlank(contract.getCnCustomer().getContName())) {
                predicates.add(cb.like(root.join("cnCustomer").get("contName"), "%" + contract.getCnCustomer().getContName() + "%"));
            }

            // 日期范围查询：创建时间
            if (StrUtil.isNotBlank(createDateStr)) {
                String[] createDateArr = createDateStr.split(" - ");
                Date startDate = DateUtil.parse(createDateArr[0]);
                Date endDate = DateUtil.parse(createDateArr[1]);
                predicates.add(cb.between(root.get("createDate"), startDate, DateUtil.endOfDay(endDate)));
            }

            // 日期范围查询：采购付款日期
            if (StrUtil.isNotBlank(paymentDueDateStr)) {
                String[] paymentDueDateArr = paymentDueDateStr.split(" - ");
                Date startDate = DateUtil.parse(paymentDueDateArr[0]);
                Date endDate = DateUtil.parse(paymentDueDateArr[1]);
                predicates.add(cb.between(root.get("paymentDueDate"), startDate, endDate));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}
