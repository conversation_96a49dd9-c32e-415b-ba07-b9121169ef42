<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .customer-autocomplete {
            position: relative;
        }

        .customer-info-row {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .form-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .form-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #009688;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
        }

        .form-col {
            flex: 1;
            min-width: 300px;
            padding: 0 10px;
        }

        .layui-form-item {
            margin-bottom: 15px;
        }

        .form-buttons {
            text-align: center;
            padding: 20px 0;
        }

        .form-buttons .layui-btn {
            margin: 0 10px;
            padding: 0 30px;
        }

        .form-buttons .ajax-submit {
            background-color: #009688;
        }

        .form-buttons .close-popup {
            background-color: #777;
        }

        .readonly-field {
            background-color: #f5f5f5 !important;
            cursor: not-allowed;
        }

        .readonly-field:focus {
            background-color: #f5f5f5 !important;
        }

        .amount-field {
            text-align: right;
        }

        .field-error {
            color: #ff5722;
            font-size: 12px;
            margin-top: 2px;
        }

        .field-success {
            color: #4caf50;
        }
    </style>
</head>

<body>
    <div class="layui-form timo-compile">
        <form th:action="@{/psm/goodsInfo/save}">
            <input type="hidden" name="id" th:if="${goodsInfo}" th:value="${goodsInfo.id}">
            <div class="layui-form-item">
                <label class="layui-form-label">单据号 код-данных</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" id="orderNoSearch" placeholder="请输入单据号进行搜索"
                        th:value="${goodsInfo?.orderNo}" autocomplete="off"
                        th:readonly="${goodsInfo?.orderNo != null && goodsInfo?.orderNo != ''}"
                        th:classappend="${goodsInfo?.orderNo != null && goodsInfo?.orderNo != ''} ? 'readonly-field' : ''">
                    <input type="hidden" name="orderNo" th:value="${goodsInfo?.orderNo}" id="orderNo">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">货物编码 код товара</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="goodsNo" placeholder="请输入货物编码"
                        th:value="${goodsInfo?.goodsNo}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">货物名称 наименование товара</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="goodsName" placeholder="请输入货物名称"
                        th:value="${goodsInfo?.goodsName}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">货物信息 инфо о грузе</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="goodsDesc" placeholder="请输入货物信息"
                        th:value="${goodsInfo?.goodsDesc}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">重量（KG） вес (кг)</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="weight" lay-affix="number" lay-precision="2" step="0.01" min="0" max="999999999" placeholder="请输入重量（KG）"
                        th:value="${goodsInfo?.weight}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">体积（m³） объем (м³)</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="volume" lay-affix="number" lay-precision="2" step="0.01" min="0" max="999999999" placeholder="请输入体积（m³）"
                        th:value="${goodsInfo?.volume}">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">数量 количество</label>
                <div class="layui-input-inline">
                    <input class="layui-input" type="text" name="quantity" lay-affix="number" lay-precision="0" step="1" min="0" max="999999999" placeholder="请输入数量"
                        th:value="${goodsInfo?.quantity}">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注 Примечание</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入内容" class="layui-textarea"
                        name="remark">[[${goodsInfo?.remark}]]</textarea>
                </div>
            </div>
            <div class="layui-form-item timo-finally">
                <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存 держать</button>
                <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭 закрытие</button>
            </div>
        </form>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.config({
            base: '/js/'
        }).extend({
            autocomplete: 'autocomplete'
        });
        layui.use(['jquery', 'autocomplete', 'laydate', 'form'], function () {
            var $ = layui.jquery;
            var autocomplete = layui.autocomplete;
            var laydate = layui.laydate;
            var form = layui.form;

            // 初始化合同autocomplete
            autocomplete.render({
                elem: '#orderNoSearch',
                hidden: '#orderNo',
                url: '/psm/contract/search',
                field: 'orderNo',
                valueField: 'orderNo',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.orderNo || '') + '-' + (item.contractNo || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected contract:', data);
                }
            });
        });
    </script>
</body>

</html>
