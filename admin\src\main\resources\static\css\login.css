@charset "UTF-8";
.layui-layout-login .login-bg{
    background-color: #e7e7e7;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}
.layui-layout-login .login-bg .cover{
    background-color: #009688;
    height: 50%;
}
.layui-layout-login .login-content{
    width:250px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform:translate(-50%,-50%);
    background-color: #ffffff;
    padding: 40px;
    padding-top: 32px;
    -webkit-box-shadow: 0px 3px 20px 3px rgba(0, 0, 0, 0.15);
    box-shadow: 0px 3px 20px 3px rgba(0, 0, 0, 0.15);
}
.layui-layout-login .login-content.captcha{
    width:300px;
    padding-bottom: 38px;
}
.layui-layout-login .login-content.captcha .captcha-item .layui-icon{
    font-size: 16px;
}
.layui-layout-login .login-content.captcha .captcha-item .layui-input{
    float: left;
    width: 180px;
}
.layui-layout-login .login-content.captcha .captcha-item .captcha-img{
    float: right;
    height: 38px;
    cursor: pointer;
}
.layui-layout-login .login-box-title{
    font-size: 26px;
    margin-bottom: 20px;
    text-align: center;
    color: #444444;
}
.layui-layout-login .layui-form-item{
    position: relative;
    margin-bottom: 20px;
    min-height: 18px;
}
.layui-layout-login .layui-form-item label{
    position: absolute;
    top:0;
    left: 0;
    font-size: 18px;
    width: 38px;
    line-height: 38px;
    text-align: center;
    color: #999999;
}
.layui-layout-login .layui-form-item input[type=text],
.layui-layout-login .layui-form-item input[type=password]{
    padding-left: 36px;
    border: 1px solid #ddd;
    transition: all 0s;
    -webkit-transition: all 0s;
}
.layui-layout-login .layui-form-item .layui-form-checkbox{
    margin-top: 0;
}
.layui-layout-login .layui-form-item .layui-form-checkbox .layui-icon{
    width: 14px;
    height: 14px;
    top: 1px;
    line-height: 14px;
}
.layui-layout-login .layui-form-item .layui-form-checkbox[lay-skin=primary]:hover i {
    border-color: #009688;
}
.layui-layout-login .layui-form-item .layui-form-checked[lay-skin=primary] i{
    border-color: #009688;
    background-color: #009688;
}
.layui-layout-login .layui-form-item .forget-password{
    color: #009688;
}
.layui-layout-login .login-page-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0!important;
    background-color: rgba(255, 255, 255, 0.3)!important;
}