package com.linln.admin.foreignOrder.validator;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Data
public class ForeignOrderValid implements Serializable {
    @NotEmpty(message = "俄罗斯销售合同号不能为空")
    private String ruContractNo;
    @Digits(integer = 12, fraction = 2, message = "俄罗斯售价不是数字")
    @NotNull(message = "俄罗斯售价不能为空")
    private BigDecimal ruSalePrice;
    @NotEmpty(message = "俄罗斯收款方不能为空")
    private String ruPayee;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date ruPaymentDate;
    @NotEmpty(message = "外贸合同号不能为空")
    private String cnRuContractNo;
    @Digits(integer = 12, fraction = 2, message = "采购价不是数字")
    @NotNull(message = "采购价不能为空")
    private BigDecimal purchasePrice;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date purchasePaymentDate;
    @Digits(integer = 12, fraction = 2, message = "运费不是数字")
    private BigDecimal transportCost;
    @Digits(integer = 12, fraction = 2, message = "杂费不是数字")
    private BigDecimal miscellaneousCost;
    @Digits(integer = 12, fraction = 2, message = "重量（KG）不是数字")
    private BigDecimal weight;
    @Digits(integer = 12, fraction = 2, message = "体积（M³）不是数字")
    private BigDecimal volume;
    @Digits(integer = 12, fraction = 2, message = "数量不是数字")
    private BigDecimal quantity;
    @Digits(integer = 12, fraction = 2, message = "中国内贸合同价不是数字")
    private BigDecimal cnDomesticPrice;
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date cnPaymentDate;
    private String cnPayer;
    @NotEmpty(message = "业务员不能为空")
    private String operatorName;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}