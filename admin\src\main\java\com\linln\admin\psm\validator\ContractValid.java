package com.linln.admin.psm.validator;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import com.linln.admin.psm.domain.Customer;

import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Data
public class ContractValid implements Serializable {

    @NotEmpty(message = "单据号不能为空")
    private String orderNo;

    @NotNull(message = "收款日期不能为空")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date collectionDate;

    @NotEmpty(message = "合同状态不能为空")
    private String contractStatus;

    @NotNull(message = "付款日期不能为空")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date paymentDueDate;

    @NotNull(message = "负责人（俄方）不能为空")
    private Customer ruCustomer;

    @NotNull(message = "负责人（中方）不能为空")
    private Customer cnCustomer;
}
