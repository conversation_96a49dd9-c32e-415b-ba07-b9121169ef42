package com.linln.admin.acct.service;

import com.linln.admin.acct.domain.Info;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
public interface InfoService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<Info> getPageList(Example<Info> example);


    Info getInfoByUserId(Long userId);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    Info getById(Long id);

    /**
     * 保存数据
     * @param info 实体对象
     */
    Info save(Info info);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);
}