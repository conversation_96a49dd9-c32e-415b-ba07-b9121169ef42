<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-bars"></i> 运输信息</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        </div>
        <div class="layui-card-body">
            <form class="layui-form timo-search-box" action="" id="searchForm">
                <div class="layui-form-pane">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">单据号 код-данных</label>
                                <div class="layui-input-block">
                                    <input type="text" name="orderNo" th:value="${param.orderNo}" placeholder="请输入单据号"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">运输方式 вид транс</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" name="transportType" mo:dict="TRANSPORT_TYPE"
                                        mo-selected="${param.transportType}" mo-empty="全部"></select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">运输公司 Трансп.компания</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" name="transportCompany" mo:dict="TRANSPORT_COMPANY"
                                        mo-selected="${param.transportCompany}" mo-empty="全部"></select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">启运日期 дата отп</label>
                                <div class="layui-input-block" id="departureDate">
                                    <input type="text" autocomplete="off" id="departureDateStr" class="layui-input"
                                        name="departureDateStr" placeholder="开始-结束"
                                        th:value="${param.departureDateStr}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">送达日期 Дата Доставки</label>
                                <div class="layui-input-block" id="deliveryDate">
                                    <input type="text" autocomplete="off" id="deliveryDateStr" class="layui-input"
                                        name="deliveryDateStr" placeholder="开始-结束" th:value="${param.deliveryDateStr}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="text-align: center;">
                            <button type="button" class="layui-btn timo-search-btn">
                                <i class="fa fa-search"></i> 搜索 Поиск
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                <i class="fa fa-refresh"></i> 重置 Сбросить
                            </button>
                            <button type="button" class="layui-btn open-popup" data-title="添加运输信息管理"
                                th:attr="data-url=@{/psm/transport/add}" data-size="auto">
                                <i class="fa fa-plus"></i> 添加 Добавить
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            <div class="timo-table-wrap">
                <table class="layui-table timo-table">
                    <thead>
                        <tr>
                            <th class="timo-table-checkbox">
                                <label class="timo-checkbox"><input type="checkbox">
                                    <i class="layui-icon layui-icon-ok"></i></label>
                            </th>
                            <th>单据号 код-данных</th>
                            <th>运输方式 вид транс</th>
                            <th>运输公司 Трансп.компания</th>
                            <th>中国总费用 Итого(КНР)</th>
                            <th>中国经办人 ОЛ (КНР) </th>
                            <th>俄罗斯总费用 Итого(РФ)</th>
                            <th>俄罗斯经办人 ОЛ (РФ)</th>
                            <th>启运日期 дата отп</th>
                            <th>送达日期 Дата Доставки</th>
                            <th>操作 Операции с Данными</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item:${list}">
                            <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                                    <i class="layui-icon layui-icon-ok"></i></label></td>
                            <td th:text="${item.orderNo}">单据号</td>
                            <td th:text="${#dicts.keyValue('TRANSPORT_TYPE', item.transportType)}">运输方式</td>
                            <td th:text="${#dicts.keyValue('TRANSPORT_COMPANY', item.transportCompany)}">运输公司</td>
                            <td th:text="'$' + ${item.chinaTotalCost}">中国总费用</td>
                            <td th:text="${item.chinaHandler}">中国经办人</td>
                            <td th:text="'$' + ${item.russiaTotalCost}">俄罗斯总费用</td>
                            <td th:text="${item.russiaHandler}">俄罗斯经办人</td>
                            <td th:text="${#dates.format(item.departureDate, 'yyyy-MM-dd')}">启运日期</td>
                            <td th:text="${#dates.format(item.deliveryDate, 'yyyy-MM-dd')}">送达日期</td>
                            <td>
                                <a class="open-popup" data-title="编辑运输信息"
                                    th:attr="data-url=@{'/psm/transport/edit/'+${item.id}}" data-size="auto"
                                    href="#">编辑</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div th:replace="/common/fragment :: page"></div>
        </div>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.use(['laydate', 'form'], function () {
            var laydate = layui.laydate;
            var form = layui.form;

            $('#resetBtn').on('click', function () {
                $("#searchForm input").val("");
                $("#searchForm select").val("");
                $(".timo-search-btn").click();
            });

            laydate.render({
                elem: '#departureDateStr',
                range: true,
                rangeLinked: true
            });

            laydate.render({
                elem: '#deliveryDateStr',
                range: true,
                rangeLinked: true
            });
        });
    </script>
</body>

</html>
