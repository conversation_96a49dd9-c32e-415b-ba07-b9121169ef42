package com.linln.admin.psm.service;

import com.linln.admin.psm.domain.Contract;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface ContractService {

    /**
     * 获取分页列表数据
     *
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<Contract> getPageList(Example<Contract> example);

    /**
     * 获取分页列表数据
     *
     * @param spec 查询实例
     * @return 返回分页数据
     */
    Page<Contract> getPageList(Specification<Contract> spec);

    /**
     * 根据ID查询数据
     *
     * @param id 主键ID
     */
    Contract getById(Long id);

    /**
     * 保存数据
     *
     * @param contract 实体对象
     */
    Contract save(Contract contract);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);

    /**
     *
     * @return
     */
    Contract newInstance();

    /**
     * 根据关键字搜索合同（OR连接条件）
     *
     * @param keyword     搜索关键字
     * @param salesUserId 业务员ID
     * @param pageRequest 分页参数
     * @return 合同列表
     */
    List<Contract> searchByKeyword(String keyword, Long salesUserId, PageRequest pageRequest);

    /**
     * 根据条件统计合同数据
     *
     * @param spec 查询条件
     * @return 统计结果Map
     */
    java.util.Map<String, Object> getStatistics(Specification<Contract> spec);

    /**
     * 根据条件获取合同列表（用于导出）
     *
     * @param spec 查询条件
     * @return 合同列表
     */
    List<Contract> getListForExport(Specification<Contract> spec);
}
