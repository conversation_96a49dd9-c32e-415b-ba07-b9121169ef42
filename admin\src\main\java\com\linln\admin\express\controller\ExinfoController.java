package com.linln.admin.express.controller;

import com.google.common.base.Strings;
import com.linln.admin.express.domain.Exinfo;
import com.linln.admin.express.domain.ExpressSummaryDto;
import com.linln.admin.express.repository.ExinfoRepository;
import com.linln.admin.express.service.ExinfoService;
import com.linln.admin.express.validator.ExinfoValid;
import com.linln.common.constant.AdminConst;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.SpringContextUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import com.linln.component.excel.ExcelUtil;
import com.linln.component.shiro.ShiroUtil;
import com.linln.modules.system.domain.Role;
import com.linln.modules.system.domain.User;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.thymeleaf.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/03/28
 */
@Controller
@RequestMapping("/express/exinfo")
public class ExinfoController {

    @Autowired
    private ExinfoService exinfoService;
    @Autowired
    private ExinfoRepository exinfoRepository;

    @GetMapping("/index")
    @RequiresPermissions("express:exinfo:index")
    @Transactional
    public String index(Model model, Exinfo exinfo,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate reachStoreStartDate,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate reachStoreEndDate,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDateStartDate,
                        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDateEndDate
    ) {
        User loginUser = ShiroUtil.getSubject();
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        if (!loginUser.getId().equals(AdminConst.ADMIN_ID)
                && subjectRoles.stream().noneMatch(x -> "expressadmin".equalsIgnoreCase(x.getName()))) {
            exinfo.setCreator(loginUser.getId());
        }
        exinfo.setReachStoreStartDate(reachStoreStartDate);
        exinfo.setReachStoreEndDate(reachStoreEndDate);
        exinfo.setStartDateStartDate(startDateStartDate);
        exinfo.setStartDateEndDate(startDateEndDate);
        // 1. 构造 Example
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("sender", match -> match.contains())
                .withMatcher("oper", match -> match.contains());
        Example<Exinfo> example = Example.of(exinfo, matcher);

        // 2. 构造 Specification（分页和汇总共用）
        Specification<Exinfo> spec = exinfoService.buildSpecByExample(example);
        // 3. 先统计汇总数据
        ExpressSummaryDto summary = exinfoService.getSummaryBySpec(spec);
        // 4. 再进行分页查询
        Page<Exinfo> list = exinfoService.getPageListBySpec(spec);
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        model.addAttribute("summary", summary);
        return "/express/exinfo/index";
    }

    /**
     * 导出数据
     */
    @GetMapping("/export")
    @ResponseBody
    public void exportExcel() {
        User loginUser = ShiroUtil.getSubject();
        Exinfo exinfo = new Exinfo();
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        // 创建匹配器，进行动态查询匹配
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("sender", match -> match.contains())
                .withMatcher("oper", match -> match.contains());

        boolean expressadmin = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("expressadmin", x.getName()));

        // 管理员拥有所有数据的，非管理员只能查询自己的数据
        if (!loginUser.getId().equals(AdminConst.ADMIN_ID) && !expressadmin) {
            exinfo.setCreator(loginUser.getId());
        }
        // 获取数据列表
        Example<Exinfo> example = Example.of(exinfo, matcher);
        ExinfoRepository exinfoRepository = SpringContextUtil.getBean(ExinfoRepository.class);

        List<Exinfo> list = exinfoRepository.findAll(example);
        ExcelUtil.exportExcel(Exinfo.class, list);
    }

    /**
     * 列表页面
     */
    @GetMapping("/index2")
    public String searchDetail(Model model, Exinfo query) {
        if (!Strings.isNullOrEmpty(query.getCargoCode())) {
            // 创建匹配器，进行动态查询匹配
            ExampleMatcher matcher = ExampleMatcher.matching();

            Exinfo queryExinfo = new Exinfo();
            queryExinfo.setCargoCode(query.getCargoCode());
            // 获取数据列表
            Example<Exinfo> example = Example.of(queryExinfo, matcher);
            Sort sort = new Sort(Sort.Direction.ASC, "id");
            List<Exinfo> list = exinfoService.getListByExample(example, sort);
            if (list == null || list.size() != 1) {
                //重新输入货物编码 код
            } else {
                // 封装数据
                model.addAttribute("detail", list.get(0));
            }
        }
        return "/express/exinfo/index2";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("express:exinfo:add")
    public String toAdd() {
        return "/express/exinfo/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("express:exinfo:edit")
    public String toEdit(@PathVariable("id") Exinfo exinfo, Model model) {
        model.addAttribute("exinfo", exinfo);
        return "/express/exinfo/add";
    }

    /**
     * 保存添加/修改的数据
     *
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"express:exinfo:add", "express:exinfo:edit"})
    @ResponseBody
    public ResultVo save(@Validated ExinfoValid valid, Exinfo exinfo) {
        String paymentDate = exinfo.getPaymentDate();
        if (StringUtils.isEmpty(paymentDate)) {
            exinfo.setPaymentDate(null);  // 如果为空字符串，设置为null
        } else {
            exinfo.setPaymentDate(paymentDate);  // 否则保存原值
        }
        if (StringUtils.isEmpty(paymentDate)) {
            exinfo.setPaymentDate(null);  // 如果为空字符串，设置为null
        } else {
            exinfo.setPaymentDate(paymentDate);  // 否则保存原值
        }
        User loginUser = ShiroUtil.getSubject();
        Set<Role> subjectRoles = ShiroUtil.getSubjectRoles();
        boolean expressadmin = subjectRoles.stream().anyMatch(x -> StringUtils.equalsIgnoreCase("expressadmin", x.getName()));
        // 复制保留无需修改的数据
      
        //if (exinfo.getId() != null) {
          //  Exinfo beExinfo = exinfoService.getById(exinfo.getId());
          //  EntityBeanUtil.copyProperties(beExinfo, exinfo);
            // 管理员拥有所有数据的，非管理员只能查询自己的数据
           // if (!loginUser.getId().equals(AdminConst.ADMIN_ID) && !expressadmin) {
             //   if (!Objects.equals(beExinfo.getOuterAmount(), exinfo.getOuterAmount())) {
               //     return ResultVoUtil.error("国外应付不可编辑，请重新操作");
            //    }
           // }

       // } //else {
            //if (!loginUser.getId().equals(AdminConst.ADMIN_ID) && !expressadmin) {
             //   if (!Strings.isNullOrEmpty(exinfo.getOuterAmount())) {
                //    return ResultVoUtil.error("国外应付不可编辑，请重新操作");
               // }
           // }
       // }
       if (exinfo.getId() != null) {
    // 已有数据的情况（更新操作）
    Exinfo beExinfo = exinfoService.getById(exinfo.getId());
    EntityBeanUtil.copyProperties(beExinfo, exinfo);
    
    // 移除权限校验，允许任何人修改 outerAmount
    // （如果仍需其他业务校验，可以保留）
   } else {
    // 新增数据的情况
    // 移除权限校验，允许业务员填写 outerAmount
   }
        // 中国仓储费
        String interSundryfees = exinfo.getInterSundryfees();
        if (interSundryfees == null || interSundryfees.trim().isEmpty()) {
            interSundryfees = "0";
        }
        // 中国运费
        String interShippingFee = exinfo.getInterShippingFee();
        if (interShippingFee == null || interShippingFee.trim().isEmpty()) {
            interShippingFee = "0";
        }
        // 转为浮点进行相加
        BigDecimal interSundryfeesBD = new BigDecimal(interSundryfees);
        BigDecimal interShippingFeeBD = new BigDecimal(interShippingFee);
        BigDecimal internalAmount = interSundryfeesBD.add(interShippingFeeBD);
        exinfo.setInternalAmount(internalAmount.toString());
        exinfo.setCreator(loginUser.getId());
        // 保存数据
        exinfoService.save(exinfo);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("express:exinfo:detail")
    public String toDetail(@PathVariable("id") Exinfo exinfo, Model model) {
        model.addAttribute("exinfo", exinfo);
        return "/express/exinfo/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("express:exinfo:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (exinfoService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}