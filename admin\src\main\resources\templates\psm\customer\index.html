<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .layui-form-select .layui-input {
            height: 38px;
        }
    </style>
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-bars"></i> 客户管理 Управление клиентами</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        </div>
        <div class="layui-card-body">
            <form class="layui-form timo-search-box" action="" id="searchForm">
                <div class="layui-form-pane">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户信息 Информация о клиентах</label>
                                <div class="layui-input-block">
                                    <input type="text" name="custOrg" th:value="${param.custOrg}" placeholder="请输入客户信息"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">联系人 Контакт</label>
                                <div class="layui-input-block">
                                    <input type="text" name="contName" th:value="${param.contName}" placeholder="请输入联系人"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">创建时间 дата создания</label>
                                <div class="layui-input-block" id="createDate">
                                    <input type="text" autocomplete="off" id="createDateStr" class="layui-input"
                                        name="createDateStr" placeholder="开始-结束" th:value="${param.createDateStr}">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户类型 Тип клиента</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" name="custType" mo:dict="CUST_TYPE"
                                        mo-selected="${param.custType}" mo-empty="全部"></select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">客户国家 Страна клиента</label>
                                <div class="layui-input-block">
                                    <select class="layui-select" name="custCountry" mo:dict="CUST_LOCAL"
                                        mo-selected="${param.custCountry}" mo-empty="全部"></select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="text-align: center;">
                            <button type="button" class="layui-btn timo-search-btn">
                                <i class="fa fa-search"></i> 搜索 Поиск
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                <i class="fa fa-refresh"></i> 重置 Сбросить
                            </button>
                            <button type="button" class="layui-btn open-popup" data-title="添加基础信息"
                                th:attr="data-url=@{/psm/customer/add}" data-size="auto">
                                <i class="fa fa-plus"></i> 添加 Добавить
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            <div class="timo-table-wrap">
                <table class="layui-table timo-table">
                    <thead>
                        <tr>
                            <th class="timo-table-checkbox">
                                <label class="timo-checkbox"><input type="checkbox">
                                    <i class="layui-icon layui-icon-ok"></i></label>
                            </th>
                            <th>主键ID ID (первичный ключ)</th>
                            <th>创建时间 дата создания</th>
                            <th>客户信息 Информация о клиентах</th>
                            <th>业务信息 Деловые данные</th>
                            <th>联系人 Контакт</th>
                            <th>客户所在国家 Страна клиента</th>
                            <th>客户类型 Тип клиента</th>
                            <th>联系电话 Контактный телефон</th>
                            <th>操作 Операции с Данными</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr th:each="item:${list}">
                            <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                                    <i class="layui-icon layui-icon-ok"></i></label></td>
                            <td th:text="${item.id}">主键ID</td>
                            <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                            <td th:text="${item.custOrg}">客户信息</td>
                            <td th:text="${item.custDetail}">业务信息</td>
                            <td th:text="${item.contName}">联系人</td>
                            <td th:text="${#dicts.keyValue('CUST_LOCAL',item.custCountry)}">客户所在国家</td>
                            <td th:text="${#dicts.keyValue('CUST_TYPE',item.custType)}">客户类型</td>
                            <td th:text="${item.contPhone}">联系电话</td>
                            <td>
                                <a class="open-popup" data-title="编辑基础信息"
                                    th:attr="data-url=@{'/psm/customer/edit/'+${item.id}}" data-size="auto"
                                    href="#">编辑</a>
                                <a class="open-popup" data-title="详细信息"
                                    th:attr="data-url=@{'/psm/customer/detail/'+${item.id}}" data-size="800,600"
                                    href="#">详细</a>
                                <a class="ajax-get" data-msg="您是否确认删除"
                                    th:href="@{/psm/customer/status/delete(ids=${item.id})}">删除</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div th:replace="/common/fragment :: page"></div>
        </div>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.use(['laydate', 'form'], function () {
            var laydate = layui.laydate;
            var form = layui.form;
            $('#resetBtn').on('click', function () {
                $("#searchForm input").val("");
                $("#searchForm select").val("");
                $(".timo-search-btn").click();
            });

            laydate.render({
                elem: '#createDateStr',
                range: true,
                rangeLinked: true
            });
            
        });
    </script>
</body>

</html>
