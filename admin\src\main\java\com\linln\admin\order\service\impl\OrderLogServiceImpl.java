package com.linln.admin.order.service.impl;

import com.linln.admin.order.domain.OrderLog;
import com.linln.admin.order.repository.OrderLogRepository;
import com.linln.admin.order.service.OrderLogService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/04/11
 */
@Service
public class OrderLogServiceImpl implements OrderLogService {

    @Autowired
    private OrderLogRepository orderLogRepository;

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    @Override
    public OrderLog getById(Long id) {
        return orderLogRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<OrderLog> getPageList(Example<OrderLog> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return orderLogRepository.findAll(example, page);
    }

    /**
     * 保存数据
     * @param orderLog 实体对象
     */
    @Override
    public OrderLog save(OrderLog orderLog) {
        return orderLogRepository.save(orderLog);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return orderLogRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }
}