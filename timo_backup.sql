-- MySQL dump 10.13  Distrib 8.0.43, for Linux (aarch64)
--
-- Host: localhost    Database: timo
-- ------------------------------------------------------
-- Server version	8.0.43

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `acct_info`
--

DROP TABLE IF EXISTS `acct_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `acct_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `balance` varchar(25) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `user_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `history_amount` varchar(25) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '历史充值金额',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK8rg3bx68e9cpuvd412psn8685` (`create_by`) USING BTREE,
  KEY `FKckwb0gml2fdk8cl039w64ldjs` (`update_by`) USING BTREE,
  CONSTRAINT `FK8rg3bx68e9cpuvd412psn8685` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKckwb0gml2fdk8cl039w64ldjs` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `acct_info`
--

LOCK TABLES `acct_info` WRITE;
/*!40000 ALTER TABLE `acct_info` DISABLE KEYS */;
INSERT INTO `acct_info` VALUES (24,'0.02','2021-11-13 16:52:49',NULL,1,'2025-07-14 14:59:22',18,3,3,'1B.ru','18995580.92'),(25,'-136302.26','2021-11-13 17:37:38',NULL,1,'2024-08-23 10:52:27',19,3,3,'sergey','5417751.72'),(26,'-7229.7','2021-11-30 17:27:13',NULL,1,'2022-07-12 17:20:00',20,3,3,'lesha','0'),(27,'0','2022-09-16 16:27:54',NULL,1,'2022-09-16 16:29:17',29,3,3,'sir-01','242454'),(28,'1163372','2022-09-22 11:41:34',NULL,1,'2022-09-22 11:41:34',36,3,3,'ntn-01','1163372'),(29,'-0.01','2023-04-27 17:15:05','',1,'2023-10-26 10:02:46',39,3,3,'TRADE01','7118156.29'),(30,'1.36','2023-06-27 01:05:32',NULL,1,'2023-10-12 16:20:40',40,3,3,'RADI','21168016.36'),(31,'0.00','2023-08-16 15:19:18',NULL,1,'2024-03-04 09:35:29',41,3,3,'RADI-3','3519305.64'),(32,'127428.5','2023-09-07 14:08:49',NULL,1,'2024-01-02 13:29:49',42,3,3,'Svyat','1304050'),(33,'-431381.3','2023-10-20 10:31:54',NULL,1,'2023-12-07 14:01:04',43,3,3,'NTNK','5663370.7'),(34,'-3119428.8','2023-10-20 10:49:33',NULL,1,'2023-10-20 10:54:50',44,3,3,'SIRIN','1500634.2'),(35,'74.4','2023-10-21 08:17:37',NULL,1,'2023-11-21 16:56:41',45,3,3,'KARIMOV','148860.40'),(36,'591.3','2023-12-21 09:52:13',NULL,1,'2024-03-19 10:20:28',54,3,3,'Pavel','214833.5'),(37,'103748.53','2023-12-21 09:56:18',NULL,1,'2023-12-21 10:01:33',55,3,3,'Pantes','657455.85'),(38,'0.01','2025-02-04 20:27:01',NULL,3,'2025-02-04 20:27:01',56,64,64,'新增用户1','0.01');
/*!40000 ALTER TABLE `acct_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `or_exinfo`
--

DROP TABLE IF EXISTS `or_exinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `or_exinfo` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `arrival_date` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `cargo_code` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `cargo_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `cargo_volume` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `cargo_weight` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `foreign_recv` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `internal_amount` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `oper` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `outer_amount` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `reach_store_date` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `recv_amount` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `sender` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `sender_tel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `start_date` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `tel` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `trans_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  `carton_number` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `progress` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `express_no` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `outer_clear_amount` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `outer_remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `payment_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '收款类型',
  `payment_date` datetime DEFAULT NULL,
  `inter_sundryfees` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `inter_shipping_fee` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKmpjx5w325grmolnkxkj6ix2fw` (`create_by`) USING BTREE,
  KEY `FKajnq57mofgtvwhq4dqrayrqq4` (`update_by`) USING BTREE,
  CONSTRAINT `FKajnq57mofgtvwhq4dqrayrqq4` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKmpjx5w325grmolnkxkj6ix2fw` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=1070 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `or_exinfo`
--

LOCK TABLES `or_exinfo` WRITE;
/*!40000 ALTER TABLE `or_exinfo` DISABLE KEYS */;
INSERT INTO `or_exinfo` VALUES (1001,'2025-09-05','MK77-MOCK-1001','刹车片','1.651','340.93','2025-08-18 00:00:00','巴西客户C',NULL,'李四','10066.57','2025-11-01','30000.02','','保定工厂',NULL,'2025-02-03',3,'+72079729645','海运','2025-06-20 10:05:57',27,1,1,'3','','EXP-604899','3000','待清账','DOMESTIC_COMPANY','2025-05-21 00:00:00','2356.44','11008.54'),(1002,'2025-03-14','MK77-MOCK-1002','减震器','0.08','55.34','2025-05-06 00:00:00','巴西客户C',NULL,'李四','3121.02','2025-04-13','30000',NULL,'保定工厂',NULL,'2025-04-15',3,'+77671956836','海运','2025-06-16 09:42:25',27,1,1,'3','','EXP-413922','12261.81','','到付','2023-01-04 00:00:00','3000','7000'),(1003,'2025-01-22','MK77-MOCK-1003','发动机','1.013','55.64','2025-06-18 00:00:00','巴西客户C',NULL,'王五','16347.77','2025-03-19','18435.88','','保定工厂',NULL,'2025-05-18',3,'+71733058843','','2025-06-17 13:54:29',27,1,1,'3','','EXP-399763','','待清账','DOMESTIC_COMPANY','2025-05-01 00:00:00','52.6','894.85'),(1004,'2023-01-03','MK77-MOCK-1004','刹车片','1.618','17.35','2023-04-12 00:00:00','德国客户B',NULL,'张三','2629.92','2025-04-23','20000',NULL,'重庆基地',NULL,'2025-03-27',3,'+75242296923','','2025-06-16 09:47:36',27,1,1,'1','','EXP-707239','300','待清账','月结','2025-06-03 00:00:00','335.51','99.0'),(1005,'2023-08-15','MK77-MOCK-1005','刹车片','0.305','158.48','2025-02-23 00:00:00','德国客户B','6993.6','王五','19244.68','2023-02-26','7151.78','','保定工厂','13402507936','2025-10-09',3,'+75671774341','陆运','2023-02-03 00:00:00',27,27,27,'4','已付款','EXP-908145','11975.22','','到付','2023-05-18 00:00:00','476.38','381.78'),(1006,'2025-04-26','MK77-MOCK-1006','发动机','0.122','4.89','2025-04-04 00:00:00','德国客户B','8083.51','张三','9978.42','2025-01-25','17831.58','','保定工厂','13567882811','2023-05-17',3,'+71084863031','海运','2025-02-19 00:00:00',27,27,27,'1','已付款','EXP-556819','','待清账','预付',NULL,'159.33','1466.84'),(1007,'2025-03-23','MK77-MOCK-1007','发动机','0.217','31.62','2025-08-16 00:00:00','俄罗斯客户A','9643.42','张三','655.15','2025-07-12','11838.41','','天津工厂','13234322060','2025-03-03',3,'+71125947904','海运','2025-03-25 00:00:00',27,27,27,'2','已到达','EXP-553529','1235.66','','月结','2023-07-25 00:00:00','13.17','261.22'),(1008,'2023-03-24','MK77-MOCK-1008','减震器','0.878','462.68','2025-06-08 00:00:00','俄罗斯客户A',NULL,'张三','3448.22','2023-04-30','24161.18','','保定工厂',NULL,'2023-10-16',3,'+71290039540','海运','2025-06-17 10:01:49',27,1,1,'2','','EXP-669682','5019.81','','INTERNATIONAL_PERSONAL','2025-05-26 00:00:00','172.76','1355.35'),(1009,'2025-11-28','MK77-MOCK-1009','发动机','0.368','353.67','2023-01-24 00:00:00','俄罗斯客户A','9987.35','李四','4568.61','2025-02-24','14559.07','','重庆基地','13480043226','2025-08-01',3,'+74083611097','空运','2025-01-13 00:00:00',27,27,27,'3','已到达','EXP-107912','12920.98','','预付','2023-11-06 00:00:00','269.32','1236.89'),(1010,'2025-12-07','MK77-MOCK-1010','发动机','1.495','189.6','2025-10-22 00:00:00','巴西客户C','1329.07','王五','12507.03','2023-01-05','17874.2','','天津工厂','13906067430','2023-03-11',3,'+79805685540','海运','2025-07-07 00:00:00',27,27,27,'5','待付款','EXP-763671','','待清账','预付',NULL,'255.7','857.67'),(1011,'2025-05-31','MK77-MOCK-1011','减震器','1.272','321.84','2025-04-23 00:00:00','巴西客户C',NULL,'王五','16009.03','2025-04-24','13156.87','','重庆基地',NULL,'2025-05-07',3,'+79446747222','','2025-06-13 17:56:50',27,1,1,'3','已完结','EXP-669625','14602.42','','预付','2025-08-09 00:00:00','41.64','585.78'),(1012,'2023-06-23','MK77-MOCK-1012','减震器','1.455','112.7','2025-06-10 00:00:00','俄罗斯客户A','3531.68','张三','8336.19','2025-07-13','12806.64','','天津工厂','13284686309','2025-08-20',3,'+76779676713','海运','2025-09-02 00:00:00',27,27,27,'1','已付款','EXP-982404','14017.98','','到付','2025-01-12 00:00:00','242.01','295.85'),(1013,'2025-05-16','MK77-MOCK-1013','刹车片','1.545','462.46','2023-12-24 00:00:00','德国客户B',NULL,'张三','2090.3','2025-02-24','7011.26',NULL,'重庆基地',NULL,'2025-06-05',3,'+76786041860','空运','2025-06-16 09:46:56',27,1,1,'2','','EXP-388066','2125.05','','预付','2025-11-25 00:00:00','230.86','720.51'),(1014,'2025-10-06','MK77-MOCK-1014','仪表板','1.144','171.2','2023-08-14 00:00:00','德国客户B',NULL,'王五','4201.58','2025-09-26','9983.92',NULL,'天津工厂',NULL,'2025-02-07',3,'+74925084214','空运','2025-06-16 09:48:34',27,1,1,'1','','EXP-838590','10161.1','','预付','2023-10-07 00:00:00','467.99','1096.28'),(1015,'2025-08-05','MK77-MOCK-1015','安全气囊','1.909','36.47','2023-05-12 00:00:00','巴西客户C','2067.85','李四','11198.15','2023-05-17','7206.91','','重庆基地','13555329628','2023-05-23',3,'+79156322471','陆运','2025-11-03 00:00:00',27,27,27,'3','在途','EXP-401034','','待清账','月结',NULL,'347.6','870.28'),(1016,'2025-05-20','MK77-MOCK-1016','刹车片','1.43','258.49','2025-04-29 00:00:00','巴西客户C',NULL,'李四','16120.71','2025-08-11','50000','','重庆基地',NULL,'2023-03-29',3,'+72896740873','海运','2025-06-17 10:02:04',27,1,1,'2','','EXP-326278','854.89','','DOMESTIC_PERSONAL','2025-02-16 00:00:00','319.72','764.29'),(1017,'2023-03-06','MK77-MOCK-1017','发动机','1.256','148.56','2025-04-10 00:00:00','德国客户B',NULL,'李四','14896.81','2025-06-13','27838.22',NULL,'重庆基地',NULL,'2025-01-08',3,'+71709524310','','2025-06-16 09:54:37',27,1,1,'1','','EXP-605053','2500','待清账','预付','2025-06-10 00:00:00','1004.06','7980.92'),(1018,'2025-11-13','MK77-MOCK-1018','减震器','1.892','298.3','2023-12-28 00:00:00','巴西客户C',NULL,'李四','9445.47','2025-03-18','18309.05','','天津工厂',NULL,'2025-03-16',3,'+71020232349','空运','2025-06-17 14:28:58',27,1,1,'4','进行中','EXP-444827','','待清账','INTERNATIONAL_COMPANY','2025-05-01 00:00:00','443.55','1213.78'),(1019,'2023-04-01','MK77-MOCK-1019','减震器','1.295','8.74','2023-07-16 00:00:00','俄罗斯客户A','9326.77','张三','11187.83','2025-07-13','17348.01','','保定工厂','13727641254','2023-10-07',3,'+78205473852','海运','2023-08-02 00:00:00',27,27,27,'5','待付款','EXP-329159','14082.38','','到付','2025-04-19 00:00:00','313.32','951.42'),(1020,'2023-07-17','MK77-MOCK-1020','仪表板','0.68','8.56','2023-07-01 00:00:00','巴西客户C',NULL,'王五','19950.42','2025-03-21','66413.07',NULL,'重庆基地',NULL,'2025-04-10',3,'+72193155514','空运','2025-06-16 09:48:05',27,1,1,'1','','EXP-350652','8500.43','','月结','2025-04-14 00:00:00','298.13','1446.05'),(1021,'2023-09-26','MK77-MOCK-1021','减震器','0.774','341.94','2025-12-14 00:00:00','俄罗斯客户B',NULL,'王五','10468.28','2025-01-25','22102.24','','重庆基地',NULL,'2025-03-21',3,'+70270865113','空运','2025-06-27 08:38:46',27,1,1,'3','已完结','EXP-728370','5000','待清账','','2025-06-13 00:00:00','49.18','141.03'),(1022,'2023-02-08','MK77-MOCK-1022','发动机','1.696','375.59','2023-06-14 00:00:00','俄罗斯客户A','1046.62','王五','12406.13','2025-04-06','11828.17','','保定工厂','13261127330','2023-06-17',3,'+72784203335','空运','2025-06-13 00:00:00',27,27,27,'1','已到达','EXP-287665','','待清账','月结',NULL,'308.65','1129.25'),(1023,'2025-05-21','MK77-MOCK-1023','刹车片','1.492','259.95','2023-10-01 00:00:00','巴西客户C','5167.36','王五','12839.18','2023-12-13','15485.32','','重庆基地','13626580532','2025-09-27',3,'+70355551423','空运','2023-07-11 00:00:00',27,27,27,'2','已到达','EXP-730466','12882.29','','月结','2025-03-02 00:00:00','210.88','613.11'),(1024,'2025-12-06','MK77-MOCK-1024','仪表板','1.883','201.57','2023-11-13 00:00:00','俄罗斯客户A','9936.05','李四','14417.29','2023-08-11','2178.15','','天津工厂','13277695243','2025-12-16',3,'+71581376188','海运','2023-10-09 00:00:00',27,27,27,'4','待付款','EXP-817852','2118.36','','预付','2025-11-27 00:00:00','483.13','232.31'),(1025,'2023-09-25','MK77-MOCK-1025','仪表板','1.826','55.79','2025-10-17 00:00:00','巴西客户C','8749.42','王五','4097.97','2023-07-24','204.97','','保定工厂','13083893919','2023-10-19',3,'+76480657820','海运','2023-01-21 00:00:00',27,27,27,'5','已到达','EXP-100972','','待清账','预付',NULL,'441.66','131.37'),(1026,'2025-05-31','MK77-MOCK-1026','仪表板','1.832','427.93','2025-05-11 00:00:00','巴西客户C','940.89','李四','9018.97','2025-11-06','16547.83','','保定工厂','13646092891','2025-05-20',3,'+73002021905','空运','2025-12-16 00:00:00',27,27,27,'3','待付款','EXP-258018','','待清账','到付',NULL,'377.72','759.69'),(1027,'2025-01-14','MK77-MOCK-1027','刹车片','0.125','264.0','2025-01-24 00:00:00','巴西客户C',NULL,'李四','6652.99','2023-09-22','25000','','天津工厂',NULL,'2025-03-10',3,'+72107921518','','2025-06-17 13:55:46',27,1,1,'3','进行中','EXP-311813','6880','','INTERNATIONAL_COMPANY','2025-04-30 00:00:00','5000','6000'),(1028,'2025-02-17','MK77-MOCK-1028','刹车片','1.681','369.07','2023-04-20 00:00:00','巴西客户C',NULL,'王五','3000','2023-12-13','20000',NULL,'天津工厂',NULL,'2025-07-11',3,'+75215135585','','2025-06-14 16:44:14',27,1,1,'4','进行中','EXP-363338','5000','','到付','2023-09-03 00:00:00','3000','5000'),(1029,'2023-10-18','MK77-MOCK-1029','发动机','0.192','295.2','2025-09-29 00:00:00','俄罗斯客户A','181.95','张三','5221.89','2025-04-22','14901.58','','天津工厂','13592578022','2023-06-22',3,'+79655752060','空运','2025-02-04 00:00:00',27,27,27,'2','已付款','EXP-481231','','待清账','月结',NULL,'12.82','1346.42'),(1030,'2023-09-03','MK77-MOCK-1030','仪表板','1.219','92.08','2023-06-15 00:00:00','巴西客户C',NULL,'张三','11351.39','2025-02-12','19311.93',NULL,'保定工厂',NULL,'2025-08-16',3,'+70127759624','','2025-06-16 09:49:40',27,1,1,'2','进行中','EXP-158823','2000','待清账','月结','2025-05-08 00:00:00','435.91','404.18'),(1031,'2025-06-22','MK77-MOCK-1031','刹车片','1.329','433.4','2025-12-11 00:00:00','德国客户B','7076.01','张三','14059.25','2025-06-30','14207.94','','保定工厂','13779291676','2023-01-25',3,'+70679840677','空运','2023-10-26 00:00:00',27,27,27,'4','在途','EXP-633530','','待清账','到付',NULL,'476.46','1125.62'),(1032,'2025-02-02','MK77-MOCK-1032','发动机','0.051','6.94','2023-10-08 00:00:00','德国客户B','4673.13','王五','13532.24','2025-08-04','9049.59','','重庆基地','13615030636','2023-01-05',3,'+79972364268','空运','2023-11-18 00:00:00',27,27,27,'2','在途','EXP-130186','','待清账','月结',NULL,'267.4','1319.21'),(1033,'2025-02-17','MK77-MOCK-1033','安全气囊','1.83','82.55','2025-10-17 00:00:00','俄罗斯客户A','2793.96','王五','16121.04','2023-06-15','8564.33','','重庆基地','13213916332','2023-03-23',3,'+78339470963','海运','2023-04-17 00:00:00',27,27,27,'2','待付款','EXP-342371','11581.73','','月结','2025-12-05 00:00:00','445.0','1153.01'),(1034,'2025-05-13','MK77-MOCK-1034','仪表板','0.45','84.54','2023-03-19 00:00:00','俄罗斯客户A',NULL,'张三','13898.45','2025-08-25','30000',NULL,'重庆基地',NULL,'2025-02-06',3,'+72187778816','海运','2025-06-16 09:51:32',27,1,1,'4','','EXP-257215','3000','待清账','到付','2025-05-21 00:00:00','3090.05','1123.84'),(1035,'2023-05-29','MK77-MOCK-1035','减震器','0.173','496.66','2023-01-26 00:00:00','德国客户B',NULL,'张三','9800.65','2023-03-03','15000',NULL,'保定工厂',NULL,'2023-10-07',3,'+79977222096','','2025-06-16 09:50:46',27,1,1,'1','','EXP-188058','2000','待清账','预付','2025-05-05 00:00:00','1500','809.47'),(1036,'2025-01-19','MK77-MOCK-1036','安全气囊','1.329','368.09','2023-12-28 00:00:00','巴西客户C',NULL,'张三','231.7','2023-12-28','19480.61','','保定工厂',NULL,'2025-04-10',3,'+70061166766','','2025-06-17 14:28:39',27,1,1,'5','进行中','EXP-278963','11218.59','','DOMESTIC_COMPANY','2023-11-19 00:00:00','342.51','2510.73'),(1037,'2023-05-29','MK77-MOCK-1037','刹车片','1.689','267.18','2023-07-13 00:00:00','德国客户B','9524.8','王五','16199.67','2023-08-15','1242.72','','天津工厂','13004252853','2025-01-09',3,'+71155198720','海运','2023-08-11 00:00:00',27,27,27,'5','待付款','EXP-215951','2260.27','','月结','2023-04-24 00:00:00','68.77','398.69'),(1038,'2025-06-02','MK77-MOCK-1038','仪表板','1.545','466.97','2023-03-11 00:00:00','德国客户B',NULL,'王五','5902.83','2025-05-16','25705.61',NULL,'保定工厂',NULL,'2025-09-23',3,'+75871591534','空运','2025-06-16 09:51:58',27,1,1,'4','','EXP-221202','7595.39','','到付','2025-01-15 00:00:00','172.79','605.5'),(1039,'2025-04-26','MK77-MOCK-1039','发动机','1.294','438.83','2025-01-03 00:00:00','德国客户B',NULL,'王五','17025.4','2023-01-10','49501.49',NULL,'重庆基地',NULL,'2023-01-24',3,'+74260763543','海运','2025-06-16 09:53:08',27,1,1,'5','','EXP-361431','6251.41','','到付','2023-03-08 00:00:00','138.14','887.58'),(1040,'2023-10-26','MK77-MOCK-1040','发动机','1.63','234.36','2023-03-22 00:00:00','俄罗斯客户A',NULL,'王五','10000','2025-03-03','30000',NULL,'保定工厂',NULL,'2025-03-16',3,'+73274517016','海运','2025-06-16 09:45:20',27,1,1,'5','','EXP-960769','5000','','预付','2023-08-22 00:00:00','3000','5000'),(1041,'2025-01-08','MK77-MOCK-1041','安全气囊','1.713','390.17','2025-02-17 00:00:00','俄罗斯客户A','849.72','李四','1261.35','2023-03-05','7641.51','','天津工厂','13151552361','2023-10-04',3,'+75369277100','海运','2023-08-10 00:00:00',27,27,27,'4','在途','EXP-815444','','待清账','到付',NULL,'117.31','1214.23'),(1042,'2023-10-16','MK77-MOCK-1042','安全气囊','1.698','201.2','2023-09-18 00:00:00','德国客户B','429.01','王五','16679.95','2023-08-03','7303.83','','天津工厂','13521140308','2023-09-18',3,'+70643566653','空运','2025-09-11 00:00:00',27,27,27,'3','已到达','EXP-688243','10452.83','','月结','2025-03-21 00:00:00','46.86','1239.99'),(1043,'2025-05-21','MK77-MOCK-1043','减震器','0.227','192.95','2023-05-03 00:00:00','德国客户B',NULL,'张三','1000','2023-05-12','5000',NULL,'重庆基地',NULL,'2023-01-17',3,'+75140458254','海运','2025-06-14 16:43:26',27,1,1,'3','已完结','EXP-118206','1000','','预付','2025-06-24 00:00:00','600','800'),(1044,'2025-03-19','MK77-MOCK-1044','减震器','1.766','438.79','2025-01-20 00:00:00','德国客户B',NULL,'张三','9445.86','2025-04-02','19936.52','','天津工厂',NULL,'2025-01-06',3,'+76280081542','','2025-06-17 14:28:19',27,1,1,'2','','EXP-487314','','待清账','INTERNATIONAL_COMPANY','2024-05-01 00:00:00','147.6','1155.12'),(1045,'2025-05-28','MK77-MOCK-1045','发动机','0.265','470.88','2023-05-18 00:00:00','巴西客户C','1254.32','李四','19932.46','2025-03-05','12964.72','','保定工厂','13259389992','2025-10-31',3,'+77541345291','空运','2025-03-18 00:00:00',27,27,27,'5','待付款','EXP-595576','','待清账','到付',NULL,'349.84','1497.19'),(1046,'2025-07-21','MK77-MOCK-1046','发动机','0.405','475.59','2023-08-22 00:00:00','德国客户B','6761.61','张三','6834.5','2025-12-10','15736.98','','天津工厂','13918407146','2025-03-01',3,'+72127159268','陆运','2023-03-05 00:00:00',27,27,27,'3','在途','EXP-553041','10528.59','','月结','2023-09-12 00:00:00','128.54','721.75'),(1047,'2025-11-20','MK77-MOCK-1047','减震器','0.985','326.64','2023-02-25 00:00:00','德国客户B','1248.26','李四','13076.33','2023-07-09','5716.38','','保定工厂','13862638756','2023-02-14',3,'+71592293942','空运','2023-06-12 00:00:00',27,27,27,'5','已到达','EXP-769989','','待清账','预付',NULL,'25.61','1166.2'),(1048,'2025-12-02','MK77-MOCK-1048','减震器','1.419','329.6','2023-01-17 00:00:00','俄罗斯客户A',NULL,'张三','11370.71','2023-10-21','16683.5',NULL,'保定工厂',NULL,'2023-11-11',3,'+74418625962','空运','2025-06-16 09:52:23',27,1,1,'1','','EXP-807632','1213.05','','月结','2025-05-11 00:00:00','235.99','175.51'),(1049,'2025-02-16','MK77-MOCK-1049','仪表板','0.453','270.79','2023-04-06 00:00:00','德国客户B',NULL,'王五','15946.39','2025-12-29','30000',NULL,'保定工厂',NULL,'2023-09-20',3,'+73582309560','海运','2025-06-16 09:46:19',27,1,1,'4','','EXP-300027','','待清账','月结','2025-05-07 00:00:00','14.42','893.46'),(1050,'2025-09-12','MK77-MOCK-1050','减震器','1.548','402.28','2025-08-23 00:00:00','德国客户B',NULL,'王五','18800','2025-01-16','30000','','重庆基地',NULL,'2025-05-12',3,'+77456519737','海运','2025-06-16 17:17:03',27,1,1,'1','未收款','EXP-495981','5000','待清账','INTERNATIONAL_COMPANY','2025-06-10 00:00:00','2800','4000'),(1051,'2025-09-05','202506111604666','刹车片','1.651','340.93','2025-06-13 17:45:31','巴西客户C','3000','黄','200','2025-11-01','10000','说的是','徐搏',NULL,'2025-02-03',3,'+72079729645','','2025-06-13 17:45:31',1,1,1,'','已完结','EXP-605053','1000','',NULL,'2025-06-13 00:00:00','2000','111'),(1052,'2025-05-31','20250614','汽车配件','20','200','2025-06-14 16:30:43','tony',NULL,'黄','2000','2025-05-01','10000',NULL,'徐搏',NULL,'2025-05-15',3,'23423','陆运特快','2025-06-16 09:44:00',1,1,1,'20','进行中','12323212','2000','',NULL,'2025-05-07 00:00:00','2000','2000'),(1053,'','SPB77-XZ-NY-250407-5','丝巾','0.53','171.7','2025-07-08 18:09:18','','228.96','郑永祥','','2025-04-07','228.96','','',NULL,'2025-04-07',1,'','陆运普快','2025-07-14 16:15:25',56,56,56,'5','完成выполнено','','','','','2025-07-14 00:00:00','','228.96'),(1054,'','SPB77-XZ-YW-250603-6','包装','0.64','179.9','2025-07-08 18:13:12','','343','郑永祥','','2025-06-02','343','','',NULL,'2025-06-03',1,'','陆运普快','2025-07-14 15:52:46',56,56,56,'5','完成выполнено','','','','','2025-06-06 00:00:00','','343'),(1055,'','SPB77-XZ-GZ-250610-6','手机壳 包装袋','0.51','144.1','2025-07-08 18:16:38','','150','郑永祥','','2025-06-10','215','','',NULL,'2025-06-10',1,'','陆运普快','2025-07-11 11:29:07',56,56,56,'6','运输中 перевозка','','','','','2025-07-04 00:00:00','0','150'),(1056,'2025-07-14','SPB77-XZ-YW-250701-3','宠物浴缸 桌椅','4.7','620','2025-07-08 18:18:51','','1538','郑永祥','','2025-07-01','1848','','祥子',NULL,'2025-07-01',1,'','陆运特快','2025-07-14 17:17:14',56,56,56,'5','运输中 перевозка','','','','',NULL,'0','1538'),(1057,'','SPB77-XZ-YW-250701-2','吹风机','0.34','46','2025-07-08 18:21:22','','123','郑永祥','','2025-07-01','146','1','',NULL,'2025-07-01',1,'','陆运特快','2025-07-14 15:15:17',56,56,56,'5','运输中 перевозка','','','','',NULL,'0','123'),(1058,'2025-05-03','SPB77-XZ-NY-250407-5','丝巾','0.53','171.7','2025-07-14 15:20:21','','0','祥子','','2025-04-07','202','','祥子',NULL,'2025-04-07',3,'','陆运普快','2025-07-14 15:32:55',56,1,1,'5','完成выполнено','SPB77-XZ-NY-250407-5','','','','2025-07-01 00:00:00','',''),(1059,'2025-05-24','MK77-CC250507-1','轴承','0.14','24','2025-07-14 15:31:36','','69','李善振','','2025-05-09','279','LAAB','蓝莲花（云仁机电）',NULL,'2025-05-09',1,'','陆运特快','2025-07-14 15:31:36',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','69'),(1060,'2025-06-12','MK77-250519-4','尾灯','2.94','575','2025-07-14 15:49:18','','1215.6','李善振','','2025-05-22','5855.5','LAAB','高彤（启硕焊金）',NULL,'2025-05-22',1,'','陆运特快','2025-07-14 15:49:18',27,27,27,'4','已付款оплачен','','','','DOMESTIC_COMPANY',NULL,'','1215.6'),(1061,'','SPB77-XZ-YW-250626-2','丝巾','0.19','52.1','2025-07-14 15:57:36','','68','郑永祥','','2025-06-26','68','','祥子',NULL,'2025-06-26',1,'','陆运普快','2025-07-14 17:12:06',56,56,56,'2','运输中 перевозка','','','','',NULL,'','68'),(1062,'2025-06-04','MK77-CC250515-2','尾灯','1.44','292','2025-07-14 15:58:15','','592','李善振','','2025-05-18','2973.55','LAAB','高彤（启硕焊金）',NULL,'2025-06-04',1,'','陆运特快','2025-07-14 15:58:15',27,27,27,'2','已付款оплачен','','','','DOMESTIC_COMPANY',NULL,'','592'),(1063,'','MK77-CC250601-3-2','管柱中间轴总成','0.45','45.4','2025-07-14 16:04:30','','143.83','李善振','','2025-06-02','1046.33','LAAB','IBESTWOLF',NULL,'2025-06-02',1,'','陆运超特快','2025-07-14 16:04:30',27,27,27,'2','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','143.83'),(1064,'2025-06-12','MK77-CC250523LY','开关','0.01','1','2025-07-14 16:07:59','','6','李善振','','2025-05-27','111.61','LAAB','Majunchi',NULL,'2025-05-27',1,'','陆运特快','2025-07-14 16:07:59',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','6'),(1065,'2025-06-20','MK77-CC250605-1','汽配','0.17','18','2025-07-14 16:11:34','','54','李善振','','2025-06-08','488.29','LAAB','RIMI',NULL,'2025-06-08',1,'','陆运超特快','2025-07-14 16:11:34',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','54'),(1066,'2025-06-27','MK77-CC250613LY-2','汽车显示屏','0.32','45.2','2025-07-14 16:14:24','','132','李善振','','2025-06-16','488.29','LAAB','婷宝',NULL,'2025-06-16',1,'','陆运特快','2025-07-14 16:14:24',27,27,27,'2','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','132'),(1067,'2025-07-07','MK77-CC250620LY-1','尾灯安装板总成','0.16','20.2','2025-07-14 16:33:04','','64','李善振','','2025-06-25','279','LAAB','高彤（启硕焊金）',NULL,'2025-06-25',1,'','陆运特快','2025-07-14 16:33:04',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','64'),(1068,'2025-07-07','MK77-CC250624-1','尾门锁','0.08','12.6','2025-07-14 16:36:53','','44.05','李善振','','2025-06-26','209.27','LAAB','miA',NULL,'2025-06-26',1,'','陆运特快','2025-07-14 16:36:53',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','44.05'),(1069,'2025-06-28','MK77-CC250618KY','发动机盖锁','','2','2025-07-14 16:40:14','','39.06','李善振','','2025-06-18','125.59','LAAB','杨明娇',NULL,'2025-06-18',1,'','空运','2025-07-14 16:40:14',27,27,27,'1','完成выполнено','','','','DOMESTIC_COMPANY',NULL,'','39.06');
/*!40000 ALTER TABLE `or_exinfo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `or_foreign_order`
--

DROP TABLE IF EXISTS `or_foreign_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `or_foreign_order` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ru_contract_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '俄罗斯销售合同号',
  `ru_sale_price` decimal(18,2) DEFAULT NULL COMMENT '俄罗斯售价',
  `ru_payee` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '俄罗斯收款方',
  `ru_payment_date` date DEFAULT NULL COMMENT '俄罗斯收款日期',
  `cn_ru_contract_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '外贸合同号',
  `purchase_price` decimal(18,2) DEFAULT NULL COMMENT '采购价',
  `purchase_payment_date` date DEFAULT NULL COMMENT '采购付款日期',
  `transport_mode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '运输方式',
  `transport_cost` decimal(18,2) DEFAULT NULL COMMENT '运费',
  `miscellaneous_cost` decimal(18,2) DEFAULT NULL COMMENT '杂费',
  `declaration_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '报关备注',
  `product_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '品名',
  `weight` decimal(18,2) DEFAULT NULL COMMENT '重量KG',
  `volume` decimal(18,2) DEFAULT NULL COMMENT '体积M3',
  `quantity` decimal(18,2) DEFAULT NULL COMMENT '数量',
  `cn_domestic_contract_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '中国内贸合同号',
  `cn_domestic_price` decimal(18,2) DEFAULT NULL COMMENT '中国内贸合同价',
  `cn_payment_date` date DEFAULT NULL COMMENT '中国付款日期',
  `cn_payer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '中国付款方',
  `operator_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '业务员',
  `create_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `status` tinyint DEFAULT NULL COMMENT '状态',
  `create_time` datetime DEFAULT NULL,
  `update_time` datetime DEFAULT NULL,
  `ru_payment_end_date` datetime DEFAULT NULL,
  `ru_payment_start_date` datetime DEFAULT NULL,
  `creator` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FK25i9mqevxhfv5iy921btlpg7j` (`create_by`) USING BTREE,
  KEY `FK90kq3lkt3xowoawf9l7w193cv` (`update_by`) USING BTREE,
  CONSTRAINT `FK25i9mqevxhfv5iy921btlpg7j` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FK90kq3lkt3xowoawf9l7w193cv` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `or_foreign_order`
--

LOCK TABLES `or_foreign_order` WRITE;
/*!40000 ALTER TABLE `or_foreign_order` DISABLE KEYS */;
INSERT INTO `or_foreign_order` VALUES (58,'RU2025-3316',36479.25,'远东国际物流','2025-06-06','CN-RU-6444',6694.88,'2025-10-25','陆运特快',1908.65,2220.53,'需提前预约清关','钢材',639.46,27.30,558.00,'CN-DOM-5814',29554.04,'2025-02-16','莫斯科商贸集团','赵敏','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(59,'RU2025-9873',99089.94,'莫斯科商贸集团','2025-11-28','CN-RU-9725',10907.34,'2025-08-21','铁路运输',6624.65,896.67,'需提前预约清关','日用品',2055.63,38.92,866.00,'CN-DOM-7254',79252.48,'2025-03-30','俄罗斯能源股份','王强','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(60,'RU2025-9862',15127.22,'中俄贸易有限公司','2025-07-08','CN-RU-4733',61624.49,'2025-07-19','空运',8059.43,1355.95,'需提前预约清关','机械设备',4992.19,32.93,330.00,'CN-DOM-5688',52568.82,'2025-06-20','俄罗斯能源股份','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(61,'RU2025-7284',21205.90,'中国外贸总公司','2025-02-26','CN-RU-7716',70915.25,'2025-03-20','空运',5669.89,2948.14,'需提前预约清关','机械设备',1637.71,33.28,380.00,'CN-DOM-7513',119424.25,'2025-09-29','远东国际物流','李娜','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(62,'RU2025-4827',20935.44,'西伯利亚矿产公司','2025-06-15','CN-RU-1682',77167.73,'2025-03-28','铁路运输',8023.29,1410.43,'需提前预约清关','钢材',3951.49,39.48,250.00,'CN-DOM-6499',11956.56,'2025-11-18','远东国际物流','王强','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(63,'RU2025-2258',43217.48,'俄罗斯能源股份','2025-02-02','CN-RU-1958',37759.81,'2025-02-12','陆运特快',5699.22,672.28,'需提前预约清关','日用品',820.56,16.27,625.00,'CN-DOM-9705',31457.33,'2025-08-19','远东国际物流','张伟','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(64,'RU2025-9408',40408.33,'中国外贸总公司','2025-01-06','CN-RU-5297',40828.28,'2025-08-15','海运',1469.83,191.40,'需提前预约清关','家电产品',3468.13,21.17,34.00,'CN-DOM-5358',95854.07,'2025-01-18','俄罗斯能源股份','李娜','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(65,'RU2025-4156',57578.67,'中俄贸易有限公司','2025-01-23','CN-RU-6915',73429.94,'2025-07-08','海运',5563.10,2137.34,'需提前预约清关','日用品',3679.25,29.02,36.00,'CN-DOM-3650',75343.47,'2025-07-03','中俄贸易有限公司','张伟','2025-06-27 10:03:02','2025-07-08 18:27:39',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(66,'RU2025-9624',31463.47,'俄罗斯能源股份','2025-09-15','CN-RU-9726',7892.58,'2025-03-21','陆运普快',1071.52,690.32,'需提前预约清关','电子元件',977.11,33.51,498.00,'CN-DOM-9511',115965.50,'2025-07-13','西伯利亚矿产公司','王强','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(67,'RU2025-5877',70730.24,'中俄贸易有限公司','2025-04-04','CN-RU-4990',36823.20,'2025-06-11','陆运特快',8386.07,2786.77,'需提前预约清关','电子元件',703.60,47.50,715.00,'CN-DOM-7466',12761.51,'2025-04-07','中国外贸总公司','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(68,'RU2025-1119',38630.07,'中俄贸易有限公司','2025-09-16','CN-RU-5930',45697.33,'2025-05-22','陆运特快',8696.36,189.83,'需提前预约清关','家电产品',1880.40,10.57,341.00,'CN-DOM-4735',63501.45,'2025-11-19','远东国际物流','王强','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(69,'RU2025-7187',91309.56,'远东国际物流','2025-02-12','CN-RU-3221',6643.81,'2025-05-04','海运',8688.04,1901.94,'需提前预约清关','化工原料',4861.16,15.60,640.00,'CN-DOM-9158',54297.65,'2025-05-29','中国外贸总公司','李娜','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(70,'RU2025-5182',38197.51,'俄罗斯能源股份','2025-01-18','CN-RU-9401',35893.97,'2025-07-10','空运',9709.90,247.92,'需提前预约清关','电子元件',3347.80,13.51,329.00,'CN-DOM-2770',27446.17,'2025-10-01','中国外贸总公司','张伟','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(71,'RU2025-2444',20831.56,'莫斯科商贸集团','2025-06-09','CN-RU-3701',66191.93,'2025-08-25','陆运特快',3284.40,2909.92,'需提前预约清关','机械设备',4911.43,46.98,515.00,'CN-DOM-3569',99388.49,'2025-01-22','欧亚运输集团','孙婷','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(72,'RU2025-5673',47148.39,'欧亚运输集团','2025-07-09','CN-RU-8083',6370.31,'2025-09-22','陆运普快',6078.33,1306.41,'需提前预约清关','化工原料',3418.72,46.82,365.00,'CN-DOM-5709',37111.21,'2025-01-18','欧亚运输集团','孙婷','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(73,'RU2025-7360',85440.12,'中俄贸易有限公司','2025-11-13','CN-RU-1140',7503.10,'2025-03-01','陆运普快',6554.66,794.49,'需提前预约清关','食品',955.33,47.32,782.00,'CN-DOM-7542',117360.50,'2025-07-28','中俄贸易有限公司','李娜','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(74,'RU2025-9300',41662.60,'西伯利亚矿产公司','2025-03-24','CN-RU-3446',50161.04,'2025-07-27','空运',1998.54,2255.21,'需提前预约清关','机械设备',1312.23,23.48,584.00,'CN-DOM-4052',78004.88,'2025-02-17','莫斯科商贸集团','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(75,'RU2025-9002',83936.82,'莫斯科商贸集团','2025-02-11','CN-RU-3757',60451.78,'2025-05-04','陆运特快',3703.96,815.55,'需提前预约清关','日用品',2584.72,22.21,70.00,'CN-DOM-5488',89023.87,'2025-03-01','欧亚运输集团','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:44',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(76,'RU2025-4224',27711.13,'中俄贸易有限公司','2025-04-24','CN-RU-2809',2681.23,'2025-09-27','空运',3717.00,1548.06,'需提前预约清关','家电产品',1330.87,11.12,296.00,'CN-DOM-1260',104185.78,'2025-08-09','欧亚运输集团','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(77,'RU2025-2120',42793.78,'俄罗斯能源股份','2025-01-11','CN-RU-3650',9545.34,'2025-04-21','海运',798.85,145.41,'需提前预约清关','电子元件',1001.03,17.84,187.00,'CN-DOM-6038',97806.56,'2025-04-27','远东国际物流','孙婷','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(78,'RU2025-3279',48371.55,'莫斯科商贸集团','2025-06-20','CN-RU-1253',45172.03,'2025-08-10','铁路运输',2278.98,2017.39,'需提前预约清关','化工原料',4113.29,47.45,678.00,'CN-DOM-6399',48265.34,'2025-03-22','欧亚运输集团','刘洋','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(79,'RU2025-2095',82613.29,'西伯利亚矿产公司','2025-07-06','CN-RU-5222',5481.52,'2025-11-19','陆运特快',4136.29,1831.49,'需提前预约清关','化工原料',4001.78,43.96,327.00,'CN-DOM-7906',14374.76,'2025-11-10','远东国际物流','孙婷','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(80,'RU2025-2074',30068.08,'莫斯科商贸集团','2025-10-11','CN-RU-4254',48824.25,'2025-10-06','空运',1305.79,1206.91,'需提前预约清关','日用品',2309.47,31.55,380.00,'CN-DOM-3702',45417.93,'2025-05-09','俄罗斯能源股份','张伟','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(81,'RU2025-3485',53071.68,'西伯利亚矿产公司','2025-07-09','CN-RU-6836',13357.93,'2025-04-25','铁路运输',6430.81,1743.73,'需提前预约清关','日用品',2131.41,41.59,158.00,'CN-DOM-8013',112939.89,'2025-11-17','西伯利亚矿产公司','刘洋','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(82,'RU2025-7041',94692.47,'远东国际物流','2025-10-15','CN-RU-2089',71494.79,'2025-09-17','空运',4506.16,2976.54,'需提前预约清关','钢材',4207.47,27.03,418.00,'CN-DOM-1259',66209.90,'2025-04-14','欧亚运输集团','张伟','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(83,'RU2025-3169',94629.81,'欧亚运输集团','2025-01-07','CN-RU-2520',47150.62,'2025-03-03','陆运普快',7367.82,2846.11,'需提前预约清关','家电产品',2291.52,15.78,628.00,'CN-DOM-6714',68961.00,'2025-02-18','欧亚运输集团','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(84,'RU2025-6139',57267.40,'西伯利亚矿产公司','2025-02-01','CN-RU-8966',59498.56,'2025-07-09','海运',8715.61,1018.91,'需提前预约清关','机械设备',2439.98,5.49,484.00,'CN-DOM-8942',14091.54,'2025-01-01','中国外贸总公司','陈磊','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(85,'RU2025-8373',54078.54,'莫斯科商贸集团','2025-01-01','CN-RU-1390',16240.80,'2025-09-02','海运',820.38,2228.32,'需提前预约清关','家电产品',3585.04,15.06,307.00,'CN-DOM-6012',33322.45,'2025-01-02','中国外贸总公司','李娜','2025-06-27 10:03:02','2025-07-08 18:27:56',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(86,'RU2025-3237',64643.64,'远东国际物流','2025-02-02','CN-RU-9318',46817.26,'2025-03-10','空运',8484.04,690.91,'需提前预约清关','家电产品',3319.21,34.73,407.00,'CN-DOM-9115',87926.84,'2025-01-31','俄罗斯能源股份','李娜','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(87,'RU2025-8636',53896.74,'中俄贸易有限公司','2025-07-16','CN-RU-6337',38214.80,'2025-09-14','空运',712.12,2529.78,'需提前预约清关','电子元件',1054.25,32.27,812.00,'CN-DOM-1464',41801.87,'2025-08-22','中国外贸总公司','李娜','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(88,'RU2025-5362',98586.69,'俄罗斯能源股份','2025-01-24','CN-RU-3044',45253.74,'2025-08-12','铁路运输',784.75,2592.42,'需提前预约清关','家电产品',1421.54,39.55,902.00,'CN-DOM-4418',70987.17,'2025-06-10','欧亚运输集团','孙婷','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(89,'RU2025-7601',99837.70,'中国外贸总公司','2025-08-16','CN-RU-7649',13346.06,'2025-03-25','铁路运输',6011.71,1803.80,'需提前预约清关','食品',3463.53,46.84,606.00,'CN-DOM-1625',74507.45,'2025-12-04','欧亚运输集团','李娜','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(90,'RU2025-2891',72618.93,'中俄贸易有限公司','2025-12-19','CN-RU-6528',22597.48,'2025-10-17','铁路运输',6264.68,625.69,'需提前预约清关','机械设备',780.10,11.06,166.00,'CN-DOM-9875',24308.04,'2025-03-04','西伯利亚矿产公司','李娜','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(91,'RU2025-9120',96085.99,'西伯利亚矿产公司','2025-08-11','CN-RU-5635',61416.26,'2025-05-10','空运',5492.04,854.65,'需提前预约清关','化工原料',2676.88,8.66,804.00,'CN-DOM-9338',33614.85,'2025-07-16','中俄贸易有限公司','陈磊','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(92,'RU2025-2597',81457.53,'中国外贸总公司','2025-09-30','CN-RU-8601',71319.58,'2025-10-29','陆运普快',1118.16,2068.01,'需提前预约清关','家电产品',3310.93,5.07,19.00,'CN-DOM-1646',50913.82,'2025-03-20','远东国际物流','赵敏','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(93,'RU2025-7858',8833.59,'欧亚运输集团','2025-03-08','CN-RU-3305',2745.00,'2025-12-25','空运',1005.91,1415.49,'需提前预约清关','电子元件',3770.77,21.44,838.00,'CN-DOM-3728',102032.46,'2025-08-22','中国外贸总公司','孙婷','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(94,'RU2025-4248',99745.20,'莫斯科商贸集团','2025-06-10','CN-RU-9965',7593.87,'2025-04-21','空运',1491.14,2297.79,'需提前预约清关','钢材',1602.16,14.88,228.00,'CN-DOM-7712',33095.56,'2025-08-05','中俄贸易有限公司','张伟','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(95,'RU2025-4070',22407.35,'中国外贸总公司','2025-11-25','CN-RU-5250',15040.27,'2025-04-30','陆运普快',6254.84,2241.56,'需提前预约清关','食品',1499.24,29.25,872.00,'CN-DOM-2623',84320.31,'2025-04-26','西伯利亚矿产公司','刘洋','2025-06-27 10:03:02','2025-07-08 18:28:03',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(96,'RU2025-6983',88282.64,'莫斯科商贸集团','2025-06-20','CN-RU-5004',3895.66,'2025-07-13','铁路运输',3209.59,231.56,'需提前预约清关','食品',3060.62,21.04,139.00,'CN-DOM-1271',112999.34,'2025-07-19','西伯利亚矿产公司','刘洋','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(97,'RU2025-5826',96410.33,'远东国际物流','2025-04-21','CN-RU-6710',58673.03,'2025-08-14','陆运普快',7652.59,2082.11,'需提前预约清关','化工原料',1724.67,29.52,974.00,'CN-DOM-6713',18122.18,'2025-11-21','西伯利亚矿产公司','王强','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(98,'RU2025-6513',77095.51,'莫斯科商贸集团','2025-02-16','CN-RU-2074',29335.74,'2025-10-09','铁路运输',1276.37,2845.35,'需提前预约清关','家电产品',4059.24,31.61,530.00,'CN-DOM-2018',82689.24,'2025-05-18','中俄贸易有限公司','李娜','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(99,'RU2025-7233',88096.24,'中国外贸总公司','2025-08-18','CN-RU-7627',17508.46,'2025-09-27','空运',2954.52,2465.53,'需提前预约清关','机械设备',503.15,7.81,192.00,'CN-DOM-3437',28048.12,'2025-02-09','俄罗斯能源股份','孙婷','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(100,'RU2025-3928',31849.00,'莫斯科商贸集团','2025-04-16','CN-RU-5070',49915.66,'2025-02-06','铁路运输',2521.98,2033.33,'需提前预约清关','食品',2827.92,45.44,731.00,'CN-DOM-8509',104036.84,'2025-02-05','中俄贸易有限公司','张伟','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(101,'RU2025-1441',54861.82,'欧亚运输集团','2025-10-19','CN-RU-7596',75830.96,'2025-01-03','陆运普快',1764.02,905.46,'需提前预约清关','化工原料',1928.54,24.94,904.00,'CN-DOM-5343',65443.24,'2025-11-06','西伯利亚矿产公司','陈磊','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(102,'RU2025-1605',59071.09,'莫斯科商贸集团','2025-03-24','CN-RU-4250',37123.74,'2025-03-07','陆运普快',7326.88,2236.34,'需提前预约清关','电子元件',2137.24,21.74,706.00,'CN-DOM-4153',38256.67,'2025-01-03','中国外贸总公司','张伟','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(103,'RU2025-2932',73790.40,'中国外贸总公司','2025-06-22','CN-RU-1562',43484.46,'2025-11-30','铁路运输',4743.80,2149.57,'需提前预约清关','电子元件',1392.85,26.79,942.00,'CN-DOM-3229',57261.71,'2025-01-01','俄罗斯能源股份','孙婷','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(104,'RU2025-2008',95289.57,'中国外贸总公司','2025-02-09','CN-RU-8041',42495.41,'2025-09-10','海运',751.24,783.95,'需提前预约清关','电子元件',3267.36,30.42,493.00,'CN-DOM-1462',78254.04,'2025-06-12','中俄贸易有限公司','张伟','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(105,'RU2025-9343',58896.89,'俄罗斯能源股份','2025-06-12','CN-RU-1894',10056.89,'2025-08-30','海运',5932.03,2494.69,'需提前预约清关','食品',3091.85,23.79,454.00,'CN-DOM-4118',49188.32,'2025-11-01','俄罗斯能源股份','孙婷','2025-06-27 10:03:02','2025-07-08 18:28:08',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(106,'RU2025-6508',32154.57,'中国外贸总公司','2025-08-25','CN-RU-7575',65192.62,'2025-04-28','空运',8948.87,816.11,'需提前预约清关','日用品',3772.68,7.88,787.00,'CN-DOM-1490',115787.12,'2025-12-22','俄罗斯能源股份','赵敏','2025-06-27 10:03:02','2025-07-09 11:57:46',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(107,'RU2025-8761',21556.70,'欧亚运输集团','2025-09-01','CN-RU-2627',46259.98,'2025-12-18','空运',4122.04,794.04,'需提前预约清关','家电产品',4764.83,44.10,838.00,'CN-DOM-3757',99705.65,'2025-11-08','远东国际物流','陈磊','2025-06-27 10:03:02','2025-07-09 11:57:46',NULL,NULL,NULL,3,NULL,NULL,NULL,NULL,NULL),(108,'20250627',888.00,'ebuy','2025-06-18','dfdsf-1421',666.00,'2025-06-01','陆运普快',200.00,150.00,'加急','数据线',200.00,6.20,5125.00,'2021245-jkcs',580.00,'2025-06-22','江苏苏钻有限公司','杨帆','2025-06-27 10:20:48','2025-07-08 18:27:39',NULL,1,69,3,NULL,NULL,NULL,NULL,NULL),(109,'1',1.10,'1',NULL,'1',1.10,NULL,'空运',1.10,1.10,'','1',1.10,1.10,1.10,'',1.10,NULL,'','1','2025-06-27 10:35:36','2025-07-08 18:27:39',NULL,1,1,3,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `or_foreign_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `or_order_log`
--

DROP TABLE IF EXISTS `or_order_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `or_order_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `customer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `manager` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `goods_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `internal_ord_amt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `buy_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `internal_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `international_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `trf_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `internal_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `clienttt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `user_russ` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tax_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tax_extra_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ct_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ct_extr_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `russclear_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `russ_extr_fees` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `markup` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cost_amt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sales_amt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `profit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `explaintt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `r1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `r2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `r3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `r4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `or_order_log_ord_no` (`order_no`) USING BTREE,
  KEY `FK9h3u1g4yx7ssu8j4x4xquex4x` (`update_by`) USING BTREE,
  KEY `FKqsa7g3y6ipjmn7v8d6j2x7x0w` (`create_by`) USING BTREE,
  CONSTRAINT `FK9h3u1g4yx7ssu8j4x4xquex4x` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqsa7g3y6ipjmn7v8d6j2x7x0w` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `or_order_log`
--

LOCK TABLES `or_order_log` WRITE;
/*!40000 ALTER TABLE `or_order_log` DISABLE KEYS */;
INSERT INTO `or_order_log` VALUES (1,NULL,'2023-05-30 11:17:41','2023-05-30 11:20:38',1,1,3,'20230530111741','111','222','333','444','555','666','777',NULL,'888',NULL,'999','111','222','333','444','555',NULL,'20230530','666','777',NULL,'88',NULL,NULL,NULL,NULL),(2,NULL,'2023-11-03 11:19:19','2023-11-03 11:19:19',1,1,3,'20231103111919','测试客户','工号','1','2','3','4','5',NULL,'6',NULL,'7','8','9','1','2','3',NULL,'20231103','4','5',NULL,'6',NULL,NULL,NULL,NULL),(3,NULL,'2023-11-03 12:10:11','2023-11-03 12:10:11',3,3,3,'20231103121011','aaaaa','？','MK77-CC1101','2000','','','',NULL,'',NULL,'','','','','','',NULL,'20231103','','',NULL,'',NULL,NULL,NULL,NULL),(4,NULL,'2023-11-03 12:14:18','2023-11-03 12:16:46',48,47,3,'20231103121418','123123123','工号是啥','MK77-CC1010','11','3','5','60',NULL,'国外收款',NULL,'1','2','3','4','5','6',NULL,'20231102','7','8',NULL,NULL,NULL,NULL,NULL,NULL),(5,NULL,'2023-11-03 12:15:56','2023-11-03 12:15:56',47,47,3,'20231103121556',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'','1','2','3','4','5',NULL,NULL,'6','',NULL,NULL,NULL,NULL,NULL,NULL),(6,NULL,'2023-11-08 15:05:36','2023-11-08 15:29:19',49,49,1,'20231108150536','长城','张子轩','MK77-GW23112-1','','','','64$',NULL,'057汽配（体积0.011毛重6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-11-3',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(7,NULL,'2023-11-08 15:09:24','2023-11-08 15:20:35',49,49,1,'20231108150923','长城','张子轩','MK77-GW231028-2','','','','787$',NULL,'057汽配（体积0.722毛重75.9）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-11-2',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(8,NULL,'2023-11-08 15:10:48','2023-11-08 15:20:47',49,49,1,'20231108151047','长城','李善振','MK77-LL1020-1(MK77-CC1020)','','','','44$',NULL,'057汽配（体积0.048毛重9.1）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-28',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(9,NULL,'2023-11-08 15:12:39','2023-11-08 15:21:17',49,49,1,'20231108151239','1B','李善振','SPB77-B1018-1','','','','51$',NULL,'057避震器（体积0.037毛重11.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-26',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(10,NULL,'2023-11-08 15:14:08','2023-11-08 15:21:33',49,49,1,'20231108151407','艾力','艾力','SPB77-HT-1','','','','35$',NULL,'057核桃（体积0.033毛重6.7）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-24',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(11,NULL,'2023-11-08 15:18:02','2023-11-08 15:18:02',49,49,1,'20231108151802','长城','李善振','MK77-CC1008-1','','','100￥','32$',NULL,'057汽配（体积0.076毛重7）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-12',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(12,NULL,'2023-11-08 15:23:58','2023-11-08 15:23:58',49,49,1,'20231108152357','长城','张子轩','MK77-GW23107-1','','','','370$',NULL,'057配件（体积0.045毛重32）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-9',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(13,NULL,'2023-11-08 15:25:56','2023-11-08 15:25:56',49,49,1,'20231108152556','长城','张子轩','MK77-GW23101-1','','','','655$',NULL,'057汽配（体积0.224毛重67.4）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-5',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(14,NULL,'2023-11-08 15:28:59','2023-11-08 15:28:59',49,49,1,'20231108152858','长城','张子轩','MK77-1004-1（MK77-GW23928-1）','','','','133$',NULL,'057制动盘（体积0.037毛重13.5）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-5',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(15,NULL,'2023-11-08 15:38:52','2023-11-08 15:46:50',49,49,1,'20231108153851','长城','李善振','MK77-0926CJJ-1','','','130￥','68$',NULL,'057汽配（体积0.072毛重16）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-30',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(16,NULL,'2023-11-08 15:45:08','2023-11-08 15:45:08',49,49,1,'20231108154508','长城','李善振','MK77-CC0926-1','','','145￥','36$',NULL,'057汽配（体积0.092毛重8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-30',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(17,NULL,'2023-11-08 15:48:18','2023-11-08 15:48:18',49,49,1,'20231108154818','长城','张子轩','MK77-GW23923-1','','','','208＄',NULL,'057汽配（体积0.075毛重21.1）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-26',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(18,NULL,'2023-11-08 15:51:01','2023-11-08 15:51:01',49,49,1,'20231108155101','柴部长（代采购）','李善振','MK77-CBZ0920-1','','','360￥','554＄',NULL,'057叉车配件（体积0.123毛重138.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-22',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(19,NULL,'2023-11-08 15:54:04','2023-11-08 15:54:04',49,49,1,'20231108155404','','','MK77-A201-2','','','','151$',NULL,'057T恤马甲（体积0.163毛重31.7）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-22',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(20,NULL,'2023-11-08 15:56:12','2023-11-08 15:56:12',49,49,1,'20231108155611','长城','李善振','MK77-CC0911-1','','','135￥','10＄',NULL,'057汽配（体积0.06毛重8.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-16',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(21,NULL,'2023-11-08 16:06:55','2023-11-08 16:06:55',49,49,1,'20231108160655','长城','李善振','MK77-CC0904-5','','','2280￥','883＄',NULL,'057汽配（体积2.114毛重151.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-13',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(22,NULL,'2023-11-08 16:08:01','2023-11-08 16:08:01',49,49,1,'20231108160801','长城','张子轩','MK77-GW2394-1','','','','152.61＄',NULL,'057汽配（体积0.062毛重15.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-12',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(23,NULL,'2023-11-08 16:09:02','2023-11-08 16:09:02',49,49,1,'20231108160901','长城','张子轩','MK77-GW2397-1','','','','72.81＄',NULL,'057泡沫隔音板（体积0.237毛重7.2）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-12',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(24,NULL,'2023-11-08 16:10:41','2023-11-08 16:10:41',49,49,1,'20231108161040','长城','张子轩','MK77-GW23911-1','','','','1633.57$',NULL,'057汽配（体积0.74毛重166.8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-12',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(25,NULL,'2023-11-08 16:11:32','2023-11-08 16:11:32',49,49,1,'20231108161132','长城','张子轩','MK77-GW2394-1','','','','153$',NULL,'057汽配（体积0.062毛重15.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-8',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(26,NULL,'2023-11-08 16:25:47','2023-11-09 11:21:48',49,49,1,'20231108162546','柴部长（代采购）','李善振','MK77-1108CBZ','','','','',NULL,'速辰柴部长胶（体积12.68毛重2606.5）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-11-8',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(27,NULL,'2023-11-08 16:31:47','2023-11-08 16:31:47',49,49,1,'20231108163146','长城','张子轩','Y6720-2（MK77-GT231031）','','','','383.38$',NULL,'786轮胎（体积0.0797毛重64.8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-11-5',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(28,NULL,'2023-11-08 16:36:14','2023-11-08 16:36:14',49,49,1,'20231108163614','长城','张子轩','Y6687-1（MK77-GW23107）','','','','38.8$',NULL,'786车架支撑板（体积0.062毛重10.8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-21',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(29,NULL,'2023-11-08 16:39:32','2023-11-08 16:39:32',49,49,1,'20231108163932','长城','张子轩','Y6675-1','','','','150.77$',NULL,'786汽配（体积0.13毛重65.2）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-10-15',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(30,NULL,'2023-11-08 16:54:51','2023-11-09 11:37:09',49,49,1,'20231108165451','serg','李善振','GW15632-4','','','','780.12$',NULL,'786琴弦（体积1.173毛重260）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-9-19',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(31,NULL,'2023-11-08 16:57:52','2023-11-09 11:37:03',49,49,1,'20231108165752','serg','李善振','GW15522-11','','','','1400.47$',NULL,'786琴弦（体积2.181毛重482.1）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-8-31',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(32,NULL,'2023-11-08 16:58:47','2023-11-09 11:36:52',49,49,1,'20231108165847','serg','李善振','GW15414-26-1','','','','985.03',NULL,'786琴弦（体积1.426毛重322.5）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-8-12',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(33,NULL,'2023-11-08 16:59:59','2023-11-09 11:36:45',49,49,1,'20231108165958','serg','李善振','GW15364-17','','','','2100.94',NULL,'786琴弦（体积3.274毛重682.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-7-29',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(34,NULL,'2023-11-08 17:02:13','2023-11-09 11:36:37',49,49,1,'20231108170213','serg','李善振','GW15176-14','','','','1713.4',NULL,'786琴弦（体积2.748毛重559）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-7-2',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(35,NULL,'2023-11-08 17:06:56','2023-11-09 11:36:28',49,49,1,'20231108170656','serg','李善振','GW15142-11','','','','1346.73$',NULL,'786琴弦（体积2.059毛重396.5）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-28',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(36,NULL,'2023-11-08 17:11:19','2023-11-08 17:20:08',49,49,1,'20231108171118','大张总','大张总','Y6448-1（SPB77-CY）','','','58￥','34.44$',NULL,'786茶叶（体积0.062毛重7.6）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-25',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(37,NULL,'2023-11-08 17:15:23','2023-11-08 17:19:21',49,49,1,'20231108171522','1B','李善振','Y6437-1（SPB77-B0615）','','','194￥','120.79$',NULL,'786灯条（体积0.28毛重33）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-21',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(38,NULL,'2023-11-08 17:18:54','2023-11-08 17:18:54',49,49,1,'20231108171853','长城','李善振','Y6438-1（MK77-CC0613YB）','','','155￥','41.51$',NULL,'786仪表（体积0.104毛重8.4）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-21',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(39,NULL,'2023-11-08 17:22:54','2023-11-08 17:22:54',49,49,1,'20231108172253','长城','李善振','Y6434-5（MK77-CC0605WYH）','','','430￥','548.15$',NULL,'786编织袋（体积1.15毛重207）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-20',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(40,NULL,'2023-11-08 17:26:26','2023-11-09 11:33:42',49,49,1,'20231108172626','serg','李善振','GW15082-31','','','','3931.89$',NULL,'786琴弦（体积6.743毛重1204.2）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-18',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(41,NULL,'2023-11-08 17:28:45','2023-11-08 17:28:45',49,49,1,'20231108172844','长城','张子轩','Y6431-1（MK77-GW23612）','','','87￥','27.55$',NULL,'786汽配（体积0.012毛重10.2）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-18',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(42,NULL,'2023-11-09 11:44:50','2023-11-09 11:44:50',49,49,1,'20231109114450','长城','李善振','Y6423-1（MK77-CC0530）','','','82￥','45.92$',NULL,'786汽配（体积0.092毛重6.4）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-16',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(43,NULL,'2023-11-09 11:50:32','2023-11-09 11:50:32',49,49,1,'20231109115032','长城','李善振','Y6424-1（MK77-CC0510CJJ）','','','76￥','26.12$',NULL,'786汽配（体积0.057毛重5.8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-16',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(44,NULL,'2023-11-09 11:51:40','2023-11-09 11:51:40',49,49,1,'20231109115139','serg','李善振','GW15021-6','','','','1148.85$',NULL,'786（体积1.327毛重303.5）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-6-10',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(45,NULL,'2023-11-09 13:19:47','2023-11-09 13:19:47',49,49,1,'20231109131947','长城','张子轩','Y6391-1（MK77-GW23516）','','','120￥','18.98$',NULL,'786汽配（体积0.022毛重5.8）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-5-29',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(46,NULL,'2023-11-09 13:21:36','2023-11-09 13:21:36',49,49,1,'20231109132136','serg','李善振','GW14911-31','','','','4420.81$',NULL,'786琴弦（体积6.133毛重1174）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-5-23',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(47,NULL,'2023-11-09 13:22:42','2023-11-09 13:22:42',49,49,1,'20231109132241','serg','李善振','GW14857-10','','','','1275.48$',NULL,'786琴弦（体积1.553毛重365）',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2023-5-14',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL),(48,NULL,'2025-07-08 17:11:23','2025-07-08 17:12:19',68,68,1,'20250708171122','stas moto','ms01','chel-74-ks-1','60120元','5465元','','',NULL,'7-8日natasha收取68 万卢布',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'08-07',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `or_order_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_amount_detail`
--

DROP TABLE IF EXISTS `psm_amount_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_amount_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `acc_type` varchar(255) DEFAULT NULL,
  `amount_dollar` decimal(19,2) DEFAULT NULL,
  `amount_ori` decimal(19,2) DEFAULT NULL,
  `biz_date` datetime DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `curr_ori` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `oper` varchar(100) DEFAULT NULL COMMENT '操作员',
  `biz_type` varchar(100) DEFAULT NULL COMMENT '业务类型',
  `biz_code` varchar(100) DEFAULT NULL COMMENT '业务编码',
  PRIMARY KEY (`id`),
  KEY `FKbihvunblipk373qnaanvaw57c` (`create_by`),
  KEY `FK9oygspq6tdbnief2t3cujfeus` (`update_by`),
  CONSTRAINT `FK9oygspq6tdbnief2t3cujfeus` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKbihvunblipk373qnaanvaw57c` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_amount_detail`
--

LOCK TABLES `psm_amount_detail` WRITE;
/*!40000 ALTER TABLE `psm_amount_detail` DISABLE KEYS */;
INSERT INTO `psm_amount_detail` VALUES (1,NULL,NULL,100.00,NULL,'2025-07-30 19:38:31',NULL,NULL,1,'2025-07-30 19:38:31',1,1,NULL,NULL,NULL),(2,NULL,NULL,2000.00,NULL,'2025-07-30 21:22:49',NULL,NULL,1,'2025-07-30 21:22:49',1,1,NULL,NULL,NULL),(3,NULL,NULL,1500.00,NULL,'2025-07-30 21:22:49',NULL,NULL,1,'2025-07-30 21:22:49',1,1,NULL,NULL,NULL);
/*!40000 ALTER TABLE `psm_amount_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_cost_record`
--

DROP TABLE IF EXISTS `psm_cost_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_cost_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `biz_type` varchar(255) DEFAULT NULL,
  `cost_type` varchar(255) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `fund_flow` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `amount_id` bigint DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `biz_id` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `biz_no` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKrfdvhfoakbym2a5yydpk88ya2` (`biz_id`),
  KEY `FKon59esgyvel0q0m4dy08ph6tb` (`amount_id`),
  KEY `FKeuxxpja9rlt4kx0lslr8rm04b` (`create_by`),
  KEY `FKcskn00vxc52vgh7f6tbmrpenn` (`biz_no`),
  KEY `FK3s1xj3ucsg58nfqve84sb6tet` (`update_by`),
  CONSTRAINT `FK3s1xj3ucsg58nfqve84sb6tet` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKbyvuckohycbh8xbygi6f8gd7v` FOREIGN KEY (`biz_no`) REFERENCES `psm_purchase_main` (`pur_con_no`),
  CONSTRAINT `FKcskn00vxc52vgh7f6tbmrpenn` FOREIGN KEY (`biz_no`) REFERENCES `psm_trans_main` (`trans_con_no`),
  CONSTRAINT `FKeuxxpja9rlt4kx0lslr8rm04b` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKon59esgyvel0q0m4dy08ph6tb` FOREIGN KEY (`amount_id`) REFERENCES `psm_amount_detail` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_cost_record`
--

LOCK TABLES `psm_cost_record` WRITE;
/*!40000 ALTER TABLE `psm_cost_record` DISABLE KEYS */;
INSERT INTO `psm_cost_record` VALUES (3,'PUR','TAX_FREE','2025-07-30 19:38:31','PLUS','asdfasdf',1,'2025-07-30 19:38:31',1,1,NULL,1,NULL);
/*!40000 ALTER TABLE `psm_cost_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_customer`
--

DROP TABLE IF EXISTS `psm_customer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_customer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `cust_detail` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '业务信息',
  `cont_name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '联系人',
  `cust_country` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '客户所在国家',
  `create_date` datetime DEFAULT NULL,
  `cust_org` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '客户所属组织或个人',
  `cust_type` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '客户类型（公司/个人）',
  `cont_phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '联系电话',
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKhk32vyucoh7b2kbhwwgxfc2gx` (`create_by`),
  KEY `FK32kxyp1thl0xo31wj0660w6kg` (`update_by`),
  CONSTRAINT `FK32kxyp1thl0xo31wj0660w6kg` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKhk32vyucoh7b2kbhwwgxfc2gx` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_customer`
--

LOCK TABLES `psm_customer` WRITE;
/*!40000 ALTER TABLE `psm_customer` DISABLE KEYS */;
INSERT INTO `psm_customer` VALUES (1,'TEST_测试业务信息_1','张三','CN','2025-07-25 18:44:49','北京张三','COM','15117777777',1,'2025-07-25 18:44:49',1,1),(2,'TEST_测试业务信息_2','李四','CN','2025-07-25 18:44:49','上海李四','COM','15117777777',1,'2025-07-25 18:44:49',1,1),(3,'TEST_测试业务信息_3','王五','CN','2025-07-25 18:44:49','广州王五','COM','15117777777',1,'2025-07-25 18:44:49',1,1),(4,'TEST_测试业务信息_4','赵六','CN','2025-07-25 18:44:49','深圳赵六','COM','15117777777',1,'2025-07-25 18:44:49',1,1);
/*!40000 ALTER TABLE `psm_customer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_exchange_rate`
--

DROP TABLE IF EXISTS `psm_exchange_rate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_exchange_rate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_date` datetime DEFAULT NULL,
  `currency_code` varchar(255) DEFAULT NULL,
  `currency_name` varchar(255) DEFAULT NULL,
  `currency_symbol` varchar(255) DEFAULT NULL,
  `rate` decimal(10,5) DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK4rr0h63u692kuila2p87l6dhv` (`create_by`),
  KEY `FKov65dh94tgbr9rkmkri3dninh` (`update_by`),
  CONSTRAINT `FK4rr0h63u692kuila2p87l6dhv` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKov65dh94tgbr9rkmkri3dninh` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_exchange_rate`
--

LOCK TABLES `psm_exchange_rate` WRITE;
/*!40000 ALTER TABLE `psm_exchange_rate` DISABLE KEYS */;
INSERT INTO `psm_exchange_rate` VALUES (2,'2025-07-29 21:27:50','USD','美元','$',1.00000,1,'2025-07-29 21:27:50',1,1),(4,'2025-07-29 22:15:30','RMB','人民币','¥',0.13920,1,'2025-07-29 22:15:30',1,1),(5,'2025-07-29 22:15:50','RUB','卢布','₽',0.01271,1,'2025-07-29 22:39:33',1,1);
/*!40000 ALTER TABLE `psm_exchange_rate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_goods_info`
--

DROP TABLE IF EXISTS `psm_goods_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_goods_info` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `arrival_date` datetime DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `goods_cbm` decimal(19,2) DEFAULT NULL,
  `goods_name` varchar(255) DEFAULT NULL,
  `goods_no` varchar(255) DEFAULT NULL,
  `goods_weight` decimal(19,2) DEFAULT NULL,
  `package_count` bigint DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `shipment_date` datetime DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `trans_type` varchar(255) DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `warehouse_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKtig2gyv8y89ck6bsan0c15ro3` (`create_by`),
  KEY `FK6cdlll4mxhymsts2fg8ditd62` (`update_by`),
  CONSTRAINT `FK6cdlll4mxhymsts2fg8ditd62` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKtig2gyv8y89ck6bsan0c15ro3` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_goods_info`
--

LOCK TABLES `psm_goods_info` WRITE;
/*!40000 ALTER TABLE `psm_goods_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_goods_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_outbound_order`
--

DROP TABLE IF EXISTS `psm_outbound_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_outbound_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `actual_amount` decimal(19,2) DEFAULT NULL,
  `contract_no` varchar(255) DEFAULT NULL,
  `cost_amount` decimal(19,2) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `sales` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `pur_amount_cost` decimal(19,2) DEFAULT NULL,
  `pur_con_no` varchar(255) DEFAULT NULL,
  `sales_no` varchar(255) DEFAULT NULL,
  `biz_no` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKnd8ielcmpxemhy9t143caloc0` (`create_by`),
  KEY `FKtavyy4oejegetmjq9hssxhu4w` (`update_by`),
  KEY `FK66fc0mx2x6x4tnhc3xddva6o` (`biz_no`),
  CONSTRAINT `FK66fc0mx2x6x4tnhc3xddva6o` FOREIGN KEY (`biz_no`) REFERENCES `psm_purchase_main` (`pur_con_no`),
  CONSTRAINT `FKnd8ielcmpxemhy9t143caloc0` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKtavyy4oejegetmjq9hssxhu4w` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_outbound_order`
--

LOCK TABLES `psm_outbound_order` WRITE;
/*!40000 ALTER TABLE `psm_outbound_order` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_outbound_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_purchase_main`
--

DROP TABLE IF EXISTS `psm_purchase_main`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_purchase_main` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `con_end_date` datetime DEFAULT NULL,
  `con_start_date` datetime DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `payee_type` varchar(255) DEFAULT NULL,
  `pur_con_no` varchar(255) DEFAULT NULL,
  `pur_con_status` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `sale_no` varchar(255) DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `cust_id` bigint DEFAULT NULL,
  `pur_amount_con_id` bigint DEFAULT NULL,
  `pur_amount_payee_id` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_chdl93ajaahbnjyjrfahsxo84` (`pur_con_no`),
  KEY `FKa1q92port7c2ydhbxispgh7x6` (`create_by`),
  KEY `FK52twipex70ncq7ordymo4ddyn` (`cust_id`),
  KEY `FKhe72ipf3n7qu6w4krb776g2u2` (`pur_amount_con_id`),
  KEY `FK4uvl0n9la3q103pv90rudsjam` (`pur_amount_payee_id`),
  KEY `FKbaynkkc6yi0n8wphhi0q2atn4` (`update_by`),
  CONSTRAINT `FK4uvl0n9la3q103pv90rudsjam` FOREIGN KEY (`pur_amount_payee_id`) REFERENCES `psm_amount_detail` (`id`),
  CONSTRAINT `FK52twipex70ncq7ordymo4ddyn` FOREIGN KEY (`cust_id`) REFERENCES `psm_customer` (`id`),
  CONSTRAINT `FKa1q92port7c2ydhbxispgh7x6` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKbaynkkc6yi0n8wphhi0q2atn4` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKhe72ipf3n7qu6w4krb776g2u2` FOREIGN KEY (`pur_amount_con_id`) REFERENCES `psm_amount_detail` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_purchase_main`
--

LOCK TABLES `psm_purchase_main` WRITE;
/*!40000 ALTER TABLE `psm_purchase_main` DISABLE KEYS */;
INSERT INTO `psm_purchase_main` VALUES (2,'2025-07-31 00:00:00','2025-07-30 00:00:00','2025-07-30 21:22:49','L_P','pur001','INIT','asdfasd','sales001',1,'2025-07-30 21:22:49',1,1,3,3,1);
/*!40000 ALTER TABLE `psm_purchase_main` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_trans_goods`
--

DROP TABLE IF EXISTS `psm_trans_goods`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_trans_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_date` datetime DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `arrival_date` datetime DEFAULT NULL,
  `goods_code` varchar(255) DEFAULT NULL,
  `goods_no` varchar(255) DEFAULT NULL,
  `quantity` bigint DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `shipment_date` datetime DEFAULT NULL,
  `store_in_date` datetime DEFAULT NULL,
  `trans_type` varchar(255) DEFAULT NULL,
  `volume` decimal(19,2) DEFAULT NULL,
  `weight` decimal(19,2) DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `trans_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKqvkmep2nydcli1328drqnfrkg` (`create_by`),
  KEY `FKa4jiv4xwh5o006566kvpn43tj` (`update_by`),
  KEY `FKt2dybfe3tqo27kbq3u6dsv5eg` (`trans_id`),
  CONSTRAINT `FKa4jiv4xwh5o006566kvpn43tj` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKqvkmep2nydcli1328drqnfrkg` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKt2dybfe3tqo27kbq3u6dsv5eg` FOREIGN KEY (`trans_id`) REFERENCES `psm_trans_main` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_trans_goods`
--

LOCK TABLES `psm_trans_goods` WRITE;
/*!40000 ALTER TABLE `psm_trans_goods` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_trans_goods` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_trans_main`
--

DROP TABLE IF EXISTS `psm_trans_main`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_trans_main` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_date` datetime DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `account_status` varchar(255) DEFAULT NULL,
  `consignee_id` varchar(255) DEFAULT NULL,
  `payee_date` datetime DEFAULT NULL,
  `payee_type` varchar(255) DEFAULT NULL,
  `pur_con_no` varchar(255) DEFAULT NULL,
  `remark` varchar(255) DEFAULT NULL,
  `sales_no` varchar(255) DEFAULT NULL,
  `shipper_id` varchar(255) DEFAULT NULL,
  `trans_con_no` varchar(255) DEFAULT NULL,
  `trans_status` varchar(255) DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `balance_amount_id` bigint DEFAULT NULL,
  `prepayment_amount_id` bigint DEFAULT NULL,
  `receivable_amount_id` bigint DEFAULT NULL,
  `received_amount_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UK_6ex5fnwkf16n0q5bsdgk4lvnb` (`trans_con_no`),
  KEY `FKfk2ymly35xttmsdj0ch42rx63` (`create_by`),
  KEY `FKp2g4tamt0m67gu98rcen8thtw` (`update_by`),
  KEY `FKd79t5tpklx64qckcnijqvbplr` (`balance_amount_id`),
  KEY `FKrc46kkikcnr7g354ef3a5ecqc` (`prepayment_amount_id`),
  KEY `FK5sa9bp9d7r1juhqfo178d46so` (`receivable_amount_id`),
  KEY `FKkhpial09cg42jqis0knwk4ouy` (`received_amount_id`),
  CONSTRAINT `FK5sa9bp9d7r1juhqfo178d46so` FOREIGN KEY (`receivable_amount_id`) REFERENCES `psm_amount_detail` (`id`),
  CONSTRAINT `FKd79t5tpklx64qckcnijqvbplr` FOREIGN KEY (`balance_amount_id`) REFERENCES `psm_amount_detail` (`id`),
  CONSTRAINT `FKfk2ymly35xttmsdj0ch42rx63` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKkhpial09cg42jqis0knwk4ouy` FOREIGN KEY (`received_amount_id`) REFERENCES `psm_amount_detail` (`id`),
  CONSTRAINT `FKp2g4tamt0m67gu98rcen8thtw` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKrc46kkikcnr7g354ef3a5ecqc` FOREIGN KEY (`prepayment_amount_id`) REFERENCES `psm_amount_detail` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_trans_main`
--

LOCK TABLES `psm_trans_main` WRITE;
/*!40000 ALTER TABLE `psm_trans_main` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_trans_main` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_trans_payment`
--

DROP TABLE IF EXISTS `psm_trans_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_trans_payment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_date` datetime DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `trans_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKr9bq4l2flimn3f03wfp7tr9om` (`create_by`),
  KEY `FKe7kjx6m5x1k63an8offr0xd3p` (`update_by`),
  KEY `FKbmq9ga4jyedph5tgpxhgqgkc` (`trans_id`),
  CONSTRAINT `FKbmq9ga4jyedph5tgpxhgqgkc` FOREIGN KEY (`trans_id`) REFERENCES `psm_trans_main` (`id`),
  CONSTRAINT `FKe7kjx6m5x1k63an8offr0xd3p` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKr9bq4l2flimn3f03wfp7tr9om` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_trans_payment`
--

LOCK TABLES `psm_trans_payment` WRITE;
/*!40000 ALTER TABLE `psm_trans_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_trans_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `psm_trans_waybill`
--

DROP TABLE IF EXISTS `psm_trans_waybill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `psm_trans_waybill` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `create_date` datetime DEFAULT NULL,
  `status` tinyint DEFAULT NULL,
  `update_date` datetime DEFAULT NULL,
  `create_by` bigint DEFAULT NULL,
  `update_by` bigint DEFAULT NULL,
  `trans_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK1glb4paduy881k7j32koj4sqn` (`create_by`),
  KEY `FKn9xloevfvaugqfj5bk3l6ehrh` (`update_by`),
  KEY `FKlpt4123d7gfma6nyy0c4bwo2a` (`trans_id`),
  CONSTRAINT `FK1glb4paduy881k7j32koj4sqn` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`),
  CONSTRAINT `FKlpt4123d7gfma6nyy0c4bwo2a` FOREIGN KEY (`trans_id`) REFERENCES `psm_trans_main` (`id`),
  CONSTRAINT `FKn9xloevfvaugqfj5bk3l6ehrh` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `psm_trans_waybill`
--

LOCK TABLES `psm_trans_waybill` WRITE;
/*!40000 ALTER TABLE `psm_trans_waybill` DISABLE KEYS */;
/*!40000 ALTER TABLE `psm_trans_waybill` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_action_log`
--

DROP TABLE IF EXISTS `sys_action_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_action_log` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `clazz` varchar(255) DEFAULT NULL,
  `create_date` datetime DEFAULT NULL,
  `ipaddr` varchar(255) DEFAULT NULL,
  `message` text,
  `method` varchar(255) DEFAULT NULL,
  `model` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `oper_name` varchar(255) DEFAULT NULL,
  `record_id` bigint DEFAULT NULL,
  `type` tinyint DEFAULT NULL,
  `oper_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK32gm4dja0jetx58r9dc2uljiu` (`oper_by`),
  CONSTRAINT `FK32gm4dja0jetx58r9dc2uljiu` FOREIGN KEY (`oper_by`) REFERENCES `sys_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_action_log`
--

LOCK TABLES `sys_action_log` WRITE;
/*!40000 ALTER TABLE `sys_action_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_action_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '部门名称',
  `pid` bigint DEFAULT NULL COMMENT '父级ID',
  `pids` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所有父级编号',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建用户',
  `update_by` bigint DEFAULT NULL COMMENT '更新用户',
  `status` tinyint DEFAULT NULL COMMENT '状态（1:正常,2:冻结,3:删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKifwd1h4ciusl3nnxrpfpv316u` (`create_by`) USING BTREE,
  KEY `FK83g45s1cjqqfpifhulqhv807m` (`update_by`) USING BTREE,
  CONSTRAINT `FK83g45s1cjqqfpifhulqhv807m` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKifwd1h4ciusl3nnxrpfpv316u` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (1,'总公司',0,'[0]',1,'','2018-12-02 17:41:23','2019-02-23 02:41:28',1,1,1),(2,'技术部门',1,'[0],[1]',1,'','2018-12-02 17:51:04','2019-04-27 13:12:46',1,1,1),(3,'市场部门',1,'[0],[1]',2,'','2018-12-02 17:51:42','2019-04-27 13:12:20',1,1,1),(4,'财务部门',1,'[0],[1]',3,'','2018-12-02 17:51:55','2025-01-24 09:32:29',1,1,1),(5,'测试部门',1,'[0],[1]',4,'','2018-12-02 17:52:07','2019-04-27 13:12:20',1,1,1),(6,'物流部门',1,'[0],[1]',5,'','2022-03-28 15:49:02','2022-03-28 15:49:02',1,1,1);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict`
--

DROP TABLE IF EXISTS `sys_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字典名称',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '字典键名',
  `type` tinyint DEFAULT NULL COMMENT '字典类型',
  `value` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci COMMENT '字典键值',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建用户',
  `update_by` bigint DEFAULT NULL COMMENT '更新用户',
  `status` tinyint DEFAULT NULL COMMENT '状态（1:正常,2:冻结,3:删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKag4shuprf2tjot9i1mhh37kk6` (`create_by`) USING BTREE,
  KEY `FKoyng5jlifhsme0gc1lwiub0lr` (`update_by`) USING BTREE,
  CONSTRAINT `FKag4shuprf2tjot9i1mhh37kk6` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKoyng5jlifhsme0gc1lwiub0lr` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict`
--

LOCK TABLES `sys_dict` WRITE;
/*!40000 ALTER TABLE `sys_dict` DISABLE KEYS */;
INSERT INTO `sys_dict` VALUES (1,'数据状态','DATA_STATUS',2,'1:正常,2:冻结,3:删除','','2018-10-05 16:03:11','2018-10-05 16:11:41',1,1,1),(2,'字典类型','DICT_TYPE',2,'2:键值对','','2018-10-05 20:08:55','2019-01-17 23:39:23',1,1,1),(3,'用户性别','USER_SEX',2,'1:男,2:女','','2018-10-05 20:12:32','2018-10-05 20:12:32',1,1,1),(4,'菜单类型','MENU_TYPE',2,'1:目录,2:菜单,3:按钮','','2018-10-05 20:24:57','2019-11-06 20:08:46',1,1,1),(5,'搜索栏状态','SEARCH_STATUS',2,'1:正常,2:冻结','','2018-10-05 20:25:45','2019-02-26 00:34:34',1,1,1),(6,'日志类型','LOG_TYPE',2,'1:业务,2:登录,3:系统','','2018-10-05 20:28:47','2019-02-26 00:31:43',1,1,1),(7,'客户所在国家','CUST_LOCAL',2,'CN:中国,RU:俄罗斯','','2025-07-26 20:47:53','2025-07-26 20:47:53',1,1,1),(8,'客户类型','CUST_TYPE',2,'INI:个人,COM:公司','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(12,'合同状态','PUR_CON_STATUS',2,'INIT:初始化,FINISH:已完结,CANCLE:作废','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(13,'合同收款方','PUR_PAYEE_TYPE',2,'LOCAL:境内公司,OVERSEA:境外公司','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(14,'币种','CUR_TYPE',2,'RMB:人民币,RUB:卢布,USD:美元','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(15,'业务类型','BIZ_TYPE',2,'PUR:采购,TRANS:物流','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(16,'资金流向','FUND_FLOW',2,'MINUS:负相关,PLUS:正相关','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(17,'成本类型','COST_TYPE',2,'TAX_FREE:退税,PROFIT_BACK:返点,CASH_PAY:现金,CARGO_DAM:货损,COMPLAINT:投诉,CARGO_DELAY:延误','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(18,'运输状态','TRANS_STATUS',2,'INIT:初始化,ROADING:运输中,FINISH:已完结','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(19,'物流入账状态','TRANS_PAYEE_ACC_STATUS',2,'INIT:初始化,FINISH:已完结','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(20,'物流收款类型','TRANS_PAYEE_TYPE',2,'L_P:国内个人,L_C:国内公司,O_P:国外个人,O_C:国外公司','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(21,'货物描述','GOODS_DESC',2,'DATA_LINE:数据线,BRAKE_PADS:刹车片','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(22,'物流付款发生地','TRANS_LOC_TYPE',2,'LOCAL:境内公司,OVERSEA:境外公司','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(23,'物流付款类型','TRANS_PAYMENT_TYPE',2,'LOC_TRANS_FEES:国内运费,LOC_TRANS_STO:国内仓储,LOC_TRANS_PER:国内杂费,OVE_TRANS_FEES:国外运维,OVE_TRANS_CUST:国外清关费','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(24,'货运方式','TRANS_TYPE',2,'ROAD_EXP:陆运特快,ROAD_EXP_SUP:陆运超快,ROAD_EXP_ORI:陆运普快,RAIL_EXP:铁路,SEA_EXP:海运,AIR_TYPE:空运','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1),(25,'收款类型','PAYEE_TYPE',2,'L_P:国内个人,L_C:国内公司,O_P:国外个人,O_C:国外公司','','2025-07-26 20:49:18','2025-07-26 20:49:18',1,1,1);
/*!40000 ALTER TABLE `sys_dict` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_file`
--

DROP TABLE IF EXISTS `sys_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_file` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '文件名',
  `path` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '文件路径',
  `mime` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'MIME文件类型',
  `size` bigint DEFAULT NULL COMMENT '文件大小',
  `md5` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'MD5值',
  `sha1` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'SHA1值',
  `create_by` bigint DEFAULT NULL COMMENT '上传者',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKkkles8yp0a156p4247cc22ovn` (`create_by`) USING BTREE,
  CONSTRAINT `FKkkles8yp0a156p4247cc22ovn` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_file`
--

LOCK TABLES `sys_file` WRITE;
/*!40000 ALTER TABLE `sys_file` DISABLE KEYS */;
INSERT INTO `sys_file` VALUES (1,'8c714634095c4f9280b84c043e010ed1.jpg','/upload/picture/20220411/8c714634095c4f9280b84c043e010ed1.jpg','image/jpeg',39359,'1c593639501bd6a04057324e5b0c2040','42f2a52186e0fea60209621fb7bac934be2bfe96',28,'2022-04-11 10:24:56');
/*!40000 ALTER TABLE `sys_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '菜单名称',
  `pid` bigint DEFAULT NULL COMMENT '父级编号',
  `pids` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '所有父级编号',
  `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT 'URL地址',
  `perms` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '图标',
  `type` tinyint DEFAULT NULL COMMENT '类型（1:一级菜单,2:子级菜单,3:不是菜单）',
  `sort` int DEFAULT NULL COMMENT '排序',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建用户',
  `update_by` bigint DEFAULT NULL COMMENT '更新用户',
  `status` tinyint DEFAULT NULL COMMENT '状态（1:正常,2:冻结,3:删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKoxg2hi96yr9pf2m0stjomr3we` (`create_by`) USING BTREE,
  KEY `FKsiko0qcr8ddamvrxf1tk4opgc` (`update_by`) USING BTREE,
  CONSTRAINT `FKoxg2hi96yr9pf2m0stjomr3we` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKsiko0qcr8ddamvrxf1tk4opgc` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=315 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,'菜单管理',2,'[0],[2]','/system/menu/index','system:menu:index','',2,3,'','2018-09-29 00:02:10','2019-02-24 16:10:40',1,1,1),(2,'系统管理',0,'[0]','#','#','fa fa-cog',1,6,'','2018-09-29 00:05:50','2025-07-28 20:27:44',1,1,1),(3,'添加',1,'[0],[2],[1]','/system/menu/add','system:menu:add','',3,1,'','2018-09-29 00:06:57','2019-02-24 16:12:59',1,1,1),(4,'角色管理',2,'[0],[2]','/system/role/index','system:role:index','',2,2,'','2018-09-29 00:08:14','2019-02-24 16:10:34',1,1,1),(5,'添加',4,'[0],[2],[4]','/system/role/add','system:role:add','',3,1,'','2018-09-29 00:09:04','2019-02-24 16:12:04',1,1,1),(6,'主页',0,'[0]','/demo/t1/index2','index','layui-icon layui-icon-home',1,5,'','2018-09-29 00:09:56','2025-07-28 20:27:44',1,1,1),(7,'用户管理',2,'[0],[2]','/system/user/index','system:user:index','',2,1,'','2018-09-29 00:43:50','2019-04-05 17:43:25',1,2,1),(8,'编辑',1,'[0],[2],[1]','/system/menu/edit','system:menu:edit','',3,2,'','2018-09-29 00:57:33','2019-02-24 16:13:05',1,1,1),(9,'详细',1,'[0],[2],[1]','/system/menu/detail','system:menu:detail','',3,3,'','2018-09-29 01:03:00','2019-02-24 16:13:12',1,1,1),(10,'修改状态',1,'[0],[2],[1]','/system/menu/status','system:menu:status','',3,4,'','2018-09-29 01:03:43','2019-02-24 16:13:21',1,1,1),(11,'编辑',4,'[0],[2],[4]','/system/role/edit','system:role:edit','',3,2,'','2018-09-29 01:06:13','2019-02-24 16:12:10',1,1,1),(12,'授权',4,'[0],[2],[4]','/system/role/auth','system:role:auth','',3,3,'','2018-09-29 01:06:57','2019-02-24 16:12:17',1,1,1),(13,'详细',4,'[0],[2],[4]','/system/role/detail','system:role:detail','',3,4,'','2018-09-29 01:08:00','2019-02-24 16:12:24',1,1,1),(14,'修改状态',4,'[0],[2],[4]','/system/role/status','system:role:status','',3,5,'','2018-09-29 01:08:22','2019-02-24 16:12:43',1,1,1),(15,'编辑',7,'[0],[2],[7]','/system/user/edit','system:user:edit','',3,2,'','2018-09-29 21:17:17','2019-02-24 16:11:03',1,1,1),(16,'添加',7,'[0],[2],[7]','/system/user/add','system:user:add','',3,1,'','2018-09-29 21:17:58','2019-02-24 16:10:28',1,1,1),(17,'修改密码',7,'[0],[2],[7]','/system/user/pwd','system:user:pwd','',3,3,'','2018-09-29 21:19:40','2019-02-24 16:11:11',1,1,1),(18,'权限分配',7,'[0],[2],[7]','/system/user/role','system:user:role','',3,4,'','2018-09-29 21:20:35','2019-02-24 16:11:18',1,1,1),(19,'详细',7,'[0],[2],[7]','/system/user/detail','system:user:detail','',3,5,'','2018-09-29 21:21:00','2019-02-24 16:11:26',1,1,1),(20,'修改状态',7,'[0],[2],[7]','/system/user/status','system:user:status','',3,6,'','2018-09-29 21:21:18','2019-02-24 16:11:35',1,1,1),(21,'字典管理',2,'[0],[2]','/system/dict/index','system:dict:index','',2,5,'','2018-10-05 00:55:52','2019-02-24 16:14:24',1,1,1),(22,'字典添加',21,'[0],[2],[21]','/system/dict/add','system:dict:add','',3,1,'','2018-10-05 00:56:26','2019-02-24 16:14:10',1,1,1),(23,'字典编辑',21,'[0],[2],[21]','/system/dict/edit','system:dict:edit','',3,2,'','2018-10-05 00:57:08','2019-02-24 16:14:34',1,1,1),(24,'字典详细',21,'[0],[2],[21]','/system/dict/detail','system:dict:detail','',3,3,'','2018-10-05 00:57:42','2019-02-24 16:14:41',1,1,1),(25,'修改状态',21,'[0],[2],[21]','/system/dict/status','system:dict:status','',3,4,'','2018-10-05 00:58:27','2019-02-24 16:14:49',1,1,1),(26,'行为日志',2,'[0],[2]','/system/actionLog/index','system:actionLog:index','',2,6,'','2018-10-14 16:52:01','2019-02-27 21:34:15',1,1,1),(27,'日志详细',26,'[0],[2],[26]','/system/actionLog/detail','system:actionLog:detail','',3,1,'','2018-10-14 21:07:11','2019-02-27 21:34:15',1,1,1),(28,'日志删除',26,'[0],[2],[26]','/system/actionLog/delete','system:actionLog:delete','',3,2,'','2018-10-14 21:08:17','2019-02-27 21:34:15',1,1,1),(30,'开发中心',0,'[0]','#','#','fa fa-gavel',1,7,'','2018-10-19 16:38:23','2025-07-28 20:27:44',1,1,1),(31,'代码生成',30,'[0],[30]','/dev/code','#','',2,1,'','2018-10-19 16:39:04','2019-03-13 17:43:58',1,1,1),(125,'表单构建',30,'[0],[30]','/dev/build','#','',2,2,'','2018-11-25 16:12:23','2019-02-24 16:16:40',1,1,1),(136,'部门管理',2,'[0],[2]','/system/dept/index','system:dept:index','',2,4,'','2018-12-02 16:33:23','2019-02-24 16:10:50',1,1,1),(137,'添加',136,'[0],[2],[136]','/system/dept/add','system:dept:add','',3,1,'','2018-12-02 16:33:23','2019-02-24 16:13:34',1,1,1),(138,'编辑',136,'[0],[2],[136]','/system/dept/edit','system:dept:edit','',3,2,'','2018-12-02 16:33:23','2019-02-24 16:13:42',1,1,1),(139,'详细',136,'[0],[2],[136]','/system/dept/detail','system:dept:detail','',3,3,'','2018-12-02 16:33:23','2019-02-24 16:13:49',1,1,1),(140,'改变状态',136,'[0],[2],[136]','/system/dept/status','system:dept:status','',3,4,'','2018-12-02 16:33:23','2019-02-24 16:13:57',1,1,1),(146,'数据接口',30,'[0],[30]','/dev/swagger','#','',2,3,'','2018-12-09 21:11:11','2019-02-24 23:38:18',1,1,1),(147,'资金管理',0,'[0]','/demo/t1/index','demo:t1:index','layui-icon layui-icon-template-1',1,8,'','2021-10-25 15:01:29','2025-07-28 20:27:44',1,1,1),(148,'添加',147,'[0],[147]','/demo/t1/add','demo:t1:add',NULL,3,1,NULL,'2021-10-25 15:01:30','2021-10-25 15:01:30',1,1,1),(149,'编辑',147,'[0],[147]','/demo/t1/edit','demo:t1:edit',NULL,3,1,NULL,'2021-10-25 15:01:30','2021-10-25 15:01:30',1,1,1),(150,'详细',147,'[0],[147]','/demo/t1/detail','demo:t1:detail',NULL,3,1,NULL,'2021-10-25 15:01:30','2021-10-25 15:01:30',1,1,1),(151,'修改状态',147,'[0],[147]','/demo/t1/status','demo:t1:status',NULL,3,1,NULL,'2021-10-25 15:01:30','2021-10-25 15:01:30',1,1,1),(157,'客户资金管理',0,'[0]','/demo/t1/index2','demo:t1:index2','layui-icon layui-icon-user',1,9,'','2021-10-25 16:57:08','2025-07-28 20:27:44',1,1,1),(158,'详情',157,'[0],[157]','/demo/t1/detail','demo:t1:detail','',3,1,'','2021-10-25 16:58:37','2021-10-25 16:58:37',1,1,1),(159,'余额调整',0,'[0]','/acct/info/index','acct:info:index','fa fa-cog',1,10,'','2021-10-25 21:06:18','2025-07-28 20:27:44',1,1,1),(160,'添加',159,'[0],[159]','/acct/info/add','acct:info:add',NULL,3,1,NULL,'2021-10-25 21:06:18','2021-10-25 21:06:18',1,1,1),(161,'编辑',159,'[0],[159]','/acct/info/edit','acct:info:edit',NULL,3,1,NULL,'2021-10-25 21:06:18','2021-10-25 21:06:18',1,1,1),(162,'详细',159,'[0],[159]','/acct/info/detail','acct:info:detail',NULL,3,1,NULL,'2021-10-25 21:06:18','2021-10-25 21:06:18',1,1,1),(163,'修改状态',159,'[0],[159]','/acct/info/status','acct:info:status',NULL,3,1,NULL,'2021-10-25 21:06:18','2021-10-25 21:06:18',1,1,1),(164,'物流管理',0,'[0]','/express/exinfo/index','express:exinfo:index','layui-icon layui-icon-note',1,11,'','2022-04-01 17:39:29','2025-07-28 20:27:44',1,1,1),(165,'添加',164,'[0],[164]','/express/exinfo/add','express:exinfo:add','',3,1,'','2022-04-01 17:40:19','2022-04-01 17:40:19',1,1,1),(166,'编辑',164,'[0],[164]','/express/exinfo/edit','express:exinfo:edit','',3,2,'','2022-04-01 17:40:41','2022-04-01 17:40:41',1,1,1),(167,'详情',164,'[0],[164]','/express/exinfo/detail','express:exinfo:detail','',3,3,'','2022-04-01 17:41:03','2022-04-01 17:41:03',1,1,1),(168,'修改状态',164,'[0],[164]','/express/exinfo/status','express:exinfo:status','',3,4,'','2022-04-01 17:41:24','2022-04-01 17:41:24',1,1,1),(169,'订单管理',0,'[0]','/order/orderLog/index','order:orderLog:index','layui-icon layui-icon-note',1,12,'','2023-04-11 10:49:30','2025-07-28 20:27:44',1,1,2),(170,'添加',169,'[0],[169]','/order/orderLog/add','order:orderLog:add',NULL,3,1,NULL,'2023-04-11 10:49:32','2025-07-08 17:15:57',1,1,2),(171,'编辑',169,'[0],[169]','/order/orderLog/edit','order:orderLog:edit',NULL,3,1,NULL,'2023-04-11 10:49:32','2025-07-08 17:15:57',1,1,2),(172,'详细',169,'[0],[169]','/order/orderLog/detail','order:orderLog:detail',NULL,3,1,NULL,'2023-04-11 10:49:32','2025-07-08 17:15:57',1,1,2),(173,'修改状态',169,'[0],[169]','/order/orderLog/status','order:orderLog:status',NULL,3,1,NULL,'2023-04-11 10:49:32','2025-07-08 17:15:57',1,1,2),(174,'采购管理',0,'[0]','/foreignOrder/foreignOrder/index','foreignOrder:foreignOrder:index','fa fa-shopping-cart',1,13,'','2025-06-26 17:07:59','2025-07-28 20:27:44',1,1,1),(175,'添加',174,'[0],[174]','/foreignOrder/foreignOrder/add','foreignOrder:foreignOrder:add',NULL,3,1,NULL,'2025-06-26 17:07:59','2025-06-26 17:07:59',1,1,1),(176,'编辑',174,'[0],[174]','/foreignOrder/foreignOrder/edit','foreignOrder:foreignOrder:edit',NULL,3,1,NULL,'2025-06-26 17:07:59','2025-06-26 17:07:59',1,1,1),(177,'详细',174,'[0],[174]','/foreignOrder/foreignOrder/detail','foreignOrder:foreignOrder:detail',NULL,3,1,NULL,'2025-06-26 17:07:59','2025-06-26 17:07:59',1,1,1),(178,'修改状态',174,'[0],[174]','/foreignOrder/foreignOrder/status','foreignOrder:foreignOrder:status',NULL,3,1,NULL,'2025-06-26 17:07:59','2025-06-26 17:07:59',1,1,1),(254,'基本信息',0,'[0]','#','#','fa fa-sliders',1,1,'','2025-07-28 20:06:56','2025-07-28 20:06:56',1,1,1),(255,'采购信息',0,'[0]','#','#','fa fa-shopping-cart',1,2,'','2025-07-28 20:13:00','2025-07-28 20:13:00',1,1,1),(256,'物流管理',0,'[0]','#','#','fa fa-truck',1,3,'','2025-07-28 20:25:36','2025-07-28 20:25:36',1,1,1),(257,'资金管理',0,'[0]','#','#','fa fa-money',1,4,'','2025-07-28 20:27:44','2025-07-28 20:27:44',1,1,1),(269,'客户管理',254,'[0],[254]','/psm/customer/index','psm:customer:index','',2,1,'','2025-07-25 18:31:24','2025-07-26 17:51:56',1,1,1),(270,'添加',269,'[0],[254],[269]','/psm/customer/add','psm:customer:add',NULL,3,1,NULL,'2025-07-25 18:31:24','2025-07-25 18:39:03',1,1,1),(271,'编辑',269,'[0],[254],[269]','/psm/customer/edit','psm:customer:edit',NULL,3,1,NULL,'2025-07-25 18:31:24','2025-07-25 18:39:03',1,1,1),(272,'详细',269,'[0],[254],[269]','/psm/customer/detail','psm:customer:detail',NULL,3,1,NULL,'2025-07-25 18:31:24','2025-07-25 18:39:03',1,1,1),(273,'修改状态',269,'[0],[254],[269]','/psm/customer/status','psm:customer:status',NULL,3,1,NULL,'2025-07-25 18:31:24','2025-07-25 18:39:03',1,1,1),(274,'汇率管理',254,'[0],[254]','/psm/exchangeRate/index','psm:exchangeRate:index','',2,3,'','2025-07-25 20:41:51','2025-07-26 17:52:09',1,1,1),(275,'添加',274,'[0],[254],[274]','/psm/exchangeRate/add','psm:exchangeRate:add',NULL,3,1,NULL,'2025-07-25 20:41:51','2025-07-25 20:41:51',1,1,1),(276,'编辑',274,'[0],[254],[274]','/psm/exchangeRate/edit','psm:exchangeRate:edit',NULL,3,1,NULL,'2025-07-25 20:41:51','2025-07-25 20:41:51',1,1,1),(277,'详细',274,'[0],[254],[274]','/psm/exchangeRate/detail','psm:exchangeRate:detail',NULL,3,1,NULL,'2025-07-25 20:41:51','2025-07-25 20:41:51',1,1,1),(278,'修改状态',274,'[0],[254],[274]','/psm/exchangeRate/status','psm:exchangeRate:status',NULL,3,1,NULL,'2025-07-25 20:41:51','2025-07-25 20:41:51',1,1,1),(280,'采购管理',255,'[0],[255]','/psm/purchaseMain/index','psm:purchaseMain:index','',2,1,'','2025-07-25 19:58:16','2025-07-30 13:58:08',1,1,1),(281,'添加',280,'[0],[255],[280]','/psm/purchaseMain/add','psm:purchaseMain:add','',3,1,'','2025-07-25 19:58:16','2025-07-30 13:58:39',1,1,1),(282,'编辑',280,'[0],[255],[280]','/psm/purchaseMain/edit','psm:purchaseMain:edit','',3,2,'','2025-07-25 19:58:16','2025-07-30 13:58:49',1,1,1),(283,'详细',280,'[0],[255],[280]','/psm/purchaseMain/detail','psm:purchaseMain:detail','',3,3,'','2025-07-25 19:58:16','2025-07-30 13:59:02',1,1,1),(284,'修改状态',280,'[0],[255],[280]','/psm/purchaseMain/status','psm:purchaseMain:status','',3,4,'','2025-07-25 19:58:16','2025-07-30 13:59:13',1,1,1),(285,'出库管理',255,'[0],[255]','/psm/outboundOrder/index','psm:outboundOrder:index',NULL,2,2,NULL,'2025-07-25 20:20:28','2025-07-30 13:58:08',1,1,1),(286,'添加',285,'[0],[255],[285]','/psm/outboundOrder/add','psm:outboundOrder:add',NULL,3,1,NULL,'2025-07-25 20:20:28','2025-07-25 20:20:28',1,1,1),(287,'编辑',285,'[0],[255],[285]','/psm/outboundOrder/edit','psm:outboundOrder:edit',NULL,3,1,NULL,'2025-07-25 20:20:28','2025-07-25 20:20:28',1,1,1),(288,'详细',285,'[0],[255],[285]','/psm/outboundOrder/detail','psm:outboundOrder:detail',NULL,3,1,NULL,'2025-07-25 20:20:28','2025-07-25 20:20:28',1,1,1),(289,'修改状态',285,'[0],[255],[285]','/psm/outboundOrder/status','psm:outboundOrder:status',NULL,3,1,NULL,'2025-07-25 20:20:28','2025-07-25 20:20:28',1,1,1),(290,'物流付款管理',256,'[0],[256]','/psm/logisticsPayment/index','psm:logisticsPayment:index','',2,1,'','2025-07-25 21:24:07','2025-07-26 17:52:27',1,1,1),(291,'添加',290,'[0],[256],[290]','/psm/logisticsPayment/add','psm:logisticsPayment:add',NULL,3,1,NULL,'2025-07-25 21:24:07','2025-07-25 21:24:07',1,1,1),(292,'编辑',290,'[0],[256],[290]','/psm/logisticsPayment/edit','psm:logisticsPayment:edit',NULL,3,1,NULL,'2025-07-25 21:24:07','2025-07-25 21:24:07',1,1,1),(293,'详细',290,'[0],[256],[290]','/psm/logisticsPayment/detail','psm:logisticsPayment:detail',NULL,3,1,NULL,'2025-07-25 21:24:07','2025-07-25 21:24:07',1,1,1),(294,'修改状态',290,'[0],[256],[290]','/psm/logisticsPayment/status','psm:logisticsPayment:status',NULL,3,1,NULL,'2025-07-25 21:24:07','2025-07-25 21:24:07',1,1,1),(295,'物流信息管理',256,'[0],[256]','/psm/logisticsInfo/index','psm:logisticsInfo:index','',2,2,'','2025-07-25 21:40:02','2025-07-26 17:51:14',1,1,1),(296,'添加',295,'[0],[256],[295]','/psm/logisticsInfo/add','psm:logisticsInfo:add',NULL,3,1,NULL,'2025-07-25 21:40:02','2025-07-25 21:40:02',1,1,1),(297,'编辑',295,'[0],[256],[295]','/psm/logisticsInfo/edit','psm:logisticsInfo:edit',NULL,3,1,NULL,'2025-07-25 21:40:02','2025-07-25 21:40:02',1,1,1),(298,'详细',295,'[0],[256],[295]','/psm/logisticsInfo/detail','psm:logisticsInfo:detail',NULL,3,1,NULL,'2025-07-25 21:40:02','2025-07-25 21:40:02',1,1,1),(299,'修改状态',295,'[0],[256],[295]','/psm/logisticsInfo/status','psm:logisticsInfo:status',NULL,3,1,NULL,'2025-07-25 21:40:02','2025-07-25 21:40:02',1,1,1),(300,'货物信息管理',256,'[0],[256]','/psm/goodsInfo/index','psm:goodsInfo:index','',2,3,'','2025-07-25 21:52:19','2025-07-31 18:47:18',1,1,1),(301,'添加',300,'[0],[300]','/psm/goodsInfo/add','psm:goodsInfo:add',NULL,3,1,NULL,'2025-07-25 21:52:19','2025-07-25 21:52:19',1,1,1),(302,'编辑',300,'[0],[300]','/psm/goodsInfo/edit','psm:goodsInfo:edit',NULL,3,1,NULL,'2025-07-25 21:52:19','2025-07-25 21:52:19',1,1,1),(303,'详细',300,'[0],[300]','/psm/goodsInfo/detail','psm:goodsInfo:detail',NULL,3,1,NULL,'2025-07-25 21:52:19','2025-07-25 21:52:19',1,1,1),(304,'修改状态',300,'[0],[300]','/psm/goodsInfo/status','psm:goodsInfo:status',NULL,3,1,NULL,'2025-07-25 21:52:19','2025-07-25 21:52:19',1,1,1),(305,'成本列表',257,'[0],[257]','/psm/costRecord/index','psm:costRecord:index','',2,1,'','2025-07-25 20:54:12','2025-07-26 17:51:42',1,1,1),(306,'添加',305,'[0],[257],[305]','/psm/costRecord/add','psm:costRecord:add',NULL,3,1,NULL,'2025-07-25 20:54:12','2025-07-25 20:54:12',1,1,1),(307,'编辑',305,'[0],[257],[305]','/psm/costRecord/edit','psm:costRecord:edit',NULL,3,1,NULL,'2025-07-25 20:54:12','2025-07-25 20:54:12',1,1,1),(308,'详细',305,'[0],[257],[305]','/psm/costRecord/detail','psm:costRecord:detail',NULL,3,1,NULL,'2025-07-25 20:54:12','2025-07-25 20:54:12',1,1,1),(309,'修改状态',305,'[0],[257],[305]','/psm/costRecord/status','psm:costRecord:status',NULL,3,1,NULL,'2025-07-25 20:54:12','2025-07-25 20:54:12',1,1,1),(310,'资金列表',257,'[0],[257]','/psm/amountDetail/index','psm:amountDetail:index','',2,2,'','2025-07-31 18:23:44','2025-07-31 18:47:52',1,1,1),(311,'添加',310,'[0],[310]','/psm/amountDetail/add','psm:amountDetail:add',NULL,3,1,NULL,'2025-07-31 18:23:44','2025-07-31 18:23:44',1,1,1),(312,'编辑',310,'[0],[310]','/psm/amountDetail/edit','psm:amountDetail:edit',NULL,3,1,NULL,'2025-07-31 18:23:44','2025-07-31 18:23:44',1,1,1),(313,'详细',310,'[0],[310]','/psm/amountDetail/detail','psm:amountDetail:detail',NULL,3,1,NULL,'2025-07-31 18:23:44','2025-07-31 18:23:44',1,1,1),(314,'修改状态',310,'[0],[310]','/psm/amountDetail/status','psm:amountDetail:status',NULL,3,1,NULL,'2025-07-31 18:23:44','2025-07-31 18:23:44',1,1,1);
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '角色名称（中文名）',
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '标识名称',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建用户',
  `update_by` bigint DEFAULT NULL COMMENT '更新用户',
  `status` tinyint DEFAULT NULL COMMENT '状态（1:正常,2:冻结,3:删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKdkwvv0rm6j3d5l6hwsy2dplol` (`create_by`) USING BTREE,
  KEY `FKrouqqi3f2bgc5o83wdstlh6q4` (`update_by`) USING BTREE,
  CONSTRAINT `FKdkwvv0rm6j3d5l6hwsy2dplol` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKrouqqi3f2bgc5o83wdstlh6q4` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'管理员','admin','','2018-09-29 00:12:40','2025-06-26 17:46:51',1,1,1),(2,'开发组','group','','2018-09-30 16:04:32','2019-04-28 00:10:00',1,1,3),(3,'用户组','group1','','2018-09-30 16:24:20','2019-04-28 00:11:09',1,1,3),(4,'平台管理员','platform','','2021-10-25 17:02:22','2024-10-29 16:11:20',1,1,1),(5,'客户','customer','','2021-10-25 17:04:22','2022-04-01 17:28:43',1,1,1),(6,'物流管理员','expressadmin','','2022-03-28 15:53:24','2022-04-01 17:42:15',1,1,1),(7,'物流业务员','expressworker','','2022-03-28 15:53:38','2022-04-01 17:42:23',1,1,1),(8,'boss','boss','boss','2023-11-03 11:20:30','2023-11-03 11:38:33',1,1,1),(9,'internal','internal','internal','2023-11-03 11:20:40','2025-06-27 10:30:16',1,1,1),(10,'external','external','external','2023-11-03 11:20:49','2023-11-03 11:38:45',1,1,1),(11,'李善振专属角色','lsz','boss','2023-11-03 11:27:22','2023-11-03 11:29:40',1,1,1),(12,'xubo_insert','xubo','临时录入数据','2024-10-15 15:46:25','2024-10-15 15:46:25',1,1,3),(13,'财务部','finance','','2025-01-24 12:06:50','2025-01-27 13:03:23',1,1,1),(14,'Chain','Chain','中国业务员专属角色','2025-06-26 17:42:42','2025-06-26 17:45:20',1,1,1),(15,'test','test','','2025-06-27 10:06:13','2025-06-27 10:07:26',1,1,3),(16,'采购管理员','purchasingManager','','2025-07-09 11:30:34','2025-07-09 11:31:38',1,1,1),(17,'采购员','purchasing','','2025-07-09 11:30:51','2025-07-09 11:31:31',1,1,1);
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL,
  `menu_id` bigint NOT NULL,
  PRIMARY KEY (`role_id`,`menu_id`) USING BTREE,
  KEY `FKf3mud4qoc7ayew8nml4plkevo` (`menu_id`) USING BTREE,
  CONSTRAINT `FKf3mud4qoc7ayew8nml4plkevo` FOREIGN KEY (`menu_id`) REFERENCES `sys_menu` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKkeitxsgxwayackgqllio4ohn5` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (1,1),(1,2),(4,2),(11,2),(1,3),(1,4),(1,5),(1,6),(4,6),(5,6),(6,6),(7,6),(8,6),(9,6),(10,6),(11,6),(13,6),(16,6),(17,6),(1,7),(4,7),(11,7),(1,8),(1,9),(1,10),(1,11),(1,12),(1,13),(1,14),(1,15),(4,15),(11,15),(1,16),(4,16),(11,16),(1,17),(4,17),(11,17),(1,18),(4,18),(11,18),(1,19),(4,19),(11,19),(1,20),(4,20),(11,20),(1,21),(1,22),(1,23),(1,24),(1,25),(1,26),(1,27),(1,28),(1,30),(1,31),(1,125),(1,136),(1,137),(1,138),(1,139),(1,140),(1,146),(1,147),(4,147),(11,147),(13,147),(1,148),(4,148),(11,148),(13,148),(1,149),(4,149),(11,149),(13,149),(1,150),(4,150),(11,150),(13,150),(1,151),(4,151),(11,151),(13,151),(4,157),(5,157),(11,157),(13,157),(4,158),(5,158),(11,158),(13,158),(1,159),(4,159),(11,159),(13,159),(1,160),(4,160),(11,160),(13,160),(1,161),(4,161),(11,161),(13,161),(1,162),(4,162),(11,162),(13,162),(1,163),(4,163),(11,163),(13,163),(4,164),(6,164),(7,164),(11,164),(4,165),(6,165),(7,165),(11,165),(4,166),(6,166),(7,166),(11,166),(4,167),(6,167),(7,167),(11,167),(4,168),(6,168),(7,168),(11,168),(8,169),(9,169),(10,169),(11,169),(8,170),(9,170),(10,170),(11,170),(8,171),(9,171),(10,171),(11,171),(8,172),(9,172),(10,172),(11,172),(8,173),(9,173),(10,173),(11,173),(1,174),(9,174),(14,174),(16,174),(17,174),(1,175),(9,175),(14,175),(16,175),(17,175),(1,176),(9,176),(14,176),(16,176),(17,176),(1,177),(9,177),(14,177),(16,177),(17,177),(1,178),(9,178),(14,178),(16,178),(17,178),(1,310),(1,311),(1,312),(1,313),(1,314);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户名',
  `nickname` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '用户昵称',
  `password` char(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '密码',
  `salt` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '密码盐',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `picture` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '头像',
  `sex` tinyint DEFAULT NULL COMMENT '性别（1:男,2:女）',
  `email` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '电话号码',
  `remark` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint DEFAULT NULL COMMENT '状态（1:正常,2:冻结,3:删除）',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKb3pkx0wbo6o8i8lj0gxr37v1n` (`dept_id`) USING BTREE,
  CONSTRAINT `FKb3pkx0wbo6o8i8lj0gxr37v1n` FOREIGN KEY (`dept_id`) REFERENCES `sys_dept` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,'admin','超级管理员','5fa04c0758ae596e2a93cd2802640693a5b08bdfacd4307abce323c85e481154','7rl2t9',1,NULL,1,'<EMAIL>','10086','','2018-08-09 23:00:03','2025-06-26 17:46:34',1),(2,'linln','小懒虫','28bfc4f19b0d4b8a40018faf9aec4ad9db5491082dda439040b1c35ff8c533a7','gzNkXt',2,NULL,2,'<EMAIL>','1008612','','2018-09-30 16:25:22','2019-11-06 20:09:17',3),(3,'platformlsz','李善振','e33d13c1dd75e9deb425d506facbaa3e147cef44222ee7f5db467c81de5170c9','TpTXGY',1,NULL,1,'','','','2021-10-25 17:14:57','2024-10-29 16:15:14',1),(4,'customer001','客户A','0eb095215577e34cb3020f169113d0489685400c217c199f8a4f8d4904d5c712','JZlcfl',1,NULL,1,'','','','2021-10-25 17:39:59','2021-10-25 17:47:48',3),(5,'ct002','客户002','774242f967a813482f8a8355fcf0c721f058719056006c1f0b4f1b3d9b514b9e','2VqleZ',1,NULL,1,'','','','2021-10-25 21:24:02','2021-10-25 21:31:50',3),(6,'111','aaa','8575c00e03526a3f18a2b39863de41fb02ca7b54e8438c6f103a6484c74714ba','jgJE7T',5,NULL,1,'','','','2021-10-26 10:06:50','2021-10-26 10:06:50',3),(7,'1212121','11111','9594527742f2965d17fbcb2bdd23f0f2bec271fe4263229491b9b68b705b624a','Dmf3E7',3,NULL,1,'','18866992698','','2021-10-26 10:16:34','2021-10-26 10:21:34',3),(8,'lilili','ssss','4f2ba8673895075f8ddf477c8bcac71142092243acedab5faab5c9d319a84e04','NiO2nX',1,NULL,1,'','18866992698','','2021-10-26 10:19:55','2021-10-26 10:21:09',3),(9,'aaaaaa','aaabbb','52da36d704093b51b2625badd41859b5c7afa57d7437144a04dda205dc800852','wXLYXx',3,NULL,1,'','18866992698','','2021-10-26 10:30:45','2021-10-26 10:39:57',3),(10,'121121','100000','7a9c37d88b70f4848769ddac9e298b7df605b20129c6da445f07ad6cdc046c2a','CYcW6M',3,NULL,1,'','','','2021-10-26 10:43:04','2021-10-26 10:43:19',3),(11,'1b','1B','b5899534dce82ec1993359594eaa90cdab1efe0dd8452a8b6083e2bcc8c1e0cd','b80qbv',3,NULL,1,'','','','2021-10-26 10:45:44','2021-10-28 09:40:55',3),(12,'c003','c003','9980e717296bae245687dbc5eb22f9aa7415f5642bc537b0660b4440ca0583d9','WM8gTD',1,NULL,1,'','','','2021-10-26 16:58:08','2021-10-26 16:58:16',3),(13,'XZC2010','旋转锉','34002cb7c144120ee24ca2d42fffcc843c25459d10ed7f55863f1d1925956ed0','L3nfhc',3,NULL,1,'','','','2021-10-28 09:25:26','2021-10-28 09:43:46',3),(14,'c004','c004','094de095e061baee7a747b6699fb907ade3a5efa474e2deca75e0be6b9c75373','PYQ3bm',1,NULL,1,'','','','2021-10-28 11:40:19','2021-10-28 11:40:19',3),(15,'c005','c005','f6599f4907d6a551bef9b1481aa52492ce34b3773668f67b2baa711d1b297a12','i7uTM6',1,NULL,2,'','','','2021-10-28 19:52:11','2021-10-28 22:35:11',3),(16,'XRG001','谢尔盖','94dddfc6ce40c3c9efbe6d1fe7273960ebd6cc77d7fd5a34a03360004d82792b','u2xa6A',3,NULL,1,'','','','2021-11-11 17:48:07','2021-11-11 18:06:15',3),(17,'A001','sergey','8606aaa9438825f7d61146d8695fd09b03dbeda39de596a907423e92b5c2384c','4TJs2A',3,NULL,1,'','','','2021-11-11 20:59:44','2021-11-11 20:59:52',3),(18,'1bru003','1B.ru','b1b49162f8beba55dc8c988c783727a5b1ec24ab3e0bede2216de51e2b115808','vQ4awQ',3,NULL,1,'','','','2021-11-13 16:49:01','2021-11-13 16:52:57',1),(19,'serg001','sergey','7d12aac3c56e7b8f743e4b6bf3483e3fc846f680c23a1e8ce613ed9020165106','5nT55k',3,NULL,1,'','','','2021-11-13 17:32:07','2022-04-01 17:32:19',1),(20,'ls001','lesha','f2e7e5ca3b4981b6886faa08e52454b17e816be3d9406623c996a4d7f061b792','lCxgPI',3,NULL,1,'','','','2021-11-30 14:17:13','2021-11-30 14:20:33',1),(21,'artem','artem-M','36e4bbc42c8272ae0642412318e876e4972693ca1ec1429ee382a8cede04d3af','G2a4du',3,NULL,1,'','','','2022-01-10 16:00:13','2022-01-10 16:00:34',3),(22,'gjys001','国际运输','fd23085f275ee2953f22ec752fea05d75b75a79ff280dad26754955f272acb16','oDWxfo',3,NULL,1,'','','','2022-03-21 23:31:35','2022-03-21 23:31:45',3),(23,'expressmangerA','物流管理','7e5eb4239a6c2962b5a0181afd6fb6e48644093f7fb923fa229cdcd5bb18fe56','8vJsYJ',6,NULL,1,'','','','2022-03-28 15:50:57','2022-03-28 15:50:57',3),(24,'expressmangerB','物流管理','2cb70a65e65581e846a2aebfcd4bf5d0ccd6da4d905be7c53c4f7e26a961ba50','CCBVhu',6,NULL,1,'','','','2022-03-28 15:51:26','2022-03-28 15:51:26',3),(25,'zhf001','zhf001','ff5a781843f8616480b983fde628aa9109569849b504b8058564dae8dee5c23a','FukDvw',1,NULL,1,'','','老板账号','2022-04-01 17:43:06','2022-04-01 17:45:31',1),(26,'zhj001','zhj001','452ed9ff89b90e9eac0fe5d609100908c728e128fa3d8da480ecc217e9b1faad','6zR5Xk',1,NULL,1,'','','老板账号','2022-04-01 17:43:36','2022-04-01 17:45:22',1),(27,'lsz001','lsz001','426c31b5171abc4a0b4fb8b9f3fbec6250f5fcc275fe9148da16ee0d4c571704','LS94O3',6,NULL,1,'','','','2022-04-01 17:44:05','2025-07-14 15:05:03',1),(28,'zzx001','张子轩','eefc10ad436afd15bc302d42120fe7d68640ea3a98e9a48a59ad3c45c73f2155','6uttlT',6,'/upload/picture/20220411/8c714634095c4f9280b84c043e010ed1.jpg',1,'<EMAIL>','15002605996','','2022-04-01 17:44:30','2025-07-14 10:22:06',1),(29,'SIR-01','sir-01','3b7fb14c9144f85be40a6702dfde017c0711f826c424b58315c2d0cc2ebab3b1','EUiU2T',3,NULL,1,'','','','2022-09-16 15:59:09','2022-09-16 16:00:58',1),(30,'rad-01','rad-01','39b579839026ef19f2de5f19a37647fa679f48045edc07917871014c60f8a4f3','R772TA',3,NULL,1,'','','','2022-09-16 16:14:23','2022-09-16 16:21:28',3),(31,'ntn-01','ntn-01','18edeec4fbd4f424b7f823fc534f2f5f598e089f4c7cd20334e65d3c71531b91','NFE04o',3,NULL,1,'','','','2022-09-16 16:17:12','2022-09-16 16:21:40',3),(32,'rad-01','rad-01','d139cff12cfebffecc3e664ccafccfac1f356d1666abfbee0f6c8742c0222ac4','4b4Tez',3,NULL,1,'','','','2022-09-16 16:31:18','2022-09-16 16:37:21',3),(33,'ntn-01','ntn-01','3203885401afd918b49f565578eb3c0bec2ebe45d82016e90f5d3968543c3214','0Lc1sk',3,NULL,1,'','','','2022-09-16 16:35:38','2022-09-16 16:37:27',3),(34,'rad-01','rad-01','93581304cf6c4459b249177b860ccf84d1264b1f54d7029225143f5d9d17fbd5','qCpjCI',3,NULL,1,'','','','2022-09-16 16:39:22','2022-09-16 16:49:00',1),(35,'123','123','ddd4385716cf6c86b8681908dd6b1c443470ce62ce8281200f54ad720f9a5491','f2YP4S',3,NULL,1,'','','','2022-09-19 09:18:54','2022-09-19 09:19:17',3),(36,'ntn-01','ntn-01','af5c0663c58827f5439afdc47d976888257b2968dc385e21aae7306e12db2fe3','7WgVcs',3,NULL,1,'','','','2022-09-22 11:38:32','2022-09-22 11:38:32',1),(37,'rad-*01','rad-01','385cf8e5f0a4c5770e3207f4392c506a30fa5927feccb464564887691d22aa82','o3fceV',3,NULL,1,'','','','2022-09-22 11:39:25','2022-09-22 11:39:25',1),(38,'Trade01','Trade01','97513510997a0a8c4100bba84354bfe4618d170e91bf866544777305802c3df1','QH5Q8y',3,NULL,1,'','','','2023-04-27 17:07:44','2023-04-27 17:09:05',3),(39,'TRADE01','TRADE01','ba557c30f0f8fd56efb24ed118a4c281aa60de69bd4449d4a064f31b1fb11b44','77uM4R',3,NULL,1,'','','','2023-04-27 17:12:17','2023-04-27 17:12:28',1),(40,'RADI','RADI','6d4f219a4397ce089a25c8441247d3f8700cc4ad7f09e8c3698dee4d09fb77ca','4n90NF',3,NULL,1,'','','','2023-06-27 01:03:08','2023-06-27 01:03:23',1),(41,'RADI-3','RADI-3','e944ba9995379403a4df808efc5230dedf9a783c7c7c0619a0f48ca83a3e2556','2tpR2i',3,NULL,1,'','','','2023-08-16 15:15:32','2023-08-16 15:15:32',1),(42,'Svyat','Svyat','fa467cb84fd58d553a2006c5db797ed3d138207a6171a9d06c76c7611091dd7c','kU6Vxu',3,NULL,1,'','','','2023-09-07 14:01:19','2023-11-05 18:02:02',1),(43,'NTNK','NTNK','139210cb71e5ebe0f1eb792b904bb7a79f7251541583ab7dee5c402404f100fc','Wsx31f',3,NULL,1,'','','','2023-10-20 10:28:41','2023-10-20 10:29:04',1),(44,'SIRIN','SIRIN','dcf8786afaed9deac6685e382e1e117ef4f4b3dc62af7c20031cedabb9c3e33b','4PmJWe',3,NULL,1,'','','','2023-10-20 10:48:32','2023-10-20 10:48:32',1),(45,'KARIMOV','KARIMOV','d5f2265fa59aa9ff15694fb6b94f404c14a1f40f564247ed3547c4eccab11654','Doj6xe',3,NULL,1,'','','','2023-10-21 08:14:41','2023-10-21 08:14:59',1),(46,'znz001','znz001','77b41900453cba1c929f977c6f3712cbd7b538b242bff49871957c687711e151','B0095p',1,NULL,1,'','','','2023-11-03 09:45:10','2023-11-03 09:45:10',1),(47,'aili','aili','f5e4b01067fcc5b5d49a332dc07227859666c9c8315f05dc3d81237259810611','rBEkyl',6,NULL,1,'','','','2023-11-03 11:37:50','2023-11-08 15:03:35',3),(48,'徐搏','xubo','8dc7091ce4d9f78242d7d8945578b9d17b15b14220e11490a8fff26002a55edb','S6iNlT',6,NULL,1,'','','','2023-11-03 12:11:24','2023-11-08 14:48:09',3),(49,'xubo','徐搏','e34fd5f38d204fb9268474acb9e9c51038818c032210e4a6ad99b82b9bbf6677','h44u10',6,NULL,1,'','','','2023-11-08 14:49:08','2025-05-13 09:54:48',1),(50,'aili','aili','e0d867c539fa118e0f696895a61185d930a72741efd211889ec2bebb0562f541','f647Vp',6,NULL,1,'','','','2023-11-08 15:04:15','2023-11-08 15:04:34',1),(51,'zhanghaifeng','zhanghaifeng','44bd3656ec7859d1f4bf37038b4795e55f57bf4f57bf03f85e28f930090e4bc9','QIs2Va',6,NULL,1,'','','','2023-11-08 15:07:12','2023-11-08 15:08:06',1),(52,'zhangzixuan','zhangzixuan','975a89d28365351dfeb62fb1b7ff4be8ff781c15cf084fad39140bdb2e07d16a','sLCT6K',6,NULL,1,'','','','2023-11-10 10:14:27','2023-11-10 10:14:42',1),(53,'JON11','JON11','7205701b5b8168d8cf1b7864a831a081babbf78665c00f76fb81373c7476718e','z4ocKs',6,NULL,1,'','','','2023-11-15 19:21:33','2023-11-15 19:21:48',1),(54,'Pavel','Pavel','7855b2b36a214964ce83002684d4a6514a1e706d4eedf010562bf99b3852e815','8K7Yc3',3,NULL,1,'','','','2023-12-21 09:51:18','2023-12-21 09:51:18',1),(55,'Pantes','Pantes','035331a1d073239ad5b332dac8bec8b8fb854cfc2b3372e47975aa7851f075e5','Tj9YMZ',3,NULL,1,'','','','2023-12-21 09:53:44','2023-12-21 09:53:44',1),(56,'zyx','郑永祥','9384d131e642be53e582c33a878165b774e7f006684de18f0439f3b941f5b924','dYsS0A',6,NULL,1,'<EMAIL>','15663323910','义乌分公司','2024-10-15 16:29:14','2025-07-08 18:04:28',1),(57,'hzy','何志洋','a6a8ad84d978f6c733547eb9d6a0a2a8f5cba5c6f5293d84d579fb742b9529cf','c5Vg3Y',6,NULL,1,'','','','2024-10-15 16:29:53','2025-04-30 10:35:43',1),(58,'hzy001','hzy','7207b910ef342de52b1109421f1732c8a35ff02d78b4b75aafe2f5d32dba66e8','NHl0Er',6,NULL,1,'','','','2025-01-23 19:41:14','2025-01-23 19:46:18',3),(59,'hzy008','何志洋','029cf5a9d2378773691c2b2d9874066be3e955b5d3c71c16fec374b451a709bf','M3vwDA',6,NULL,1,'','','何志洋','2025-01-23 19:50:03','2025-01-23 19:51:32',3),(60,'hzy001','hzy001','ec684f313818b9a527032b131a7855ef65a4b58911bf193673d9149bcc970f5e','AJGYTF',6,NULL,1,'','','','2025-01-23 19:53:49','2025-01-23 20:05:20',3),(61,'hzy001','hzy001','02c53159e75b9d4554114e32cd8e85f90c116306e0052f38db8a2b72aeba2d97','Yq6TQl',6,NULL,1,'','','','2025-01-23 20:12:39','2025-05-28 17:56:08',1),(62,'sf001','sf001','b7333f888e576121d0a31054e7c0bf8a46cefe28440d60ba08206cb5ed8b7dc7','A5jy6P',3,NULL,2,'','','','2025-01-23 20:15:22','2025-01-23 20:15:37',3),(63,'ct001','chentao001','9a3f65939c547c0535e822a49f340a088bc7172bf69b895dc5aeeaa63f2ce399','gu354F',6,NULL,1,'','','陈涛','2025-01-23 21:04:02','2025-05-13 16:09:31',1),(64,'cw001','财务001','e584f828538b7a28687fe059eef0e1c73b92e97b3ba94c8a2ac53980168a6ffb','3N5f4u',4,NULL,1,'','','财务','2025-01-24 12:09:22','2025-02-04 20:22:22',1),(65,'cw002','cw002','36a983c8d39ca96d26ff43a0cf1f874d517ea68f9e84835f6a83df56203c7532','wqPhys',4,NULL,1,'','','','2025-01-31 16:53:31','2025-05-13 16:21:33',1),(66,'HJL','韩纪玲','a7b867726fd078f3a889471e7fc302f5a0ce9fff648a0e472e55b92f791b06ed','bc5yJ4',3,NULL,2,'','','韩','2025-05-12 14:26:39','2025-07-09 11:55:39',1),(67,'hzy001','何志洋1','350034d0f14bedeccf72f79f050e885cd67030d5f7be2dd13158fd8438d90eff','5zvWUA',6,NULL,1,'','','','2025-05-28 16:43:41','2025-05-28 16:44:44',3),(68,'mik-1','misha','0c8d770b316677cb7d5d3fd965c17e719a1d5c089b72edd7e1368fc7cfc4489f','9t9xNm',2,NULL,1,'','','','2025-06-09 16:00:27','2025-07-09 11:56:11',1),(69,'test1','test1','33a01e3057f7237ba65566e908839c69c2749e9c7c70bb22a26cab53e06cdcf2','DKWTbE',5,NULL,1,'<EMAIL>','13467986534','','2025-06-27 10:04:46','2025-06-27 10:10:28',3),(70,'chentao','陈涛','8b62ead9d5a93739f5693cfca6d41b73db7e4dab07cc2e811b4e280b12b53f75','YPeVHm',6,NULL,1,'','','','2025-07-08 16:19:13','2025-07-08 17:35:14',1);
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`) USING BTREE,
  KEY `FKhh52n8vd4ny9ff4x9fb8v65qx` (`role_id`) USING BTREE,
  CONSTRAINT `FKb40xxfch70f5qnyfw8yme1n1s` FOREIGN KEY (`user_id`) REFERENCES `sys_user` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKhh52n8vd4ny9ff4x9fb8v65qx` FOREIGN KEY (`role_id`) REFERENCES `sys_role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1),(18,5),(19,5),(20,5),(29,5),(34,5),(39,5),(40,5),(42,5),(45,5),(3,6),(25,6),(26,6),(49,6),(66,6),(27,7),(28,7),(56,7),(57,7),(61,7),(63,7),(67,7),(68,7),(70,7),(51,8),(53,8),(49,9),(52,9),(56,9),(57,9),(61,9),(67,9),(70,9),(50,10),(3,11),(66,11),(64,13),(65,13),(66,14),(70,14),(66,16),(68,17);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-02  6:08:58
