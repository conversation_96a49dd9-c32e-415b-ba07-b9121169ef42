<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="timo-detail-page">
        <div class="timo-detail-title">基本信息 Основная информация</div>
        <table class="layui-table timo-detail-table">
            <colgroup>
                <col width="100px"><col>
                <col width="100px"><col>
            </colgroup>
            <tbody>
                <tr>
                    <th>主键ID Первичный ключ ID</th>
                    <td th:text="${transport.id}"></td>
                    <th>单据号 код-данных</th>
                    <td th:text="${transport.orderNo}"></td>
                </tr>
                <tr>
                    <th>运输方式 вид транс</th>
                    <td th:text="${#dicts.keyValue('TRANSPORT_TYPE', transport.transportType)}"></td>
                    <th>运输公司 Трансп.компания</th>
                    <td th:text="${#dicts.keyValue('TRANSPORT_COMPANY', transport.transportCompany)}"></td>
                </tr>
                <tr>
                    <th>中国总费用 Итого(КНР)</th>
                    <td th:text="'$' + ${transport.chinaTotalCost}"></td>
                    <th>俄罗斯总费用 Итого(РФ)</th>
                    <td th:text="'$' + ${transport.russiaTotalCost}"></td>
                </tr>
                <tr>
                    <th>中国经办人 ОЛ (КНР)</th>
                    <td th:text="${transport.chinaHandler}"></td>
                    <th>俄罗斯经办人 ОЛ (РФ)</th>
                    <td th:text="${transport.russiaHandler}"></td>
                </tr>
                <tr>
                    <th>启运日期 дата отп</th>
                    <td th:text="${#dates.format(transport.departureDate, 'yyyy-MM-dd')}"></td>
                    <th>送达日期 Дата Доставки</th>
                    <td th:text="${#dates.format(transport.deliveryDate, 'yyyy-MM-dd')}"></td>
                </tr>
                <tr>
                    <th>创建者 Создатель</th>
                    <td th:text="${transport.createBy?.nickname}"></td>
                    <th>更新者 Обновивший</th>
                    <td th:text="${transport.updateBy?.nickname}"></td>
                </tr>
                <tr>
                    <th>创建时间 дата создания</th>
                    <td th:text="${#dates.format(transport.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <th>更新时间 Время обновления</th>
                    <td th:text="${#dates.format(transport.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </tr>
                <tr>
                    <th>备注 Примечание</th>
                    <td th:text="${transport.remark}" colspan="3"></td>
                </tr>
            </tbody>
        </table>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
