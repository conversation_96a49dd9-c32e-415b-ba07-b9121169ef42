# 字典项明细表

## 字典项配置说明

所有字典项按以下格式配置：
- **业务CODE**：用于系统内部标识
- **业务描述**：用于界面显示的中文说明
- **键**：字典项的唯一标识
- **值**：字典项的显示文本
- **展示顺序**：在下拉框中的排序
- **可用状态**：是否启用该字典项

## 详细字典项列表

### 1. 客户所在国家 (CUST_LOCAL)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| CN | 中国 | 1 | TRUE | 中国客户 |
| RU | 俄罗斯 | 2 | TRUE | 俄罗斯客户 |

### 2. 客户类型 (CUST_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| INI | 个人 | 1 | TRUE | 个人客户 |
| COM | 公司 | 2 | TRUE | 企业客户 |

### 3. 合同状态 (PUR_CON_STATUS)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| INIT | 初始化 | 1 | TRUE | 合同刚创建 |
| FINISH | 已完结 | 2 | TRUE | 合同执行完成 |
| CANCLE | 作废 | 3 | TRUE | 合同取消 |

### 4. 合同收款方 (PUR_PAYEE_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| LOCAL | 境内公司 | 1 | TRUE | 国内收款 |
| OVERSEA | 境外公司 | 2 | TRUE | 国外收款 |

### 5. 币种 (CUR_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| RMB | 人民币 | 1 | TRUE | 中国法定货币 |
| RUB | 卢布 | 2 | TRUE | 俄罗斯法定货币 |
| USD | 美元 | 3 | TRUE | 国际结算货币 |

### 6. 业务类型 (BIZ_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| PUR | 采购 | 1 | TRUE | 采购业务 |
| TRANS | 物流 | 2 | TRUE | 物流业务 |

### 7. 资金流向 (FUND_FLOW)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| MINUS | 负相关 | 1 | TRUE | 资金流出 |
| PLUS | 正相关 | 2 | TRUE | 资金流入 |

### 8. 成本类型 (COST_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| TAX_FREE | 退税 | 1 | TRUE | 出口退税 |
| PROFIT_BACK | 返点 | 2 | TRUE | 供应商返点 |
| CASH_PAY | 现金 | 3 | TRUE | 现金支出 |
| CARGO_DAM | 货损 | 4 | TRUE | 货物损失 |
| COMPLAINT | 投诉 | 5 | TRUE | 客户投诉成本 |
| CARGO_DELAY | 延误 | 6 | TRUE | 货物延误成本 |

### 9. 运输状态 (TRANS_STATUS)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| INIT | 初始化 | 1 | TRUE | 运输订单创建 |
| ROADING | 运输中 | 2 | TRUE | 货物在途 |
| FINISH | 已完结 | 3 | TRUE | 运输完成 |

### 10. 物流入账状态 (TRANS_PAYEE_ACC_STATUS)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| INIT | 初始化 | 1 | TRUE | 未入账 |
| FINISH | 已完结 | 2 | TRUE | 已入账 |

### 11. 物流收款类型 (TRANS_PAYEE_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| L_P | 国内个人 | 1 | TRUE | 国内个人收款 |
| L_C | 国内公司 | 2 | TRUE | 国内公司收款 |
| O_P | 国外个人 | 3 | TRUE | 国外个人收款 |
| O_C | 国外公司 | 4 | TRUE | 国外公司收款 |

### 12. 货物描述 (GOODS_DESC)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| DATA_LINE | 数据线 | 1 | TRUE | 电子产品配件 |
| BRAKE_PADS | 刹车片 | 2 | TRUE | 汽车配件 |

*注：此类型可根据实际业务扩展*

### 13. 物流付款发生地 (TRANS_LOC_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| LOCAL | 境内公司 | 1 | TRUE | 国内付款 |
| OVERSEA | 境外公司 | 2 | TRUE | 国外付款 |

### 14. 物流付款类型 (TRANS_PAYMENT_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| LOC_TRANS_FEES | 国内运费 | 1 | TRUE | 国内段运输费用 |
| LOC_TRANS_STO | 国内仓储 | 2 | TRUE | 国内仓储费用 |
| LOC_TRANS_PER | 国内杂费 | 3 | TRUE | 国内其他费用 |
| OVE_TRANS_FEES | 国外运维 | 4 | TRUE | 国外运输费用 |
| OVE_TRANS_CUST | 国外清关费 | 5 | TRUE | 国外清关费用 |

### 15. 货运方式 (TRANS_TYPE)

| 键 | 值 | 展示顺序 | 可用状态 | 说明 |
|---|---|---------|---------|------|
| ROAD_EXP | 陆运特快 | 1 | TRUE | 公路特快运输 |
| ROAD_EXP_SUP | 陆运超快 | 2 | TRUE | 公路超快运输 |
| ROAD_EXP_ORI | 陆运普快 | 3 | TRUE | 公路普通运输 |
| RAIL_EXP | 铁路 | 4 | TRUE | 铁路运输 |
| SEA_EXP | 海运 | 5 | TRUE | 海上运输 |
| AIR_TYPE | 空运 | 6 | TRUE | 航空运输 |

## 字典维护说明

### 扩展原则
- 所有标记为"绿标"的字典项可根据实际业务需要扩展
- 新增字典项时需要遵循现有的命名规范
- 确保键值的唯一性和业务含义的准确性

### 维护规则
1. **键值唯一性**：同一业务CODE下的键不能重复
2. **展示顺序**：数字越小排序越靠前
3. **可用状态**：FALSE状态的字典项不在前端显示，但保留数据
4. **国际化**：值字段需要同时维护中俄双语版本

### 系统影响
- 字典项的增删改会实时影响相关下拉框
- 删除字典项前需要检查是否有业务数据引用
- 修改键值可能影响历史数据的显示
