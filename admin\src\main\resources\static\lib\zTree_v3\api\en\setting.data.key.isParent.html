<div class="apiDetail">
<div>
	<h2><span>String</span><span class="path">setting.data.key.</span>isParent</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>The node data's attribute to save whether the node is the parent node.</p>
			<p>Default: "isParent"</p>
			<p class="highlight_red">v3.5.32+</p>
		</div>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Set the 'parent' attribute to save whether the node is the parent node.</h4>
	<pre xmlns=""><code>var setting = {
	data: {
		key: {
			isParent: "parent"
		}
	}
};
......</code></pre>
</div>
</div>