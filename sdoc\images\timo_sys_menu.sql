INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (1, '菜单管理', 2, '[0],[2]', '/system/menu/index', 'system:menu:index', '', 2, 3, '', '2018-09-29 00:02:10', '2019-02-24 16:10:40', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (2, '系统管理', 0, '[0]', '#', '#', 'fa fa-cog', 1, 2, '', '2018-09-29 00:05:50', '2019-02-27 21:34:56', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (3, '添加', 1, '[0],[2],[1]', '/system/menu/add', 'system:menu:add', '', 3, 1, '', '2018-09-29 00:06:57', '2019-02-24 16:12:59', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (4, '角色管理', 2, '[0],[2]', '/system/role/index', 'system:role:index', '', 2, 2, '', '2018-09-29 00:08:14', '2019-02-24 16:10:34', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (5, '添加', 4, '[0],[2],[4]', '/system/role/add', 'system:role:add', '', 3, 1, '', '2018-09-29 00:09:04', '2019-02-24 16:12:04', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (6, '主页', 0, '[0]', '/index', 'index', 'layui-icon layui-icon-home', 1, 1, '', '2018-09-29 00:09:56', '2019-02-27 21:34:56', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (7, '用户管理', 2, '[0],[2]', '/system/user/index', 'system:user:index', '', 2, 1, '', '2018-09-29 00:43:50', '2019-04-05 17:43:25', 1, 2, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (8, '编辑', 1, '[0],[2],[1]', '/system/menu/edit', 'system:menu:edit', '', 3, 2, '', '2018-09-29 00:57:33', '2019-02-24 16:13:05', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (9, '详细', 1, '[0],[2],[1]', '/system/menu/detail', 'system:menu:detail', '', 3, 3, '', '2018-09-29 01:03:00', '2019-02-24 16:13:12', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (10, '修改状态', 1, '[0],[2],[1]', '/system/menu/status', 'system:menu:status', '', 3, 4, '', '2018-09-29 01:03:43', '2019-02-24 16:13:21', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (11, '编辑', 4, '[0],[2],[4]', '/system/role/edit', 'system:role:edit', '', 3, 2, '', '2018-09-29 01:06:13', '2019-02-24 16:12:10', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (12, '授权', 4, '[0],[2],[4]', '/system/role/auth', 'system:role:auth', '', 3, 3, '', '2018-09-29 01:06:57', '2019-02-24 16:12:17', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (13, '详细', 4, '[0],[2],[4]', '/system/role/detail', 'system:role:detail', '', 3, 4, '', '2018-09-29 01:08:00', '2019-02-24 16:12:24', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (14, '修改状态', 4, '[0],[2],[4]', '/system/role/status', 'system:role:status', '', 3, 5, '', '2018-09-29 01:08:22', '2019-02-24 16:12:43', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (15, '编辑', 7, '[0],[2],[7]', '/system/user/edit', 'system:user:edit', '', 3, 2, '', '2018-09-29 21:17:17', '2019-02-24 16:11:03', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (16, '添加', 7, '[0],[2],[7]', '/system/user/add', 'system:user:add', '', 3, 1, '', '2018-09-29 21:17:58', '2019-02-24 16:10:28', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (17, '修改密码', 7, '[0],[2],[7]', '/system/user/pwd', 'system:user:pwd', '', 3, 3, '', '2018-09-29 21:19:40', '2019-02-24 16:11:11', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (18, '权限分配', 7, '[0],[2],[7]', '/system/user/role', 'system:user:role', '', 3, 4, '', '2018-09-29 21:20:35', '2019-02-24 16:11:18', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (19, '详细', 7, '[0],[2],[7]', '/system/user/detail', 'system:user:detail', '', 3, 5, '', '2018-09-29 21:21:00', '2019-02-24 16:11:26', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (20, '修改状态', 7, '[0],[2],[7]', '/system/user/status', 'system:user:status', '', 3, 6, '', '2018-09-29 21:21:18', '2019-02-24 16:11:35', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (21, '字典管理', 2, '[0],[2]', '/system/dict/index', 'system:dict:index', '', 2, 5, '', '2018-10-05 00:55:52', '2019-02-24 16:14:24', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (22, '字典添加', 21, '[0],[2],[21]', '/system/dict/add', 'system:dict:add', '', 3, 1, '', '2018-10-05 00:56:26', '2019-02-24 16:14:10', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (23, '字典编辑', 21, '[0],[2],[21]', '/system/dict/edit', 'system:dict:edit', '', 3, 2, '', '2018-10-05 00:57:08', '2019-02-24 16:14:34', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (24, '字典详细', 21, '[0],[2],[21]', '/system/dict/detail', 'system:dict:detail', '', 3, 3, '', '2018-10-05 00:57:42', '2019-02-24 16:14:41', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (25, '修改状态', 21, '[0],[2],[21]', '/system/dict/status', 'system:dict:status', '', 3, 4, '', '2018-10-05 00:58:27', '2019-02-24 16:14:49', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (26, '行为日志', 2, '[0],[2]', '/system/actionLog/index', 'system:actionLog:index', '', 2, 6, '', '2018-10-14 16:52:01', '2019-02-27 21:34:15', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (27, '日志详细', 26, '[0],[2],[26]', '/system/actionLog/detail', 'system:actionLog:detail', '', 3, 1, '', '2018-10-14 21:07:11', '2019-02-27 21:34:15', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (28, '日志删除', 26, '[0],[2],[26]', '/system/actionLog/delete', 'system:actionLog:delete', '', 3, 2, '', '2018-10-14 21:08:17', '2019-02-27 21:34:15', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (30, '开发中心', 0, '[0]', '#', '#', 'fa fa-gavel', 1, 3, '', '2018-10-19 16:38:23', '2019-02-27 21:34:56', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (31, '代码生成', 30, '[0],[30]', '/dev/code', '#', '', 2, 1, '', '2018-10-19 16:39:04', '2019-03-13 17:43:58', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (125, '表单构建', 30, '[0],[30]', '/dev/build', '#', '', 2, 2, '', '2018-11-25 16:12:23', '2019-02-24 16:16:40', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (136, '部门管理', 2, '[0],[2]', '/system/dept/index', 'system:dept:index', '', 2, 4, '', '2018-12-02 16:33:23', '2019-02-24 16:10:50', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (137, '添加', 136, '[0],[2],[136]', '/system/dept/add', 'system:dept:add', '', 3, 1, '', '2018-12-02 16:33:23', '2019-02-24 16:13:34', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (138, '编辑', 136, '[0],[2],[136]', '/system/dept/edit', 'system:dept:edit', '', 3, 2, '', '2018-12-02 16:33:23', '2019-02-24 16:13:42', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (139, '详细', 136, '[0],[2],[136]', '/system/dept/detail', 'system:dept:detail', '', 3, 3, '', '2018-12-02 16:33:23', '2019-02-24 16:13:49', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (140, '改变状态', 136, '[0],[2],[136]', '/system/dept/status', 'system:dept:status', '', 3, 4, '', '2018-12-02 16:33:23', '2019-02-24 16:13:57', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (146, '数据接口', 30, '[0],[30]', '/dev/swagger', '#', '', 2, 3, '', '2018-12-09 21:11:11', '2019-02-24 23:38:18', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (147, '资金管理', 0, '[0]', '/demo/t1/index', 'demo:t1:index', 'layui-icon layui-icon-template-1', 1, 4, '', '2021-10-25 15:01:29', '2021-10-25 17:00:13', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (148, '添加', 147, '[0],[147]', '/demo/t1/add', 'demo:t1:add', null, 3, 1, null, '2021-10-25 15:01:30', '2021-10-25 15:01:30', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (149, '编辑', 147, '[0],[147]', '/demo/t1/edit', 'demo:t1:edit', null, 3, 1, null, '2021-10-25 15:01:30', '2021-10-25 15:01:30', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (150, '详细', 147, '[0],[147]', '/demo/t1/detail', 'demo:t1:detail', null, 3, 1, null, '2021-10-25 15:01:30', '2021-10-25 15:01:30', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (151, '修改状态', 147, '[0],[147]', '/demo/t1/status', 'demo:t1:status', null, 3, 1, null, '2021-10-25 15:01:30', '2021-10-25 15:01:30', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (157, '客户资金管理', 0, '[0]', '/demo/t1/index2', 'demo:t1:index2', 'layui-icon layui-icon-user', 1, 5, '', '2021-10-25 16:57:08', '2021-10-25 16:57:08', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (158, '详情', 157, '[0],[157]', '/demo/t1/detail', 'demo:t1:detail', '', 3, 1, '', '2021-10-25 16:58:37', '2021-10-25 16:58:37', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (159, '余额调整', 0, '[0]', '/acct/info/index', 'acct:info:index', 'fa fa-cog', 1, 6, '', '2021-10-25 21:06:18', '2021-10-25 21:07:17', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (160, '添加', 159, '[0],[159]', '/acct/info/add', 'acct:info:add', null, 3, 1, null, '2021-10-25 21:06:18', '2021-10-25 21:06:18', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (161, '编辑', 159, '[0],[159]', '/acct/info/edit', 'acct:info:edit', null, 3, 1, null, '2021-10-25 21:06:18', '2021-10-25 21:06:18', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (162, '详细', 159, '[0],[159]', '/acct/info/detail', 'acct:info:detail', null, 3, 1, null, '2021-10-25 21:06:18', '2021-10-25 21:06:18', 1, 1, 1);
INSERT INTO timo.sys_menu (id, title, pid, pids, url, perms, icon, type, sort, remark, create_date, update_date, create_by, update_by, status) VALUES (163, '修改状态', 159, '[0],[159]', '/acct/info/status', 'acct:info:status', null, 3, 1, null, '2021-10-25 21:06:18', '2021-10-25 21:06:18', 1, 1, 1);