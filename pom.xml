<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <name>Timo后台管理系统</name>
    <description>快速搭建后台管理的系统</description>

    <groupId>com.linln</groupId>
    <artifactId>timo</artifactId>
    <version>2.0.3</version>
    <packaging>pom</packaging>

    <modules>
        <module>admin</module>
        <module>common</module>
        <module>component</module>
        <module>devtools</module>
        <module>modules</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.4.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <java.version>1.8</java.version>
        <!--shiro权限管理框架版本：component.shiro-->
        <shiro.version>1.4.0</shiro.version>
        <lombok.version>1.18.2</lombok.version>
        <ehcache.version>2.10.5</ehcache.version>
        <mysql.connector.version>5.1.46</mysql.connector.version>
        <jsoup.version>1.11.3</jsoup.version>
        <!--shiro模板视图权限标签扩展：component.thymeleaf-->
        <thymeleaf-shiro.version>2.0.0</thymeleaf-shiro.version>
        <!--excel工具框架版本：component.excel-->
        <poi.version>4.0.0</poi.version>
        <!--jwt处理框架版本：component.jwt-->
        <jwt.version>3.4.0</jwt.version>
        <!--swagger2接口文档版本：component.jwt-->
        <swagger.version>2.9.2</swagger.version>
        <!--swagger-knife4j接口文档页面：component.jwt-->
        <swagger.knife4j.version>2.0.1</swagger.knife4j.version>
        <!--hutool工具包版本-->
        <hutool.version>5.8.10</hutool.version>
        <google.findbugs.version>3.0.1</google.findbugs.version>
        <skipTests>true</skipTests>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--hutool工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--spring data jpa持久层框架-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!--mysql连接驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.connector.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!--lombok语法糖-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <optional>true</optional>
        </dependency>

        <!--ehcache缓存框架-->
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>${ehcache.version}</version>
        </dependency>

        <!--html解析工具，处理xss攻击-->
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>

        <!--swagger2接口文档-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger.version}</version>
        </dependency>
        <!--knife4j（swagger-bootstrap-ui）接口文档页面-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
            <version>${swagger.knife4j.version}</version>
        </dependency>

        <!--解决编译时javax.annotation.meta.When不存在问题-->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
            <version>${google.findbugs.version}</version>
        </dependency>
    </dependencies>

    <!--多环境配置-->
    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.name>dev</profile.name>
            </properties>
        </profile>
        <!--生产环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profile.name>prod</profile.name>
            </properties>
        </profile>
    </profiles>
</project>
