package com.linln.admin.acct.controller;

import com.linln.admin.acct.domain.Info;
import com.linln.admin.acct.service.InfoService;
import com.linln.admin.acct.validator.InfoValid;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/25
 */
@Controller
@RequestMapping("/acct/info")
public class InfoController {

    @Autowired
    private InfoService infoService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("acct:info:index")
    public String index(Model model, Info info) {

        // 创建匹配器，进行动态查询匹配
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("userName", match -> match.contains());

        // 获取数据列表
        Example<Info> example = Example.of(info, matcher);
        Page<Info> list = infoService.getPageList(example);

        // 封装数据
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        return "/acct/info/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("acct:info:add")
    public String toAdd() {
        return "/acct/info/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("acct:info:edit")
    public String toEdit(@PathVariable("id") Info info, Model model) {
        model.addAttribute("info", info);
        return "/acct/info/add";
    }

    /**
     * 保存添加/修改的数据
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"acct:info:add", "acct:info:edit"})
    @ResponseBody
    public ResultVo save(@Validated InfoValid valid, Info info) {
        // 复制保留无需修改的数据
        if (info.getId() != null) {
            Info beInfo = infoService.getById(info.getId());
            EntityBeanUtil.copyProperties(beInfo, info);
        }

        // 保存数据
        infoService.save(info);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("acct:info:detail")
    public String toDetail(@PathVariable("id") Info info, Model model) {
        model.addAttribute("info",info);
        return "/acct/info/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("acct:info:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (infoService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}