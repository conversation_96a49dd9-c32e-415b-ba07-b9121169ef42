<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        a{
            color: #005980;
        }
        .widget-small{
            background-color: #FFFFFF;
            overflow: hidden;
            text-align: center;
            border-radius: 2px;
            box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
        }
        .widget-small .fa{
            float: left;
            width: 40%;
            line-height: 80px;
            color: #FFFFFF;
        }
        .widget-user .fa{
            background-color: #029789;
        }
        .widget-visit .fa{
            background-color: #17a2b8;
        }
        .widget-message .fa{
            background-color: #fbad4c;
        }
        .widget-like .fa{
            background-color: #ff646d;
        }
        .widget-small-info{
            float: left;
            text-align: left;
            width: 40%;
            margin-left: 20px;
            margin-top: 18px;
            line-height: 24px;
        }
        .widget-small-info h4{
            font-size: 18px;
        }
        .widget-small-info span{
            font-size: 16px;
        }
        .project-introduce{
            min-height: 466px;
        }
        .project-introduce h4{
            font-weight: bold;
            margin-top: 12px;
            margin-bottom: 8px;
        }
        .project-introduce li{
            list-style: decimal;
            margin-left: 28px;
        }
        .alert {
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid transparent;
            border-radius: 4px;
        }
        .alert-info {
            color: #31708f;
            background-color: #d9edf7;
            border-color: #bce8f1;
        }
    </style>
</head>
<body class="timo-layout-page">
<h1>欢迎</h1>
<script th:replace="/common/template :: script"></script>
</body>
</html>

