<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
<div class="layui-form timo-compile">
    <form th:action="@{/order/orderLog/save}">
        <input type="hidden" name="id" th:if="${orderLog}" th:value="${orderLog.id}">
        <div th:if="${orderLog}" class="layui-form-item">
            <label class="layui-form-label">订单号码</label>
            <div th:text="${orderLog.orderNo}" class="layui-input-inline">
            </div>
        </div>
        <div th:if="${externalFlag}" class="layui-form-item">
            <label class="layui-form-label">日期 дата </label>
            <div th:text="${orderLog?.markup}" class="layui-input-inline">
            </div>
        </div>
        <div th:if="${externalFlag}" class="layui-form-item">
            <label class="layui-form-label">客户 клиент</label>
            <div th:text="${orderLog?.customer}" class="layui-input-inline">
            </div>
        </div>
        <div th:if="${externalFlag}" class="layui-form-item">
            <label class="layui-form-label">工号  USER CN</label>
            <div th:text="${orderLog?.manager}" class="layui-input-inline">
            </div>
        </div>
        <div th:if="${externalFlag}" class="layui-form-item">
            <label class="layui-form-label">货物编码 код партия</label>
            <div th:text="${orderLog?.goodsNo}" class="layui-input-inline">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}"  class="layui-form-item">
            <label class="layui-form-label">日期 дата </label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="markup" placeholder="请输入日期 дата" th:value="${orderLog?.markup}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}"  class="layui-form-item">
            <label class="layui-form-label">客户 клиент</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="customer" placeholder="请输入客户 клиент" th:value="${orderLog?.customer}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}"  class="layui-form-item">
            <label class="layui-form-label">工号  USER CN</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="manager" placeholder="请输入工号  USER CN" th:value="${orderLog?.manager}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}"  class="layui-form-item">
            <label class="layui-form-label">货物编码 код партия</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="goodsNo" placeholder="请输入货物编码 код партия" th:value="${orderLog?.goodsNo}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">订单金额 цена китай</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="internalOrdAmt" placeholder="请输入订单金额 цена китай" th:value="${orderLog?.internalOrdAmt}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">采购费 закупок КЧ</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="buyFees" placeholder="请输入采购费 закупок КЧ" th:value="${orderLog?.buyFees}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">国内支出 ПРР CN</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="internalFees" placeholder="请输入国内支出 ПРР CN" th:value="${orderLog?.internalFees}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">国际运费 COC</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="internationalFees" placeholder="请输入国际运费 COC" th:value="${orderLog?.internationalFees}">
            </div>
        </div>
        <div th:if="${internalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">备注 примечание </label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="internalRemark" placeholder="请输入备注 примечание " th:value="${orderLog?.internalRemark}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">国外 USER RU </label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="userRuss" placeholder="请输入国外 USER RU" th:value="${orderLog?.userRuss}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">ПП 关税</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="taxFees" placeholder="请输入ПП 关税" th:value="${orderLog?.taxFees}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">ПП %</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="taxExtraFees" placeholder="请输入ПП %" th:value="${orderLog?.taxExtraFees}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">合同 ИТС</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="ctFees" placeholder="请输入合同 ИТС" th:value="${orderLog?.ctFees}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">ИТС %</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="ctExtrFees" placeholder="请输入ИТС %" th:value="${orderLog?.ctExtrFees}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">КЧ БН 服务</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="russclearFees" placeholder="请输入КЧ БН 服务" th:value="${orderLog?.russclearFees}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">ПРР и авто</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="costAmt" placeholder="请输入ПРР и авто" th:value="${orderLog?.costAmt}">
            </div>
        </div>
        <div th:if="${externalFlag} or ${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">销售价 продажа </label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="salesAmt" placeholder="请输入销售价 продажа " th:value="${orderLog?.salesAmt}">
            </div>
        </div>
        <div th:if="${bossFlag}" class="layui-form-item">
            <label class="layui-form-label">说明 примечание</label>
            <div class="layui-input-inline">
                <input class="layui-input" type="text" name="explaintt" placeholder="请输入说明 примечание" th:value="${orderLog?.explaintt}">
            </div>
        </div>
        <div class="layui-form-item timo-finally">
            <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存</button>
            <button class="layui-btn btn-secondary close-popup"><i class="fa fa-times-circle"></i> 关闭</button>
        </div>
    </form>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>