<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">setting.view.</span>nameIsHTML</h2>
	<h3>Overview<span class="h3_info">[ depends on <span class="highlight_green">jquery.ztree.core</span> js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>Set to use HTML in 'name' attribute.</p>
			<p class="highlight_red">If allow HTML, please do check to avoid security issues, e.g. JavaScript Injection...</p>
			<p>Default: false</p>
		</div>
	</div>
	<h3>Boolean Format</h3>
	<div class="desc">
	<p> true means: 'name' attribute can be HTML.</p>
	<p> false means: 'name' attribute is only TEXT.</p>
	</div>
	<h3>Examples of setting</h3>
	<h4>1. Set to allow HTML</h4>
	<pre xmlns=""><code>var setting = {
	view: {
		nameIsHTML: true
	}
};
var node = {"name":"&lt;font color='red'&gt;test&lt;/font&gt;"};
......</code></pre>
</div>
</div>