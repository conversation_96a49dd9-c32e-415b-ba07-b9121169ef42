package com.linln.admin.psm.controller;

import com.linln.admin.psm.domain.GoodsInfo;
import com.linln.admin.psm.service.GoodsInfoService;
import com.linln.admin.psm.validator.GoodsInfoValid;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Controller
@RequestMapping("/psm/goodsInfo")
public class GoodsInfoController {

    @Autowired
    private GoodsInfoService goodsInfoService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("psm:goodsInfo:index")
    public String index(Model model, GoodsInfo goodsInfo) {

        // 创建匹配器，进行动态查询匹配
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING)
                .withIgnoreCase(true);
        // 获取数据列表
        Example<GoodsInfo> example = Example.of(goodsInfo, matcher);
        Page<GoodsInfo> list = goodsInfoService.getPageList(example);

        // 封装数据
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        return "/psm/goodsInfo/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("psm:goodsInfo:add")
    public String toAdd() {
        return "/psm/goodsInfo/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("psm:goodsInfo:edit")
    public String toEdit(@PathVariable("id") GoodsInfo goodsInfo, Model model) {
        model.addAttribute("goodsInfo", goodsInfo);
        return "/psm/goodsInfo/add";
    }

    /**
     * 保存添加/修改的数据
     *
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"psm:goodsInfo:add", "psm:goodsInfo:edit"})
    @ResponseBody
    public ResultVo save(@Validated GoodsInfoValid valid, GoodsInfo goodsInfo) {
        // 复制保留无需修改的数据
        if (goodsInfo.getId() != null) {
            GoodsInfo beGoodsInfo = goodsInfoService.getById(goodsInfo.getId());
            EntityBeanUtil.copyProperties(beGoodsInfo, goodsInfo);
        }

        // 保存数据
        goodsInfoService.save(goodsInfo);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("psm:goodsInfo:detail")
    public String toDetail(@PathVariable("id") GoodsInfo goodsInfo, Model model) {
        model.addAttribute("goodsInfo", goodsInfo);
        return "/psm/goodsInfo/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("psm:goodsInfo:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (goodsInfoService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}
