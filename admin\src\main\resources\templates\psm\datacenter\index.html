<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <title>数据中心 - 数据导出管理</title>
    <style>
        .data-center-card {
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .data-center-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        .data-center-icon {
            font-size: 48px;
            color: #009688;
            text-align: center;
            margin-bottom: 15px;
        }
        .data-center-title {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        .data-center-desc {
            color: #999;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-database"></i> 数据中心 数据导出管理</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        </div>
        <div class="layui-card-body">
            <div class="layui-row layui-col-space15">
                <!-- 合同数据导出 -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="goToContractExport()">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-file-contract"></i>
                            </div>
                            <div class="data-center-title">采购合同数据导出</div>
                            <div class="data-center-desc">
                                导出采购合同数据，支持筛选条件和统计预览
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 货物信息导出 (预留) -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="showComingSoon('货物信息导出')">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-boxes"></i>
                            </div>
                            <div class="data-center-title">货物信息导出</div>
                            <div class="data-center-desc">
                                导出货物信息数据 (即将推出)
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 运输信息导出 (预留) -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="showComingSoon('运输信息导出')">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-truck"></i>
                            </div>
                            <div class="data-center-title">运输信息导出</div>
                            <div class="data-center-desc">
                                导出物流运输数据 (即将推出)
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 客户信息导出 (预留) -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="showComingSoon('客户信息导出')">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-users"></i>
                            </div>
                            <div class="data-center-title">客户信息导出</div>
                            <div class="data-center-desc">
                                导出客户联系人数据 (即将推出)
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 综合报表导出 (预留) -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="showComingSoon('综合报表导出')">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-chart-bar"></i>
                            </div>
                            <div class="data-center-title">综合报表导出</div>
                            <div class="data-center-desc">
                                导出业务综合分析报表 (即将推出)
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据统计总览 (预留) -->
                <div class="layui-col-md6 layui-col-lg4">
                    <div class="layui-card data-center-card" onclick="showComingSoon('数据统计总览')">
                        <div class="layui-card-body">
                            <div class="data-center-icon">
                                <i class="fa fa-chart-pie"></i>
                            </div>
                            <div class="data-center-title">数据统计总览</div>
                            <div class="data-center-desc">
                                查看业务数据统计总览 (即将推出)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.use(['layer'], function () {
            var layer = layui.layer;
            
            window.goToContractExport = function() {
                window.location.href = '/psm/datacenter/contract';
            };
            
            window.showComingSoon = function(feature) {
                layer.msg(feature + ' 功能即将推出，敬请期待！', {
                    icon: 6,
                    time: 2000
                });
            };
        });
    </script>
</body>
</html>
