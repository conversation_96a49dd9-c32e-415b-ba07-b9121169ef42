### 服务器配置
server:
  port: 8080

### TIMO配置
project:
  captcha-open: false                         #是否开启登录验证码
  shiro:
    remember-me-timeout: 7                    #cookie记住登录信息时间，默认7天（天）
    global-session-timeout: 1800              #Session会话超时时间，默认30分钟（秒）
  upload:
    #file-path: F:/upload/                    #文件上传路径，默认在项目根目录upload下
    static-path: /upload/**                   #上传文件静态访问路径

### spring配置
spring:
  ## 多环境配置
  profiles:
    active: @profile.name@                    #多环境配置，默认为dev开发环境
  messages:
    basename: i18n.login
  ## jpa配置
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: update
    properties:
      hibernate.dialect: com.linln.common.dialect.MySQLDialectUTF8
      hibernate.format_sql: false
    open-in-view: true

  ## thymeleaf模板设置
  thymeleaf:
    prefix: classpath:/templates
    suffix: .html
    mode: HTML
    encoding: utf-8
    servlet.content-type: text/html
    cache: false

  ## 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  ## json配置
  jackson:
    time-zone: Asia/Shanghai

