package com.linln.admin.psm.controller;

import com.linln.admin.psm.service.ContractService;
import com.linln.component.shiro.ShiroUtil;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.mapping.JpaMetamodelMappingContext;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ContractController.class)
public class ContractControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ContractService contractService;

    // Mock JpaMetamodelMappingContext to prevent JPA initialization issues in @WebMvcTest
    @MockBean
    private JpaMetamodelMappingContext jpaMetamodelMappingContext;

    // Mock SecurityManager for Shiro
    @MockBean
    private SecurityManager securityManager;

    @BeforeEach
    void setUp() {
        // Mock Shiro's SecurityUtils.getSubject()
        Subject subject = mock(Subject.class);
        ThreadContext.bind(subject);
        // Mock the verifyRole call
        when(subject.hasRole("boss")).thenReturn(true); // Assume boss role for simplicity in this test
    }


    @Test
    public void testGetStatistics() throws Exception {
        // 1. Mock data
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalAmount", new BigDecimal("10000.00"));
        statistics.put("contractCount", 5L);

        // 2. Mock service layer
        // We use any(Specification.class) because the Specification is created inside the controller
        when(contractService.getStatistics(any(Specification.class))).thenReturn(statistics);

        // 3. Perform request and assert results
        mockMvc.perform(get("/psm/contract/statistics")
                        .param("createDateStr", "2025-01-01 - 2025-12-31"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalAmount").value(10000.00))
                .andExpect(jsonPath("$.data.contractCount").value(5));
    }
}
