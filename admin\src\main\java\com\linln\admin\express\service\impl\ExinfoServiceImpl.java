package com.linln.admin.express.service.impl;

import com.linln.admin.express.domain.Exinfo;
import com.linln.admin.express.domain.ExpressSummaryDto;
import com.linln.admin.express.repository.ExinfoRepository;
import com.linln.admin.express.service.ExinfoService;
import com.linln.common.constant.StatusConst;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.apache.shiro.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.*;
import java.math.BigDecimal;
import java.util.*;

@Service
public class ExinfoServiceImpl implements ExinfoService {

    @Autowired
    private ExinfoRepository exinfoRepository;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * 通用构造 Specification 条件查询器
     */
    @Override
    public Specification<Exinfo> buildSpecByExample(Example<Exinfo> example) {
        Exinfo probe = example.getProbe();

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(probe.getSender())) {
                predicates.add(cb.like(root.get("sender"), "%" + probe.getSender() + "%"));
            }
            if (StringUtils.hasText(probe.getOper())) {
                predicates.add(cb.like(root.get("oper"), "%" + probe.getOper() + "%"));
            }
            if (StringUtils.hasText(probe.getExpressNo())) {
                predicates.add(cb.like(root.get("expressNo"), "%" + probe.getExpressNo() + "%"));
            }
            if (StringUtils.hasText(probe.getCargoCode())) {
                predicates.add(cb.like(root.get("cargoCode"), "%" + probe.getCargoCode() + "%"));
            }

            if (probe.getReachStoreStartDate() != null) {
                Expression<Date> reachDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("reachStoreDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.greaterThanOrEqualTo(reachDateExpr, java.sql.Date.valueOf(probe.getReachStoreStartDate())));
            }
            if (probe.getReachStoreEndDate() != null) {
                Expression<Date> reachDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("reachStoreDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.lessThanOrEqualTo(reachDateExpr, java.sql.Date.valueOf(probe.getReachStoreEndDate())));
            }

            if (probe.getStartDateStartDate() != null) {
                Expression<Date> startDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("startDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.greaterThanOrEqualTo(startDateExpr, java.sql.Date.valueOf(probe.getStartDateStartDate())));
            }
            if (probe.getStartDateEndDate() != null) {
                Expression<Date> startDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("startDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.lessThanOrEqualTo(startDateExpr, java.sql.Date.valueOf(probe.getStartDateEndDate())));
            }

            if (probe.getCreator() != null) {
                predicates.add(cb.equal(root.get("creator"), probe.getCreator()));
            }
            if (StringUtils.hasText(probe.getPaymentType())) {
                predicates.add(cb.equal(root.get("paymentType"), probe.getPaymentType()));
            }
            predicates.add(cb.notEqual(root.get("status"), StatusConst.DELETE));
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 分页查询 - 使用 Specification
     */
    @Override
    public Page<Exinfo> getPageListBySpec(Specification<Exinfo> spec) {
        PageRequest pageRequest = PageSort.pageRequest();
        return exinfoRepository.findAll(spec, pageRequest);
    }

    /**
     * 统计汇总数据
     */
    @Override
    public ExpressSummaryDto getSummaryBySpec(Specification<Exinfo> spec) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<ExpressSummaryDto> query = cb.createQuery(ExpressSummaryDto.class);
        Root<Exinfo> root = query.from(Exinfo.class);

        Expression<BigDecimal> totalRecvAmount = cb.coalesce(cb.sum(root.get("recvAmount")), BigDecimal.ZERO);
        Expression<BigDecimal> unpaidRecvAmount = cb.coalesce(
                cb.sum(
                        cb.<BigDecimal>selectCase()
                                .when(cb.isNull(root.get("paymentDate")), root.get("recvAmount"))
                                .otherwise(BigDecimal.ZERO)
                ), BigDecimal.ZERO
        );
        Expression<BigDecimal> totalInternal = cb.coalesce(cb.sum(root.get("internalAmount")), BigDecimal.ZERO);

        // 拆解国外应付的计算，确保类型匹配
        Expression<BigDecimal> outerAmount = cb.coalesce(root.get("outerAmount"), BigDecimal.ZERO);
        Expression<BigDecimal> outerClearAmount = cb.coalesce(root.get("outerClearAmount"), BigDecimal.ZERO);
        Expression<BigDecimal> outerTotalExpr = cb.sum(outerAmount, outerClearAmount);
        Expression<BigDecimal> totalOuter = cb.coalesce(cb.sum(outerTotalExpr), BigDecimal.ZERO);

        query.select(cb.construct(ExpressSummaryDto.class, totalRecvAmount, unpaidRecvAmount, totalInternal, totalOuter));

        Predicate predicate = spec.toPredicate(root, query, cb);
        if (predicate != null) {
            query.where(predicate);
        }

        return entityManager.createQuery(query).getSingleResult();
    }


    @Override
    public Page<Exinfo> getPageList(Example<Exinfo> example) {
        PageRequest page = PageSort.pageRequest();
        return exinfoRepository.findAll(example, page);
    }

    @Override
    public List<Exinfo> getListByExample(Example<Exinfo> example, Sort sort) {
        return exinfoRepository.findAll(example, sort);
    }

    @Override
    public Exinfo getById(Long id) {
        return exinfoRepository.findById(id).orElse(null);
    }

    @Override
    public Exinfo save(Exinfo exinfo) {
        return exinfoRepository.save(exinfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return exinfoRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }


    @Override
    public Page<Exinfo> getPageListWithDateRange(Example<Exinfo> example) {
        Exinfo probe = example.getProbe();
        PageRequest page = PageSort.pageRequest();

        Specification<Exinfo> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 模糊匹配 sender
            if (StringUtils.hasText(probe.getSender())) {
                predicates.add(cb.like(root.get("sender"), "%" + probe.getSender() + "%"));
            }
            // 模糊匹配 oper
            if (StringUtils.hasText(probe.getOper())) {
                predicates.add(cb.like(root.get("oper"), "%" + probe.getOper() + "%"));
            }
            //模糊匹配expressNo
            if (StringUtils.hasText(probe.getExpressNo())) {
                predicates.add(cb.like(root.get("expressNo"), "%" + probe.getExpressNo() + "%"));
            }
            //模糊匹配cargoCode
            if (StringUtils.hasText(probe.getCargoCode())) {
                predicates.add(cb.like(root.get("cargoCode"), "%" + probe.getCargoCode() + "%"));
            }
            // 区间查询 reach_store_date（为字符串字段）
            if (probe.getReachStoreStartDate() != null) {
                Expression<Date> reachDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("reachStoreDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.greaterThanOrEqualTo(reachDateExpr,
                        java.sql.Date.valueOf(probe.getReachStoreStartDate())));
            }
            // 区间查询 reach_store_date（为字符串字段）
            if (probe.getReachStoreEndDate() != null) {
                Expression<Date> reachDateExpr = cb.function("STR_TO_DATE", Date.class,
                        root.get("reachStoreDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.lessThanOrEqualTo(reachDateExpr,
                        java.sql.Date.valueOf(probe.getReachStoreEndDate())));
            }
            // 区间查询 startDate（为字符串字段）
            if (probe.getStartDateStartDate() != null) {
                Expression<Date> startDate = cb.function("STR_TO_DATE", Date.class,
                        root.get("startDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.greaterThanOrEqualTo(startDate,
                        java.sql.Date.valueOf(probe.getStartDateStartDate())));
            }
            if (probe.getStartDateEndDate() != null) {
                Expression<Date> startDate = cb.function("STR_TO_DATE", Date.class,
                        root.get("startDate"), cb.literal("%Y-%m-%d"));
                predicates.add(cb.lessThanOrEqualTo(startDate,
                        java.sql.Date.valueOf(probe.getStartDateEndDate())));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        return exinfoRepository.findAll(spec, page);
    }
}
