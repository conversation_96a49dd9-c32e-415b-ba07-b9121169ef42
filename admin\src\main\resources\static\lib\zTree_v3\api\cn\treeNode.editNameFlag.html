<div class="apiDetail">
<div>
	<h2><span>Boolean</span><span class="path">treeNode.</span>editNameFlag</h2>
	<h3>概述<span class="h3_info">[ 依赖 <span class="highlight_green">jquery.ztree.exedit</span> 扩展 js ]</span></h3>
	<div class="desc">
		<p></p>
		<div class="longdesc">
			<p>用于记录节点是否处于编辑名称状态。<span class="highlight_red">[setting.edit.enable = true 时有效]</span></p>
			<p class="highlight_red">zTree 内部使用，请勿进行初始化 或 随意修改</p>
			<p>默认值：false</p>
		</div>
	</div>
	<h3>Boolean 格式说明</h3>
	<div class="desc">
	<p>true 表示节点处于编辑名称状态</p>
	<p>false 表示节点未处于编辑名称状态</p>
	</div>
</div>
</div>