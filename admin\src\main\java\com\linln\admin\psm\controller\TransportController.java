package com.linln.admin.psm.controller;

import cn.hutool.core.date.DateUtil;
import com.linln.admin.psm.domain.Customer;
import com.linln.admin.psm.domain.Transport;
import com.linln.admin.psm.service.TransportService;
import com.linln.admin.psm.validator.TransportValid;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Controller
@RequestMapping("/psm/transport")
public class TransportController {

    @Autowired
    private TransportService transportService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("psm:transport:index")
    public String index(Model model, Transport transport,
                        @RequestParam(value = "departureDateStr", required = false) String departureDateStr,
                        @RequestParam(value = "deliveryDateStr", required = false) String deliveryDateStr) {

        // 动态查询
        Specification<Transport> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(transport.getOrderNo())) {
                predicates.add(cb.like(root.get("orderNo"), "%" + transport.getOrderNo() + "%"));
            }

            if (StringUtils.hasText(transport.getTransportType())) {
                predicates.add(cb.like(root.get("transportType"), "%" + transport.getTransportType() + "%"));
            }

            if (StringUtils.hasText(transport.getTransportCompany())) {
                predicates.add(cb.equal(root.get("transportCompany"), transport.getTransportCompany()));
            }

            if (StringUtils.hasText(departureDateStr)) {
                String[] dateRange = departureDateStr.split(" - ");
                Date startDate = DateUtil.parse(dateRange[0]);
                Date endDate = DateUtil.parse(dateRange[1]);
                predicates.add(cb.between(root.get("departureDate"), startDate, DateUtil.endOfDay(endDate)));
            }

            if (StringUtils.hasText(deliveryDateStr)) {
                String[] dateRange = deliveryDateStr.split(" - ");
                Date startDate = DateUtil.parse(dateRange[0]);
                Date endDate = DateUtil.parse(dateRange[1]);
                predicates.add(cb.between(root.get("deliveryDate"), startDate, DateUtil.endOfDay(endDate)));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<Transport> list = transportService.getPageList(spec);

        // 封装数据
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        return "/psm/transport/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("psm:transport:add")
    public String toAdd() {
        return "/psm/transport/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("psm:transport:edit")
    public String toEdit(@PathVariable("id") Transport transport, Model model) {
        model.addAttribute("transport", transport);
        return "/psm/transport/add";
    }

    /**
     * 保存添加/修改的数据
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"psm:transport:add", "psm:transport:edit"})
    @ResponseBody
    public ResultVo save(@Validated TransportValid valid, Transport transport) {
        // 复制保留无需修改的数据
        if (transport.getId() != null) {
            Transport beTransport = transportService.getById(transport.getId());
            EntityBeanUtil.copyProperties(beTransport, transport);
        }

        // 保存数据
        transportService.save(transport);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("psm:transport:detail")
    public String toDetail(@PathVariable("id") Transport transport, Model model) {
        model.addAttribute("transport", transport);
        return "/psm/transport/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("psm:transport:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (transportService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
}