<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="timo-detail-page">
        <div class="timo-detail-title">基本信息 Основная информация</div>
        <table class="layui-table timo-detail-table">
            <colgroup>
                <col width="100px"><col>
                <col width="100px"><col>
            </colgroup>
            <tbody>
                <tr>
                    <th>主键ID Первичный ключ ID</th>
                    <td th:text="${goodsInfo.id}"></td>
                    <th>单据号 код-данных</th>
                    <td th:text="${goodsInfo.orderNo}"></td>
                </tr>
                <tr>
                    <th>货物编码 код товара</th>
                    <td th:text="${goodsInfo.goodsNo}"></td>
                    <th>货物名称 наименование товара</th>
                    <td th:text="${goodsInfo.goodsName}"></td>
                </tr>
                <tr>
                    <th>重量（KG） вес (кг)</th>
                    <td th:text="${goodsInfo.weight}"></td>
                    <th>体积（m³） объем (м³)</th>
                    <td th:text="${goodsInfo.volume}"></td>
                </tr>
                <tr>
                    <th>数量 количество</th>
                    <td th:text="${goodsInfo.quantity}"></td>
                    <th></th>
                    <td></td>
                </tr>
                <tr>
                    <th>创建者 Создатель</th>
                    <td th:text="${goodsInfo.createBy?.nickname}"></td>
                    <th>更新者 Обновивший</th>
                    <td th:text="${goodsInfo.updateBy?.nickname}"></td>
                </tr>
                <tr>
                    <th>创建时间 дата создания</th>
                    <td th:text="${#dates.format(goodsInfo.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <th>更新时间 Время обновления</th>
                    <td th:text="${#dates.format(goodsInfo.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </tr>
                <tr>
                    <th>备注 Примечание</th>
                    <td th:text="${goodsInfo.remark}" colspan="3"></td>
                </tr>
            </tbody>
        </table>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>
