<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .customer-autocomplete {
            position: relative;
        }

        .customer-info-row {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #666;
            margin-top: 2px;
        }

        .form-section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .form-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-left: 10px;
            border-left: 4px solid #009688;
        }

        .form-row {
            display: flex;
            flex-wrap: wrap;
        }

        .form-col {
            flex: 1;
            min-width: 300px;
            padding: 0 10px;
        }

        .layui-form-item {
            margin-bottom: 15px;
        }

        .form-buttons {
            text-align: center;
            padding: 20px 0;
        }

        .form-buttons .layui-btn {
            margin: 0 10px;
            padding: 0 30px;
        }

        .form-buttons .ajax-submit {
            background-color: #009688;
        }

        .form-buttons .close-popup {
            background-color: #777;
        }

        .readonly-field {
            background-color: #f5f5f5 !important;
            cursor: not-allowed;
        }

        .readonly-field:focus {
            background-color: #f5f5f5 !important;
        }

        .amount-field {
            text-align: right;
        }

        .field-error {
            color: #ff5722;
            font-size: 12px;
            margin-top: 2px;
        }

        .field-success {
            color: #4caf50;
        }
    </style>
</head>

<body>
    <div class="layui-form timo-compile">
        <form th:action="@{/psm/contract/save}">
            <input type="hidden" name="id" th:if="${contract}" th:value="${contract.id}">

            <!-- 基本信息 -->
            <div class="form-section">
                <!--            <div class="form-section-title">基本信息</div>-->
                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">单据号 код-данных</label>
                            <div class="layui-input-inline">
                                <input class="layui-input readonly-field" type="text" name="orderNo" id="orderNo"
                                    placeholder="系统自动生成" readonly th:value="${contract?.orderNo}">
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">销售合同编号 номер конт.(прод.)</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" type="text" name="contractNo" placeholder="请输入销售合同编号"
                                    th:value="${contract?.contractNo}"
                                    th:readonly="${contract?.contractNo != null && contract?.contractNo != ''}"
                                    th:classappend="${contract?.contractNo != null && contract?.contractNo != ''} ? 'readonly-field' : ''">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">外贸合同号 номер ВТК</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" type="text" name="foreignContractNo" placeholder="请输入外贸合同号"
                                    th:value="${contract?.foreignContractNo}"
                                    th:readonly="${contract?.foreignContractNo != null && contract?.foreignContractNo != ''}"
                                    th:classappend="${contract?.foreignContractNo != null && contract?.foreignContractNo != ''} ? 'readonly-field' : ''">
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">单据状态 статус</label>
                            <div class="layui-input-inline">
                                <select class="timo-search-select" name="contractStatus" mo:dict="CONTRACT_STATUS"
                                    mo-selected="${contract?.contractStatus}" mo-empty="全部"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 财务信息 -->
            <div class="form-section">
                <!--            <div class="form-section-title">财务信息</div>-->
                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">总应收款 сумма</label>
                            <div class="layui-input-inline layui-input-wrap">
                                <div class="layui-input-prefix">$</div>
                                <input class="layui-input amount-field" type="text" name="totalReceivable"
                                       id="totalReceivable" placeholder="请输入总应收款 " lay-affix="number" lay-precision="2" step="0.01" min="0" max="999999999"
                                       th:value="${contract?.totalReceivable}">
                            </div>
                            <div id="totalReceivableError" class="field-error" style="display:none;"></div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">产品售价 Пр.Ц</label>
                            <div class="layui-input-inline layui-input-wrap">
                                <div class="layui-input-prefix">$</div>
                                <input class="layui-input amount-field" type="text" name="productPrice"
                                       id="productPrice" placeholder="请输入产品售价 " lay-affix="number" lay-precision="2" min="0" max="999999999"
                                       th:value="${contract?.productPrice}">
                            </div>
                            <div id="productPriceError" class="field-error" style="display:none;"></div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">采购价 закуп.цена</label>
                            <div class="layui-input-inline layui-input-wrap">
                                <div class="layui-input-prefix">$</div>
                                <input class="layui-input amount-field" type="text" name="purchasePrice"
                                       id="purchasePrice" placeholder="请输入采购价 " lay-affix="number" lay-precision="2" min="0" max="999999999"
                                       th:value="${contract?.purchasePrice}">
                            </div>
                            <div id="purchasePriceError" class="field-error" style="display:none;"></div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">收款方 тип оплата</label>
                            <div class="layui-input-inline">
                                <select class="timo-search-select" name="payeeType" mo:dict="PAYEE_TYPE"
                                    mo-selected="${contract?.payeeType}" mo-empty="全部"></select>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">收款日期 дата оплата</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" type="text" id="collectionDate" name="collectionDate"
                                    placeholder="请选择收款日期"
                                    th:value="${#dates.format(contract?.collectionDate, 'yyyy-MM-dd')}">
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">采购付款日期 ДОЗ</label>
                            <div class="layui-input-inline">
                                <input class="layui-input" type="text" id="paymentDueDate" name="paymentDueDate"
                                    placeholder="请选择采购付款日期"
                                    th:value="${#dates.format(contract?.paymentDueDate, 'yyyy-MM-dd')}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 项目信息 -->
            <div class="form-section">
                <!--            <div class="form-section-title">项目信息</div>-->
                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目负责人（俄方） рук-ль (РФ)</label>
                            <div class="layui-input-inline customer-autocomplete">
                                <input class="layui-input" type="text" id="ruCustomerSearch"
                                    th:value="${contract?.ruCustomer?.contName}" placeholder="请输入俄方负责人进行搜索"
                                    autocomplete="off">
                                <input type="hidden" name="ruCustomer.id" th:value="${contract?.ruCustomer?.id}"
                                    id="ruCustomerId">
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">项目负责人（中方） рук-ль (КНР)</label>
                            <div class="layui-input-inline customer-autocomplete">
                                <input class="layui-input" type="text" id="cnCustomerSearch"
                                    th:value="${contract?.cnCustomer?.contName}" placeholder="请输入中方负责人进行搜索"
                                    autocomplete="off">
                                <input type="hidden" name="cnCustomer.id" th:value="${contract?.cnCustomer?.id}"
                                    id="cnCustomerId">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-col">
                        <div class="layui-form-item">
                            <label class="layui-form-label">业务员 менеджер</label>
                            <div class="layui-input-inline">
                                <input class="layui-input readonly-field" type="text"
                                    th:value="${contract?.salesMan?.nickname}" placeholder="当前登录用户" readonly>
                                <input type="hidden" name="salesMan.id" th:value="${contract?.salesMan?.id}"
                                    id="salesManId">
                            </div>
                        </div>
                    </div>
                    <div class="form-col">
                        <!-- 预留位置 -->
                    </div>
                </div>
            </div>


            <!-- 备注信息 -->
            <div class="form-section">
                <div class="form-section-title">备注信息 Примечание</div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">备注 Примечание</label>
                    <div class="layui-input-block">
                        <textarea placeholder="请输入内容" class="layui-textarea"
                            name="remark">[[${contract?.remark}]]</textarea>
                    </div>
                </div>
            </div>

            <div class="form-buttons">
                <button class="layui-btn ajax-submit"><i class="fa fa-check-circle"></i> 保存 держать</button>
                <button class="layui-btn close-popup"><i class="fa fa-times-circle"></i> 关闭 закрытие</button>
            </div>
        </form>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.config({
            base: '/js/'
        }).extend({
            autocomplete: 'autocomplete'
        });
        layui.use(['jquery', 'autocomplete', 'laydate', 'form'], function () {
            var laydate = layui.laydate;
            var $ = layui.jquery;
            var autocomplete = layui.autocomplete;
            var form = layui.form;

            function initDatePicker(elemId, label) {
                laydate.render({
                    elem: '#' + elemId,
                    format: 'yyyy-MM-dd',
                    position: 'fixed',
                    zIndex: 19999999,
                    trigger: 'click',
                    done: function (value) {
                        console.log(label + '日期: ' + value);
                    }
                });
            }

            ['collectionDate', 'paymentDueDate'].forEach(function (fieldId) {
                initDatePicker(fieldId, fieldId);
            });


            // 初始化俄方负责人autocomplete
            autocomplete.render({
                elem: '#ruCustomerSearch',
                hidden: '#ruCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected ru customer:', data);
                }
            });

            // 初始化中方负责人autocomplete
            autocomplete.render({
                elem: '#cnCustomerSearch',
                hidden: '#cnCustomerId',
                url: '/psm/customer/search',
                field: 'contName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.contName || '') + '-' + (item.custOrg || '') + '-' + (item.custDetail || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected cn customer:', data);
                }
            });

            // 初始化业务员autocomplete
            autocomplete.render({
                elem: '#salesManSearch',
                hidden: '#salesManId',
                url: '/system/user/search',
                field: 'username',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.username || '') + '-' + (item.nickname || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected sales man:', data);
                }
            });

            // 初始化运输信息autocomplete
            autocomplete.render({
                elem: '#transportSearch',
                hidden: '#transportId',
                url: '/psm/transport/search',
                field: 'orderNo',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.orderNo || '') + '-' + (item.transportType || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected transport:', data);
                }
            });

            // 初始化货物信息autocomplete
            autocomplete.render({
                elem: '#goodsInfoSearch',
                hidden: '#goodsInfoId',
                url: '/psm/goods/search',
                field: 'goodsName',
                valueField: 'id',
                template: function (item) {
                    var div = $('<div>');
                    div.append($('<div>').text((item.goodsName || '') + '-' + (item.goodsType || '')));
                    return div;
                },
                onSelect: function (data) {
                    console.log('Selected goods info:', data);
                }
            });


        });
    </script>
</body>

</html>
