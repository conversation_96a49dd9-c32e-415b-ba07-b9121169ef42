<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
<div class="timo-detail-page">
    <table class="layui-table timo-detail-table">
        <colgroup>
            <col width="130px"><col>
            <col width="130px"><col>
        </colgroup>
        <tbody>
        <tr>
            <th>主键ID</th>
            <td th:text="${foreignOrder.id}">ID</td>
            <th>业务员</th>
            <td th:text="${foreignOrder.operatorName}">业务员</td>
        </tr>

        <!-- 俄罗斯模块 -->
        <tr>
            <th>俄罗斯销售合同号</th>
            <td th:text="${foreignOrder.ruContractNo}">俄罗斯销售合同号</td>
            <th>俄罗斯售价</th>
            <td th:text="${foreignOrder.ruSalePrice}">俄罗斯售价</td>
        </tr>
        <tr>
            <th>俄罗斯收款方</th>
            <td th:text="${foreignOrder.ruPayee}">俄罗斯收款方</td>
            <th>俄罗斯收款日期</th>
            <td th:text="${#dates.format(foreignOrder.ruPaymentDate, 'yyyy-MM-dd HH:mm:ss')}">俄罗斯收款日期</td>
        </tr>

        <!-- 中俄贸易模块 -->
        <tr>
            <th>外贸合同号</th>
            <td th:text="${foreignOrder.cnRuContractNo}">外贸合同号</td>
            <th>采购价</th>
            <td th:text="${foreignOrder.purchasePrice}">采购价</td>
        </tr>
        <tr>
            <th>采购付款日期</th>
            <td th:text="${#dates.format(foreignOrder.purchasePaymentDate, 'yyyy-MM-dd HH:mm:ss')}">采购付款日期</td>
            <th>运输方式</th>
            <td th:text="${foreignOrder.transportMode}">运输方式</td>
        </tr>
        <tr>
            <th>运费</th>
            <td th:text="${foreignOrder.transportCost}">运费</td>
            <th>杂费</th>
            <td th:text="${foreignOrder.miscellaneousCost}">杂费</td>
        </tr>
        <tr>
            <th>报关备注</th>
            <td th:text="${foreignOrder.declarationRemark}" colspan="3">报关备注</td>
        </tr>

        <!-- 货物信息模块 -->
        <tr>
            <th>品名</th>
            <td th:text="${foreignOrder.productName}">品名</td>
            <th>重量(KG)</th>
            <td th:text="${foreignOrder.weight}">重量</td>
        </tr>
        <tr>
            <th>体积(M³)</th>
            <td th:text="${foreignOrder.volume}">体积</td>
            <th>数量</th>
            <td th:text="${foreignOrder.quantity}">数量</td>
        </tr>

        <!-- 中国内贸模块，仅内部用户可见 -->
        <tr th:if="${internalFlag}">
            <th>中国内贸合同号</th>
            <td th:text="${foreignOrder.cnDomesticContractNo}">中国内贸合同号</td>
            <th>中国内贸合同价</th>
            <td th:text="${foreignOrder.cnDomesticPrice}">中国内贸合同价</td>
        </tr>
        <tr th:if="${internalFlag}">
            <th>中国付款日期</th>
            <td th:text="${#dates.format(foreignOrder.cnPaymentDate, 'yyyy-MM-dd HH:mm:ss')}">中国付款日期</td>
            <th>中国付款方</th>
            <td th:text="${foreignOrder.cnPayer}">中国付款方</td>
        </tr>

        <!-- 系统信息 -->
        <tr>
            <th>创建时间</th>
            <td th:text="${#dates.format(foreignOrder.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
            <th>更新时间</th>
            <td th:text="${#dates.format(foreignOrder.updateDate, 'yyyy-MM-dd HH:mm:ss')}">更新时间</td>
        </tr>
        <tr>
            <th>创建人</th>
            <td th:text="${foreignOrder.createBy?.nickname}">创建人</td>
            <th>状态</th>
            <td th:text="${foreignOrder.status}">状态</td>
        </tr>
        </tbody>
    </table>
</div>

<script th:replace="/common/template :: script"></script>
</body>
</html>
