package com.linln.admin.foreignOrder.service.impl;

import com.linln.admin.express.repository.ExinfoRepository;

import com.linln.admin.foreignOrder.domain.ForeignOrder;
import com.linln.admin.foreignOrder.repository.ForeignOrderRepository;
import com.linln.admin.foreignOrder.service.ForeignOrderService;
import com.linln.common.data.PageSort;
import com.linln.common.enums.StatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/06/26
 */
@Service
public class ForeignOrderServiceImpl implements ForeignOrderService {

    @Autowired
    private ForeignOrderRepository foreignOrderRepository;

    @Override
    public Specification<ForeignOrder> buildSpecByExample(Example<ForeignOrder> example, Date ruPaymentStartDate, Date ruPaymentEndDate) {
        ForeignOrder probe = example.getProbe();

        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 俄罗斯销售合同号模糊查询
            if (probe.getRuContractNo() != null && !probe.getRuContractNo().isEmpty()) {
                predicates.add(cb.like(root.get("ruContractNo"), "%" + probe.getRuContractNo() + "%"));
            }

            // 外贸合同号模糊查询
            if (probe.getCnRuContractNo() != null && !probe.getCnRuContractNo().isEmpty()) {
                predicates.add(cb.like(root.get("cnRuContractNo"), "%" + probe.getCnRuContractNo() + "%"));
            }

            // 俄罗斯付款日期区间查询（数据库字段为DATE类型，直接比较）
            if (ruPaymentStartDate != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("ruPaymentDate"), ruPaymentStartDate));
            }
            if (ruPaymentEndDate != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("ruPaymentDate"), ruPaymentEndDate));
            }

            // 逻辑未删除
            predicates.add(cb.notEqual(root.get("status"), StatusEnum.DELETE.getCode()));

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }


    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    @Override
    public ForeignOrder getById(Long id) {
        return foreignOrderRepository.findById(id).orElse(null);
    }

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    @Override
    public Page<ForeignOrder> getPageList(Example<ForeignOrder> example) {
        // 创建分页对象
        PageRequest page = PageSort.pageRequest();
        return foreignOrderRepository.findAll(example, page);
    }

    /**
     * 保存数据
     * @param foreignOrder 实体对象
     */
    @Override
    public ForeignOrder save(ForeignOrder foreignOrder) {
        return foreignOrderRepository.save(foreignOrder);
    }

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(StatusEnum statusEnum, List<Long> idList) {
        return foreignOrderRepository.updateStatus(statusEnum.getCode(), idList) > 0;
    }

    /**
     * 分页查询 - 使用 Specification
     */
    @Override
    public Page<ForeignOrder> getPageListBySpec(Specification<ForeignOrder> spec) {
        PageRequest pageRequest = PageSort.pageRequest();
        return foreignOrderRepository.findAll(spec, pageRequest);
    }
}