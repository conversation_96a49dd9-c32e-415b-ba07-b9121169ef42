<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo"
      xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .dict-value {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 字典管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block timo-search-status">
                        <select class="timo-search-select" name="status" mo:dict="SEARCH_STATUS"
                                mo-selected="${param.status}"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">字典标识</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" th:value="${param.name}" placeholder="请输入字典标识" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">字典标题</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" th:value="${param.title}" placeholder="请输入字典标题"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="pull-right screen-btn-group">
                <button class="layui-btn open-popup" data-title="添加字典" th:attr="data-url=@{/system/dict/add}"
                        shiro:hasPermission="system:dict:add" data-size="615,558">
                    <i class="fa fa-plus"></i> 添加
                </button>
                <div class="btn-group" shiro:hasPermission="system:dict:status">
                    <button class="layui-btn">操作<span class="caret"></span></button>
                    <dl class="layui-nav-child layui-anim layui-anim-upbit">
                        <dd><a class="ajax-status" th:href="@{/system/dict/status/ok}">启用</a></dd>
                        <dd><a class="ajax-status" th:href="@{/system/dict/status/freezed}">冻结</a></dd>
                        <dd><a class="ajax-status" th:href="@{/system/dict/status/delete}">删除</a></dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th class="sortable" data-field="name">字典标识</th>
                    <th class="sortable" data-field="title">字典标题</th>
                    <th class="sortable" data-field="type">字典类型</th>
                    <th class="sortable" data-field="value">字典值</th>
                    <th class="sortable" data-field="createDate">创建时间</th>
                    <th class="sortable" data-field="updateDate">更新时间</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.name}">字典键名</td>
                    <td th:text="${item.title}">字典标题</td>
                    <td th:text="${#dicts.keyValue('DICT_TYPE', item.type)}">字典类型</td>
                    <td th:text="${item.value}" class="dict-value">字典键值</td>
                    <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                    <td th:text="${#dates.format(item.updateDate, 'yyyy-MM-dd HH:mm:ss')}">更新时间</td>
                    <td>
                        <a class="open-popup" data-title="编辑字典" th:attr="data-url=@{'/system/dict/edit/'+${item.id}}"
                           shiro:hasPermission="system:dict:edit"
                           data-size="615,558" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/system/dict/detail/'+${item.id}}"
                           shiro:hasPermission="system:dict:detail"
                           data-size="800,600" href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除" th:href="@{/system/dict/status/delete(ids=${item.id})}"
                           shiro:hasPermission="system:dict:status">删除</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
<script>
    layui.use(['element'], function () {
        var $ = layui.jquery;
        var dv = $(".dict-value");
        $(window).on("resize", function () {
            var width = $("body").width();
            if (width > 1200) {
                dv.css("max-width", width * 0.32);
            } else {
                dv.css("max-width", width * 0.20);
            }
        }).resize();
    });
</script>
</body>
</html>