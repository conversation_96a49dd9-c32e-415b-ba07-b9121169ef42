package com.linln.admin.psm.service;

import com.linln.admin.psm.domain.GoodsInfo;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface GoodsInfoService {

    /**
     * 获取分页列表数据
     *
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<GoodsInfo> getPageList(Example<GoodsInfo> example);

    /**
     * 根据ID查询数据
     *
     * @param id 主键ID
     */
    GoodsInfo getById(Long id);

    /**
     * 保存数据
     *
     * @param contract 实体对象
     */
    GoodsInfo save(GoodsInfo contract);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);

}
