<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:mo="https://gitee.com/aun/Timo">

<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>

<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-bars"></i> 货物信息管理管理</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
        </div>
        <div class="layui-card-body">
            <form class="layui-form timo-search-box" action="" id="searchForm">
                <div class="layui-form-pane">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">单据号 код-данных</label>
                                <div class="layui-input-block">
                                    <input type="text" name="orderNo" th:value="${param.orderNo}" placeholder="请输入单据号"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">货物编码 код товара</label>
                                <div class="layui-input-block">
                                    <input type="text" name="goodsNo" th:value="${param.goodsNo}" placeholder="请输入货物编码"
                                        autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md4">
                            <div class="layui-form-item">
                                <label class="layui-form-label">货物信息 инфо о грузе</label>
                                <div class="layui-input-block">
                                    <input type="text" name="goodsName" th:value="${param.goodsName}"
                                        placeholder="请输入货物信息" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-col-md12" style="text-align: center;">
                            <button type="button" class="layui-btn timo-search-btn">
                                <i class="fa fa-search"></i> 搜索 Поиск
                            </button>
                            <button type="button" class="layui-btn layui-btn-primary" id="resetBtn">
                                <i class="fa fa-refresh"></i> 重置 Сбросить
                            </button>
                            <button type="button" class="layui-btn open-popup" data-title="添加货物信息管理"
                                th:attr="data-url=@{/psm/goodsInfo/add}" data-size="auto">
                                <i class="fa fa-plus"></i> 添加 Добавить
                            </button>
                        </div>
                    </div>
                </div>
            </form>
            <div class="timo-table-wrap">
                <table class="layui-table timo-table">
                    <thead>
                        <tr>
                            <th class="timo-table-checkbox">
                                <label class="timo-checkbox"><input type="checkbox">
                                    <i class="layui-icon layui-icon-ok"></i></label>
                            </th>
                            <th>单据号 код-данных</th>
                            <th>货物信息 инфо о грузе</th>
                            <th>货物编码 код товара</th>
                            <th>重量（KG） вес (кг)</th>
                            <th>体积（m³） объем (м³)</th>
                            <th>数量 количество</th>
                            <th>操作 Операции</th>
                        </tr>
                    </thead>
                    <tbody th:with="items=${list == null ? T(java.util.Collections).emptyList() : (list instanceof T(java.lang.Iterable) ? list : T(java.util.Collections).singletonList(list))}">
                        <tr th:if="${#lists.isEmpty(items)}">
                            <td colspan="8" style="text-align: center;">暂无数据</td>
                        </tr>
                        <tr th:each="item:${items}">
                            <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                                    <i class="layui-icon layui-icon-ok"></i></label></td>
                            <td th:text="${item.orderNo}">单据号</td>
                            <td th:text="${item.goodsName}">货物信息</td>
                            <td th:text="${item.goodsNo}">货物编码</td>
                            <td th:text="${item.weight}">重量（KG）</td>
                            <td th:text="${item.volume}">体积（m³）</td>
                            <td th:text="${item.quantity}">数量</td>
                            <td>
                                <a class="open-popup" data-title="编辑货物信息管理"
                                    th:attr="data-url=@{'/psm/goodsInfo/edit/'+${item.id}}" data-size="auto"
                                    href="#">编辑</a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div th:replace="/common/fragment :: page"></div>
        </div>
    </div>
    <script th:replace="/common/template :: script"></script>
    <script>
        layui.use(['form'], function () {
            var form = layui.form;
            $('#resetBtn').on('click', function () {
                $("#searchForm input").val("");
                $(".timo-search-btn").click();
            });
        });
    </script>
</body>

</html>
