package com.linln.admin.express.service;

import com.linln.admin.express.domain.Exinfo;
import com.linln.admin.express.domain.ExpressSummaryDto;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/28
 */
public interface ExinfoService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<Exinfo> getPageList(Example<Exinfo> example);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    Exinfo getById(Long id);

    /**
     * 保存数据
     * @param exinfo 实体对象
     */
    Exinfo save(Exinfo exinfo);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);

    List<Exinfo> getListByExample(Example<Exinfo> example, Sort sort);

    Page<Exinfo> getPageListWithDateRange(Example<Exinfo> example);

    public Specification<Exinfo> buildSpecByExample(Example<Exinfo> example);

    public Page<Exinfo> getPageListBySpec(Specification<Exinfo> spec);

    public ExpressSummaryDto getSummaryBySpec(Specification<Exinfo> spec);
}