<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 余额调整管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block timo-search-status">
                        <select class="timo-search-select" name="status" mo:dict="SEARCH_STATUS" mo-selected="${param.status}"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">客户号</label>
                    <div class="layui-input-block">
                        <input type="text" name="userId" th:value="${param.userId}" placeholder="请输入客户号" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">客户名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="userName" th:value="${param.userName}" placeholder="请输入客户名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th>客户号</th>
                    <th>客户名称</th>
                    <th>余额</th>
                    <th>汇款总金额</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.userId}">客户号</td>
                    <td th:text="${item.userName}">客户名称</td>
                    <td th:text="${item.balance}">余额</td>
                    <td th:text="${item.historyAmount}">汇款总金额</td>
                    <td>
                        <a class="open-popup" data-title="余额调整" th:attr="data-url=@{'/acct/info/edit/'+${item.id}}" data-size="auto" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/acct/info/detail/'+${item.id}}" data-size="800,600" href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除" th:href="@{/acct/info/status/delete(ids=${item.id})}">删除</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>