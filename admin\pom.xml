<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>admin</artifactId>
    <packaging>jar</packaging>
    <name>后台管理模块</name>

    <parent>
        <groupId>com.linln</groupId>
        <artifactId>timo</artifactId>
        <version>2.0.3</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.linln</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--开发模块，上线部署可以注释掉-->
        <dependency>
            <groupId>com.linln</groupId>
            <artifactId>devtools</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.modules</groupId>
            <artifactId>system</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.component</groupId>
            <artifactId>shiro</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.component</groupId>
            <artifactId>actionLog</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.component</groupId>
            <artifactId>thymeleaf</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.component</groupId>
            <artifactId>fileUpload</artifactId>
            <version>${project.version}</version>
        </dependency>
        <!--hutool工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- JUnit 5 -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JUnit 4 for legacy tests -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <finalName>Timo-${project.version}</finalName>
    </build>
</project>
