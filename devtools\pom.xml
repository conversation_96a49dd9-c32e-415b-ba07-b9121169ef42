<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>devtools</artifactId>
    <packaging>jar</packaging>
    <name>开发中心模块</name>

    <parent>
        <groupId>com.linln</groupId>
        <artifactId>timo</artifactId>
        <version>2.0.3</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.linln</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.modules</groupId>
            <artifactId>system</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.linln.component</groupId>
            <artifactId>shiro</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <!-- 修复部分idea版本无法发布tpl文件到target目录下的问题 -->
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.tpl</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>