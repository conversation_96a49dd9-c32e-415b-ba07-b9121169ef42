package com.linln.admin.psm.service;

import com.linln.admin.psm.domain.Customer;
import com.linln.common.enums.StatusEnum;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
public interface CustomerService {

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @return 返回分页数据
     */
    Page<Customer> getPageList(Example<Customer> example);

    /**
     * 获取分页列表数据
     * @param spec 查询实例
     * @return 返回分页数据
     */
    Page<Customer> getPageList(Specification<Customer> spec);

    /**
     * 获取分页列表数据
     * @param example 查询实例
     * @param pageRequest 分页参数
     * @return 返回分页数据
     */
    Page<Customer> getPageList(Example<Customer> example, PageRequest pageRequest);

    /**
     * 根据ID查询数据
     * @param id 主键ID
     */
    Customer getById(Long id);

    /**
     * 保存数据
     * @param customer 实体对象
     */
    Customer save(Customer customer);

    /**
     * 状态(启用，冻结，删除)/批量状态处理
     */
    @Transactional(rollbackFor = Exception.class)
    Boolean updateStatus(StatusEnum statusEnum, List<Long> idList);
    
    /**
     * 根据关键字搜索客户（OR连接条件）
     * @param keyword 搜索关键字
     * @param pageRequest 分页参数
     * @return 客户列表
     */
    List<Customer> searchByKeyword(String keyword, PageRequest pageRequest);
}
