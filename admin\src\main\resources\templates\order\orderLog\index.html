<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
    <div class="layui-card">
        <div class="layui-card-header timo-card-header">
            <span><i class="fa fa-bars"></i> 订单管理管理</span>
            <i class="layui-icon layui-icon-refresh refresh-btn"></i>
            <a th:if="${bossFlag}" th:href="@{/order/orderLog/export}" ><i class="fa fa-download"></i></a>
        </div>
        <div class="layui-card-body">
            <div class="layui-row timo-card-screen">
                <div class="pull-left layui-form-pane timo-search-box">
                    <div class="layui-inline">
                        <label class="layui-form-label">状态</label>
                        <div class="layui-input-block timo-search-status">
                            <select class="timo-search-select" name="status" mo:dict="SEARCH_STATUS" mo-selected="${param.status}"></select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">订单号码</label>
                        <div class="layui-input-block">
                            <input type="text" name="orderNo" th:value="${param.orderNo}" placeholder="请输入订单号码" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <button class="layui-btn timo-search-btn">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="pull-right screen-btn-group">
                    <button class="layui-btn open-popup" data-title="添加订单管理" th:attr="data-url=@{/order/orderLog/add}" data-size="auto">
                        <i class="fa fa-plus"></i> 添加</button>
                    <div class="btn-group">
                        <button class="layui-btn">操作<span class="caret"></span></button>
                        <dl class="layui-nav-child layui-anim layui-anim-upbit">
                            <dd><a class="ajax-status" th:href="@{/order/orderLog/status/ok}">启用</a></dd>
                            <dd><a class="ajax-status" th:href="@{/order/orderLog/status/freezed}">冻结</a></dd>
                            <dd><a class="ajax-status" th:href="@{/order/orderLog/status/delete}">删除</a></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="timo-table-wrap">
                <table class="layui-table timo-table">
                    <thead>
                    <tr>
                        <th class="timo-table-checkbox">
                            <label class="timo-checkbox"><input type="checkbox">
                                <i class="layui-icon layui-icon-ok"></i></label>
                        </th>
                        <th>订单号码</th>
                        <th>日期 дата </th>
                        <th>客户 клиент</th>
                        <th>工号  USER CN</th>
                        <th>货物编码 код партия</th>
                        <th th:if="${internalFlag} or ${bossFlag}">订单金额 цена китай</th>
                        <th th:if="${internalFlag} or ${bossFlag}">采购费 закупок КЧ</th>
                        <th th:if="${internalFlag} or ${bossFlag}">国内支出 ПРР CN</th>
                        <th th:if="${internalFlag} or ${bossFlag}">国际运费 COC</th>
                        <th th:if="${internalFlag} or ${bossFlag}">备注 примечание</th>
                        <th th:if="${externalFlag} or ${bossFlag}">国外 USER RU</th>
                        <th th:if="${externalFlag} or ${bossFlag}">ПП 关税</th>
                        <th th:if="${externalFlag} or ${bossFlag}">ПП %</th>
                        <th th:if="${externalFlag} or ${bossFlag}">合同 ИТС</th>
                        <th th:if="${externalFlag} or ${bossFlag}">ИТС %</th>
                        <th th:if="${externalFlag} or ${bossFlag}">КЧ БН 服务</th>
                        <th th:if="${externalFlag} or ${bossFlag}">ПРР и авто</th>
                        <th th:if="${externalFlag} or ${bossFlag}">销售价 продажа</th>
                        <th th:if="${bossFlag}">说明 примечание</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr th:each="item:${list}">
                        <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                            <i class="layui-icon layui-icon-ok"></i></label></td>
                        <td th:text="${item.orderNo}">订单号码</td>
                        <td th:text="${item.markup}">日期 дата</td>
                        <td th:text="${item.customer}">客户 клиент</td>
                        <td th:text="${item.manager}">工号  USER CN</td>
                        <td th:text="${item.goodsNo}">货物编码 код партия</td>
                        <td th:if="${internalFlag} or ${bossFlag}" th:text="${item.internalOrdAmt}">订单金额 цена китай</td>
                        <td th:if="${internalFlag} or ${bossFlag}" th:text="${item.buyFees}">采购费 закупок КЧ</td>
                        <td th:if="${internalFlag} or ${bossFlag}" th:text="${item.internalFees}">国内支出 ПРР CN</td>
                        <td th:if="${internalFlag} or ${bossFlag}" th:text="${item.internationalFees}">国际运费 COC</td>
                        <td th:if="${internalFlag} or ${bossFlag}" th:text="${item.internalRemark}">备注 примечание </td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.userRuss}">国外 USER RU</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.taxFees}">ПП 关税</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.taxExtraFees}">ПП %</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.ctFees}">合同 ИТС</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.ctExtrFees}">ИТС %</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.russclearFees}">КЧ БН 服务</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.costAmt}">ПРР и авто</td>
                        <td th:if="${externalFlag} or ${bossFlag}" th:text="${item.salesAmt}">销售价 продажа</td>
                        <td th:if="${bossFlag}" th:text="${item.explaintt}">说明 примечание</td>
                        <td th:text="${#dates.format(item.updateDate, 'yyyy-MM-dd HH:mm:ss')}">更新时间</td>
                        <td>
                            <a class="open-popup" data-title="编辑订单管理" th:attr="data-url=@{'/order/orderLog/edit/'+${item.id}}" data-size="auto" href="#">编辑</a>
                            <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/order/orderLog/detail/'+${item.id}}" data-size="800,500" href="#">详细</a>
                            <a class="ajax-get" data-msg="您是否确认删除" th:href="@{/order/orderLog/status/delete(ids=${item.id})}">删除</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div th:replace="/common/fragment :: page"></div>
        </div>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>