package com.linln.admin.express.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

public class ExpressSummaryDto {
    //和Exinfo实体类中对应
    private BigDecimal recvAmount;           // 总应收款
    /*
    *未到账款的判断条件就是有没有填选收款时间，如果有时间代表款收了，如果是空值代表是需要统计出来的数值
    * */
    private BigDecimal recvUnpaid;          // 未到账款
    //国内应付 = Exinfo实体类中的 internalAmount
    private BigDecimal internalAmount;      // 国内应付
    //国外应付 = Exinfo实体类中的 outerAmount + outerClearAmount
    private BigDecimal outerAmountTotal;    // 国外应付

    public ExpressSummaryDto(String recvAmount, String recvUnpaid, String internalAmount, String outerAmountTotal) {
        this.recvAmount = new BigDecimal(recvAmount);
        this.recvUnpaid = new BigDecimal(recvUnpaid);
        this.internalAmount = new BigDecimal(internalAmount);
        this.outerAmountTotal = new BigDecimal(outerAmountTotal);
    }

    public ExpressSummaryDto() {
    }

    public BigDecimal getRecvAmount() {
        return recvAmount;
    }

    public void setRecvAmount(BigDecimal recvAmount) {
        this.recvAmount = recvAmount;
    }

    public BigDecimal getRecvUnpaid() {
        return recvUnpaid;
    }

    public void setRecvUnpaid(BigDecimal recvUnpaid) {
        this.recvUnpaid = recvUnpaid;
    }

    public BigDecimal getInternalAmount() {
        return internalAmount;
    }

    public void setInternalAmount(BigDecimal internalAmount) {
        this.internalAmount = internalAmount;
    }

    public BigDecimal getOuterAmountTotal() {
        return outerAmountTotal;
    }

    public void setOuterAmountTotal(BigDecimal outerAmountTotal) {
        this.outerAmountTotal = outerAmountTotal;
    }
}
