package com.linln.admin.psm.controller;

import cn.hutool.core.date.DateUtil;
import com.linln.admin.psm.domain.Customer;
import com.linln.admin.psm.service.CustomerService;
import com.linln.admin.psm.validator.CustomerValid;
import com.linln.common.enums.StatusEnum;
import com.linln.common.utils.EntityBeanUtil;
import com.linln.common.utils.ResultVoUtil;
import com.linln.common.utils.StatusUtil;
import com.linln.common.vo.ResultVo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/07/25
 */
@Controller
@RequestMapping("/psm/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    /**
     * 列表页面
     */
    @GetMapping("/index")
    @RequiresPermissions("psm:customer:index")
    public String index(Model model, Customer customer,
                        @RequestParam(value = "createDateStr", required = false) String createDateStr) {

        // 动态查询
        Specification<Customer> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(customer.getCustOrg())) {
                predicates.add(cb.like(root.get("custOrg"), "%" + customer.getCustOrg() + "%"));
            }

            if (StringUtils.hasText(customer.getContName())) {
                predicates.add(cb.like(root.get("contName"), "%" + customer.getContName() + "%"));
            }

            if (StringUtils.hasText(customer.getCustType())) {
                predicates.add(cb.equal(root.get("custType"), customer.getCustType()));
            }

            if (StringUtils.hasText(customer.getCustCountry())) {
                predicates.add(cb.equal(root.get("custCountry"), customer.getCustCountry()));
            }

            if (StringUtils.hasText(createDateStr)) {
                String[] dateRange = createDateStr.split(" - ");
                Date startDate = DateUtil.parse(dateRange[0]);
                Date endDate = DateUtil.parse(dateRange[1]);
                predicates.add(cb.between(root.get("createDate"), startDate, DateUtil.endOfDay(endDate)));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<Customer> list = customerService.getPageList(spec);

        // 封装数据
        model.addAttribute("list", list.getContent());
        model.addAttribute("page", list);
        return "/psm/customer/index";
    }

    /**
     * 跳转到添加页面
     */
    @GetMapping("/add")
    @RequiresPermissions("psm:customer:add")
    public String toAdd() {
        return "/psm/customer/add";
    }

    /**
     * 跳转到编辑页面
     */
    @GetMapping("/edit/{id}")
    @RequiresPermissions("psm:customer:edit")
    public String toEdit(@PathVariable("id") Customer customer, Model model) {
        model.addAttribute("customer", customer);
        return "/psm/customer/add";
    }

    /**
     * 保存添加/修改的数据
     * @param valid 验证对象
     */
    @PostMapping("/save")
    @RequiresPermissions({"psm:customer:add", "psm:customer:edit"})
    @ResponseBody
    public ResultVo save(@Validated CustomerValid valid, Customer customer) {
        // 复制保留无需修改的数据
        if (customer.getId() != null) {
            Customer beCustomer = customerService.getById(customer.getId());
            EntityBeanUtil.copyProperties(beCustomer, customer);
        }

        // 保存数据
        customerService.save(customer);
        return ResultVoUtil.SAVE_SUCCESS;
    }

    /**
     * 跳转到详细页面
     */
    @GetMapping("/detail/{id}")
    @RequiresPermissions("psm:customer:detail")
    public String toDetail(@PathVariable("id") Customer customer, Model model) {
        model.addAttribute("customer",customer);
        return "/psm/customer/detail";
    }

    /**
     * 设置一条或者多条数据的状态
     */
    @RequestMapping("/status/{param}")
    @RequiresPermissions("psm:customer:status")
    @ResponseBody
    public ResultVo status(
            @PathVariable("param") String param,
            @RequestParam(value = "ids", required = false) List<Long> ids) {
        // 更新状态
        StatusEnum statusEnum = StatusUtil.getStatusEnum(param);
        if (customerService.updateStatus(statusEnum, ids)) {
            return ResultVoUtil.success(statusEnum.getMessage() + "成功");
        } else {
            return ResultVoUtil.error(statusEnum.getMessage() + "失败，请重新操作");
        }
    }
    
    /**
     * 搜索客户信息，用于采购合同页面的autocomplete功能
     */
    @GetMapping("/search")
    @ResponseBody
    public ResultVo search(@RequestParam("keyword") String keyword) {
        // 使用自定义JPQL查询实现OR连接的模糊搜索
        List<Customer> customers = customerService.searchByKeyword(keyword, PageRequest.of(0, 10));
        return ResultVoUtil.success(customers);
    }
}
