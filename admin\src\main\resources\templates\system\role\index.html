<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo"
      xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 角色管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>
    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block timo-search-status">
                        <select class="timo-search-select" name="status" mo:dict="SEARCH_STATUS"
                                mo-selected="${param.status}"></select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" th:value="${param.name}" placeholder="请输入角色编号" autocomplete="off"
                               class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" th:value="${param.title}" placeholder="请输入角色名称"
                               autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
            <div class="pull-right screen-btn-group">
                <button class="layui-btn open-popup" data-title="添加角色" th:attr="data-url=@{/system/role/add}"
                        shiro:hasPermission="system:role:add" data-size="460,357">
                    <i class="fa fa-plus"></i> 添加
                </button>
                <button class="layui-btn open-popup-param" data-type="radio" data-title="授权管理"
                        th:attr="data-url=@{/system/role/auth}" shiro:hasPermission="system:role:auth"
                        data-size="600,500">
                    <i class="fa fa-user-secret"></i> 授权
                </button>
                <div class="btn-group" shiro:hasPermission="system:role:status">
                    <button class="layui-btn">操作<span class="caret"></span></button>
                    <dl class="layui-nav-child layui-anim layui-anim-upbit">
                        <dd><a class="ajax-status" th:href="@{/system/role/status/ok}">启用</a></dd>
                        <dd><a class="ajax-status" th:href="@{/system/role/status/freezed}">冻结</a></dd>
                        <dd><a class="ajax-status" th:href="@{/system/role/status/delete}">删除</a></dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox">
                            <i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th class="sortable" data-field="name">角色编号</th>
                    <th class="sortable" data-field="title">角色名称</th>
                    <th class="sortable" data-field="createDate">创建时间</th>
                    <th class="sortable" data-field="updateDate">更新时间</th>
                    <th>查看</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}">
                        <i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.name}">角色编号</td>
                    <td th:text="${item.title}">角色名称</td>
                    <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                    <td th:text="${#dates.format(item.updateDate, 'yyyy-MM-dd HH:mm:ss')}">更新时间</td>
                    <td>
                        <a class="open-popup" data-title="用户列表" shiro:hasPermission="system:role:userList"
                           th:attr="data-url=@{'/system/role/userList/'+${item.id}}" data-size="800,600"
                           href="#">用户列表</a>
                    </td>
                    <td>
                        <a class="open-popup" data-title="编辑角色" th:attr="data-url=@{'/system/role/edit/'+${item.id}}"
                           shiro:hasPermission="system:role:edit"
                           data-size="460,357" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/system/role/detail/'+${item.id}}"
                           shiro:hasPermission="system:role:detail"
                           data-size="800,600" href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除" th:href="@{/system/role/status/delete(ids=${item.id})}"
                           shiro:hasPermission="system:role:status">删除</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>
<script th:replace="/common/template :: script"></script>
</body>
</html>