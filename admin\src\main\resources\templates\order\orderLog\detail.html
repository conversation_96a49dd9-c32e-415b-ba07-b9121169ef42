<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
</head>
<body>
    <div class="timo-detail-page">
        <div class="timo-detail-title">基本信息</div>
        <table class="layui-table timo-detail-table">
            <colgroup>
                <col width="100px"><col>
                <col width="100px"><col>
            </colgroup>
            <tbody>
                <tr>
                    <th>订单号码</th>
                    <td th:text="${orderLog.orderNo}"></td>
                    <th>日期 дата </th>
                    <td th:text="${orderLog.markup}"></td>
                </tr>
                <tr>
                    <th>客户 клиент</th>
                    <td th:text="${orderLog.customer}"></td>
                    <th>工号  USER CN</th>
                    <td th:text="${orderLog.manager}"></td>
                </tr>
                <tr>
                    <th>货物编码 код партия</th>
                    <td th:text="${orderLog.goodsNo}"></td>
                    <th th:if="${internalFlag} or ${bossFlag}">订单金额 цена китай</th>
                    <td th:if="${internalFlag} or ${bossFlag}" th:text="${orderLog.internalOrdAmt}"></td>
                </tr>
                <tr  th:if="${internalFlag} or ${bossFlag}">
                    <th>采购费 закупок КЧ</th>
                    <td th:text="${orderLog.buyFees}"></td>
                    <th>国内支出 ПРР CN</th>
                    <td th:text="${orderLog.internalFees}"></td>
                </tr>
                <tr  th:if="${internalFlag} or ${bossFlag}">
                    <th>国际运费 COC</th>
                    <td th:text="${orderLog.internationalFees}"></td>
                    <th>备注 примечание </th>
                    <td th:text="${orderLog.internalRemark}"></td>
                </tr>
                <tr th:if="${externalFlag} or ${bossFlag}" >
                    <th >国外 USER RU</th>
                    <td th:text="${orderLog.userRuss}"></td>
                    <th >ПП 关税</th>
                    <td th:text="${orderLog.taxFees}"></td>
                </tr>
                <tr th:if="${externalFlag} or ${bossFlag}" >
                    <th>ПП %</th>
                    <td th:text="${orderLog.taxExtraFees}"></td>
                    <th>合同 ИТС</th>
                    <td th:text="${orderLog.ctFees}"></td>
                </tr>
                <tr th:if="${externalFlag} or ${bossFlag}" >
                    <th>ИТС %</th>
                    <td th:text="${orderLog.ctExtrFees}"></td>
                    <th>КЧ БН 服务 </th>
                    <td th:text="${orderLog.russclearFees}"></td>
                </tr>
                <tr th:if="${externalFlag} or ${bossFlag}" >
                    <th>ПРР и авто </th>
                    <td th:text="${orderLog.costAmt}"></td>
                    <th>销售价 продажа </th>
                    <td th:text="${orderLog.salesAmt}"></td>
                </tr>
                <tr th:if="${bossFlag}" >
                    <th>说明 примечание</th>
                    <td th:text="${orderLog.explaintt}"></td>
                </tr>
                <tr>
                    <th>创建者</th>
                    <td th:text="${orderLog.createBy?.nickname}"></td>
                    <th>更新者</th>
                    <td th:text="${orderLog.updateBy?.nickname}"></td>
                </tr>
                <tr>
                    <th>创建时间</th>
                    <td th:text="${#dates.format(orderLog.createDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                    <th>更新时间</th>
                    <td th:text="${#dates.format(orderLog.updateDate, 'yyyy-MM-dd HH:mm:ss')}"></td>
                </tr>
            </tbody>
        </table>
    </div>
<script th:replace="/common/template :: script"></script>
</body>
</html>