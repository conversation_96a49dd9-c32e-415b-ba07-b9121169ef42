### spring配置
spring:
  ## 数据库配置
  datasource:
    driver-class-name: com.mysql.jdbc.Driver
    url: *********************************************************************
    username: timo
    password: BXHchZPBHCMm5ch4
    # 添加连接池配置
    hikari:
      maximum-pool-size: 20           # 最大连接数
      minimum-idle: 5                 # 最小空闲连接数
      connection-timeout: 30000       # 连接超时时间(毫秒)
      idle-timeout: 600000            # 空闲连接超时时间(毫秒)
      max-lifetime: 1800000           # 连接最大存活时间(毫秒)
      leak-detection-threshold: 60000 # 连接泄漏检测阈值(毫秒)
