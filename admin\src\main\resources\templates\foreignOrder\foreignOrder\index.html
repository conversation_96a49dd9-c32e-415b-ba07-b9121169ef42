<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:mo="https://gitee.com/aun/Timo">
<head th:replace="/common/template :: header(~{::title},~{::link},~{::style})">
    <style>
        .layui-inline {
            margin-right: 10px !important;
            display: flex;
            align-items: center;
        }

        .timo-search-box {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }

        .date-range-group {
            display: flex;
            align-items: center;
        }

        .date-range-group .layui-form-label {
            width: auto;
            padding-right: 10px;
        }

        .date-range-group .layui-input-inline {
            width: 140px;
        }

        .date-range-group .layui-form-mid {
            padding: 0 6px;
            color: #666;
            white-space: nowrap;
        }
        .timo-search-box {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }
        .btn-group-inline {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    </style>


</head>
<body class="timo-layout-page">
<div class="layui-card">
    <div class="layui-card-header timo-card-header">
        <span><i class="fa fa-bars"></i> 外贸订单管理</span>
        <i class="layui-icon layui-icon-refresh refresh-btn"></i>
    </div>

    <div class="layui-card-body">
        <div class="layui-row timo-card-screen">
            <div class="pull-left layui-form-pane timo-search-box">
                <div class="layui-inline">
                    <label class="layui-form-label">俄罗斯销售合同号</label>
                    <div class="layui-input-block">
                        <input type="text" name="ruContractNo" th:value="${param.ruContractNo}" placeholder="请输入俄罗斯销售合同号" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">外贸合同号</label>
                    <div class="layui-input-block">
                        <input type="text" name="cnRuContractNo" th:value="${param.cnRuContractNo}" placeholder="请输入外贸合同号" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline date-range-group">
                    <label class="layui-form-label">俄收款日期</label>
                    <div class="layui-input-inline">
                        <input type="text" id="ruPaymentStartDate" name="ruPaymentStartDate" th:value="${param.ruPaymentStartDate}" class="layui-input" placeholder="起始日期">
                    </div>
                    <div class="layui-form-mid">至</div>
                    <div class="layui-input-inline">
                        <input type="text" id="ruPaymentEndDate" name="ruPaymentEndDate" th:value="${param.ruPaymentEndDate}" class="layui-input" placeholder="结束日期">
                    </div>
                </div>

                <div class="layui-inline btn-group-inline">
                    <button class="layui-btn timo-search-btn">
                        <i class="fa fa-search"></i> 搜索
                    </button>
                    <button class="layui-btn layui-btn-normal" id="resetBtn">
                        <i class="fa fa-refresh"></i> 重置
                    </button>
                </div>
            </div>

            <div class="pull-right screen-btn-group">
                <button class="layui-btn open-popup" data-title="添加外贸订单" th:attr="data-url=@{/foreignOrder/foreignOrder/add}" data-size="auto">
                    <i class="fa fa-plus"></i> 添加</button>
                <div class="btn-group">
                    <button class="layui-btn">操作<span class="caret"></span></button>
                    <dl class="layui-nav-child layui-anim layui-anim-upbit">
                        <dd><a class="ajax-status" th:href="@{/foreignOrder/foreignOrder/status/ok}">启用</a></dd>
                        <dd><a class="ajax-status" th:href="@{/foreignOrder/foreignOrder/status/freezed}">冻结</a></dd>
                        <dd><a class="ajax-status" th:href="@{/foreignOrder/foreignOrder/status/delete}">删除</a></dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="timo-table-wrap">
            <table class="layui-table timo-table">
                <thead>
                <tr>
                    <th class="timo-table-checkbox">
                        <label class="timo-checkbox"><input type="checkbox"><i class="layui-icon layui-icon-ok"></i></label>
                    </th>
                    <th>业务员</th>
                    <!-- 俄罗斯模块 -->
                    <th>俄罗斯销售合同号</th>
                    <th>俄罗斯售价</th>
                    <th>俄罗斯收款方</th>
                    <th>俄罗斯收款日期</th>

                    <!-- 中俄之间模块 -->
                    <th>外贸合同号</th>
                    <th>采购价</th>
                    <th>采购付款日期</th>

                    <!-- 报关模块 -->
                    <th>运输方式</th>
                    <th>运费</th>
                    <th>杂费</th>

                    <!-- 货物信息模块 -->
                    <th>品名</th>
                    <th>重量(KG)</th>
                    <th>体积(M³)</th>
                    <th>数量</th>

                    <!-- 中国模块，仅内部人员可见 -->
                    <th th:if="${internalFlag}">中国内贸合同号</th>
                    <th th:if="${internalFlag}">中国内贸合同价</th>
                    <th th:if="${internalFlag}">中国付款日期</th>
                    <th th:if="${internalFlag}">中国付款方</th>

                    <th>创建时间</th>
                    <th>更新时间</th>
                    <th>操作</th>
                </tr>
                </thead>

                <tbody>
                <tr th:each="item:${list}">
                    <td><label class="timo-checkbox"><input type="checkbox" th:value="${item.id}"><i class="layui-icon layui-icon-ok"></i></label></td>
                    <td th:text="${item.operatorName}">业务员</td>
                    <!-- 俄罗斯模块 -->
                    <td th:text="${item.ruContractNo}">俄罗斯销售合同号</td>
                    <td th:text="${item.ruSalePrice}">俄罗斯售价</td>
                    <td th:text="${item.ruPayee}">俄罗斯收款方</td>
                    <td th:text="${#dates.format(item.ruPaymentDate, 'yyyy-MM-dd')}">俄罗斯收款日期</td>

                    <!-- 中俄之间模块 -->
                    <td th:text="${item.cnRuContractNo}">外贸合同号</td>
                    <td th:text="${item.purchasePrice}">采购价</td>
                    <td th:text="${#dates.format(item.purchasePaymentDate, 'yyyy-MM-dd')}">采购付款日期</td>

                    <!-- 报关模块 -->
                    <td th:text="${item.transportMode}">运输方式</td>
                    <td th:text="${item.transportCost}">运费</td>
                    <td th:text="${item.miscellaneousCost}">杂费</td>

                    <!-- 货物信息模块 -->
                    <td th:text="${item.productName}">品名</td>
                    <td th:text="${item.weight}">重量</td>
                    <td th:text="${item.volume}">体积</td>
                    <td th:text="${item.quantity}">数量</td>

                    <!-- 中国模块，仅内部人员可见 -->
                    <td th:if="${internalFlag}" th:text="${item.cnDomesticContractNo}">中国内贸合同号</td>
                    <td th:if="${internalFlag}" th:text="${item.cnDomesticPrice}">中国内贸合同价</td>
                    <td th:if="${internalFlag}" th:text="${#dates.format(item.cnPaymentDate, 'yyyy-MM-dd')}">中国付款日期</td>
                    <td th:if="${internalFlag}" th:text="${item.cnPayer}">中国付款方</td>

                    <td th:text="${#dates.format(item.createDate, 'yyyy-MM-dd HH:mm:ss')}">创建时间</td>
                    <td th:text="${#dates.format(item.updateDate, 'yyyy-MM-dd HH:mm:ss')}">更新时间</td>
                    <td>
                        <a class="open-popup" data-title="编辑外贸订单" th:attr="data-url=@{'/foreignOrder/foreignOrder/edit/'+${item.id}}" data-size="auto" href="#">编辑</a>
                        <a class="open-popup" data-title="详细信息" th:attr="data-url=@{'/foreignOrder/foreignOrder/detail/'+${item.id}}" data-size="800,500" href="#">详细</a>
                        <a class="ajax-get" data-msg="您是否确认删除" th:href="@{/foreignOrder/foreignOrder/status/delete(ids=${item.id})}">删除</a>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>

        <div th:replace="/common/fragment :: page"></div>
    </div>
</div>

<script th:replace="/common/template :: script"></script>
<script>
    layui.use('laydate', function(){
        var laydate = layui.laydate;

        // 俄罗斯付款日期区间
        laydate.render({
            elem: '#ruPaymentStartDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
        laydate.render({
            elem: '#ruPaymentEndDate',
            format: 'yyyy-MM-dd',
            position: 'fixed'
        });
        // 重置按钮逻辑
        document.getElementById('resetBtn').addEventListener('click', function(){
            window.location.href = window.location.pathname;
        });
    });
</script>
</body>
</html>